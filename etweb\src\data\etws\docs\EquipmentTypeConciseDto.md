# EquipmentTypeConciseDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**name** | **string** |  | [optional] [default to undefined]
**sapMaterialNum** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { EquipmentTypeConciseDto } from './api';

const instance: EquipmentTypeConciseDto = {
    id,
    name,
    sapMaterialNum,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
