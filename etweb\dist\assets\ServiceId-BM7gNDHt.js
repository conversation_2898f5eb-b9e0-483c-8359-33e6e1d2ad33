import{v as A,G as y,r as o,w as F,j as e,L as k,I as M,d as P,y as S,H as b,E as x}from"./index-CFpwFZya.js";const H=()=>{var p,I;const{t:s}=A(),{mutate:v,isPending:t}=y(),[h,r]=o.useState(null),[d,a]=o.useState(null),{register:C,watch:V,formState:{errors:f}}=F(),l=V("serviceId"),g=o.useCallback(c=>{if(!c||c.trim()===""){r(null),a(null);return}r(null),a(null),v(c.trim(),{onSuccess:n=>{n.success===!0?(r(null),a(!0)):(r("invalidServiceId"),a(!1))},onError:n=>{var E,w;const u=n,T=((w=(E=u==null?void 0:u.response)==null?void 0:E.data)==null?void 0:w.message)||n.message||"validationError";r(T),a(!1)}})},[v]);o.useEffect(()=>{const c=setTimeout(()=>{g(l)},500);return()=>clearTimeout(c)},[l,g]);const m=!!f.serviceId,j=!!h,i=m||j,L=(I=(p=f.serviceId)==null?void 0:p.message)==null?void 0:I.toString(),N=l&&l.trim()!=="";return e.jsxs("div",{className:"space-y-2",children:[e.jsx(k,{htmlFor:"serviceId",children:s("enterTheService")}),e.jsxs("div",{className:"relative",children:[e.jsx(M,{id:"serviceId",type:"text",...C("serviceId"),placeholder:"TV.123, LN.123, LN-P.123",className:P(i&&"border-destructive focus-visible:ring-destructive/20",d&&!i&&"border-green-500 focus-visible:ring-green-500/20")}),N&&e.jsxs("div",{className:"absolute right-3 top-1/2 -translate-y-1/2",children:[t&&e.jsx(S,{className:"h-4 w-4 animate-spin text-muted-foreground"}),!t&&d&&!i&&e.jsx(b,{className:"h-4 w-4 text-green-600"}),!t&&i&&e.jsx(x,{className:"h-4 w-4 text-destructive"})]})]}),m&&e.jsxs("div",{className:"flex items-center text-sm text-destructive",children:[e.jsx(x,{className:"mr-1.5 h-4 w-4"}),s(L||"")]}),j&&!m&&e.jsxs("div",{className:"flex items-center text-sm text-destructive",children:[e.jsx(x,{className:"mr-1.5 h-4 w-4"}),s(h||"")]}),!t&&d&&!i&&N&&e.jsxs("div",{className:"flex items-center text-sm text-green-600",children:[e.jsx(b,{className:"mr-1.5 h-4 w-4"}),s("serviceIdValidated")]}),t&&e.jsxs("div",{className:"flex items-center text-sm text-muted-foreground",children:[e.jsx(S,{className:"mr-1.5 h-4 w-4 animate-spin"}),s("validating")]})]})};export{H as default};
