﻿namespace EtDb.Models.Requests
{
    public class CitiesRequestModel
    {
        [Required(ErrorMessage = "NameRequired")]
        [StringLength(50, ErrorMessage = "NameValidate")]
        public string Name { get; set; }

        [Required(ErrorMessage = "RegionRequired")]
        public int Region { get; set; }

        public string SAPCityCode { get; set; }

        public string Cluster { get; set; }
    }
}
