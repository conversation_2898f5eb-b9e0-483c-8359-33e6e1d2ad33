﻿namespace EtWS.Services.SubstitutionsService
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.SubstitutionModels;

    public interface ISubstitutionsService : IService
    {
        Task<SubstitutionsResponseModel> GetSubstitutionByIdAsync(int id);

        Task<SearchResponseModel<SubstitutionsResponseModel>> GetSubstitutionsAsync(SearchDataRequestModel request, bool isCurrentUserMol);

        Task AddSubstitutionAsync(SubstitutionDataRequestModel request);

        Task UpdateSubstitutionAsync(SubstitutionDataRequestModel request);

        Task DeleteSubstitutionAsync(int id);

        SubstitutionResponseModel GetActiveSubstitution(string substituteUserId);

        Task UpdateForUserIdWhenMolChanges(EditUserRequest request);
    }
}
