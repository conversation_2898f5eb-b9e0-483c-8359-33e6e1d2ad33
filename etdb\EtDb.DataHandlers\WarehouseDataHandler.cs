﻿namespace EtDb.DataHandlers
{
    using System.Linq;
    using EtDb.ApiClients.EtDb.Context;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class WarehouseDataHandler : BaseDataHandler, IWarehouseDataHandler
    {
        public WarehouseDataHandler(Lazy<EtDbContext> dbContext)
            : base(dbContext)
        {
        }

        public IQueryable<Warehouses> GetAllWarehouses() => this.dbContext.Value.Warehouses;

        public async Task<IEnumerable<Warehouses>> GetWarehousesByRegionAsync(int? region)
        {
            var query = this.GetAllWarehouses();

            if (region.HasValue)
            {
                query = query
                    .Include(w => w.<PERSON><PERSON><PERSON>)
                    .ThenInclude(s => s.City)
                    .Where(w => w.Schenkers.Any(s => s.City.Region == region));
            }

            return await query.ToListAsync();
        }
    }
}
