﻿using EtDb.ApiClients.EtDb.Enums;
using System;

namespace EtDb.ApiClients.EtDb.Models
{
    public partial class StatusHistory
    {
        public static StatusHistory CreateEntry(
            int historyId,
            string userId,
            DocStatus docStatusOld,
            DocStatus docStatusNew,
            string sourceSystem)
        {
            return new StatusHistory
            {
                HistoryId = historyId,
                DocStatusOld = (int)docStatusOld,
                DocStatusNew = (int)docStatusNew,
                SourceSystem = sourceSystem,
                UserId = userId,
                DateInsert = DateTime.Now
            };
        }
    }
}
