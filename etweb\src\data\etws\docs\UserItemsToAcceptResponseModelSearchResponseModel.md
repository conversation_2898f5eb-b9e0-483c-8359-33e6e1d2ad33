# UserItemsToAcceptResponseModelSearchResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**count** | **number** |  | [optional] [default to undefined]
**dataCollection** | [**Array&lt;UserItemsToAcceptResponseModel&gt;**](UserItemsToAcceptResponseModel.md) |  | [optional] [default to undefined]

## Example

```typescript
import { UserItemsToAcceptResponseModelSearchResponseModel } from './api';

const instance: UserItemsToAcceptResponseModelSearchResponseModel = {
    count,
    dataCollection,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
