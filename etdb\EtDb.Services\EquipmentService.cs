﻿namespace EtDb.Services
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Enums;
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using EtDb.Infrastructure.Constants;
    using EtDb.Models.Requests;
    using EtDb.Models.Requests.EquipmentModels;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.EquipmentModels;
    using EtDb.Models.Responses.UserModels;
    using EtDb.Services.Interfaces;

    public class EquipmentService : IEquipmentService
    {
        private readonly Lazy<IEquipmentDataHandler> equipmentDataHandler;
        private readonly Lazy<IMapper> mapper;

        public EquipmentService(Lazy<IEquipmentDataHandler> equipmentDataHandler, Lazy<IMapper> mapper)
        {
            this.equipmentDataHandler = equipmentDataHandler;
            this.mapper = mapper;
        }

        public async Task<List<SapmatMapp>> GetAllSapmatMappAsync()
        {
            return await this.equipmentDataHandler.Value.GetSAPMatMappAsync();
        }

        public async Task<SearchResponseModel<AvailableEquipmentResponseModel>> SearchAvailableEquipmentAsync(SearchRequestModelWithUserId request)
        {
            var data = await this.equipmentDataHandler.Value.GetFilteredUserAvailableItemsAsync(request.UserId, request.SortBy, request.SortDir, request.PageNumber, request.PageSize, request.Query);

            return new SearchResponseModel<AvailableEquipmentResponseModel>
            {
                Count = data.AllDataRowsCount,
                DataCollection = data.DataRows
                    .AsEnumerable()
                    .Select(equipment =>
                        this.mapper.Value.Map<AvailableEquipmentResponseModel>(equipment, opts =>
                        {
                            opts.Items[AutomapperConstants.UserAdAccount] = equipment.User.Adaccount;
                        })),
            };
        }

        public async Task<ReserveItemHistoryResponseModel> ReserveItemForTransferAsync(int itemId, string userId, int quantity)
        {
            var reservationEntry = await this.equipmentDataHandler.Value.ReserveItemAsync(itemId, userId, quantity);
            return this.mapper.Value.Map<ReserveItemHistoryResponseModel>(reservationEntry);
        }

        public async Task<ReserveItemHistoryResponseModel> ReserveItemForTransferAsync(string itemSerialNumber, string userId, int quantity)
        {
            var reservationEntry = await this.equipmentDataHandler.Value.ReserveItemAsync(itemSerialNumber, userId, quantity);
            return this.mapper.Value.Map<ReserveItemHistoryResponseModel>(reservationEntry);
        }

        public async Task<int> CountAllUserReservedItemsForTransfer(string userId)
        {
            var response = await this.equipmentDataHandler.Value.GetAllUserReservedItemsForTransferAsync(userId);
            return response.Count();
        }

        public async Task<IEnumerable<AvailableEquipmentResponseModel>> GetAllUserReservedItemsForTransferAsync(string userId)
        {
            var response = await this.equipmentDataHandler.Value.GetAllUserReservedItemsForTransferAsync(userId);

            return response
                    .AsEnumerable()
                    .Select(equipment =>
                        this.mapper.Value.Map<AvailableEquipmentResponseModel>(equipment, opts =>
                        {
                            opts.Items[AutomapperConstants.UserAdAccount] = equipment.User.Adaccount;
                        }));
        }

        public async Task ReserveAllItemsForTransferAsync(string userId)
        {
            await this.equipmentDataHandler.Value.ReserveAllItems(userId);
        }

        public async Task<string> RemoveItemAsync(int itemId, string userId)
        {
            return await this.equipmentDataHandler.Value.RemoveItemAsync(itemId, userId);
        }

        public async Task RemoveItemsAsync(IEnumerable<int> selectedItemIds, string userId)
        {
            await this.equipmentDataHandler.Value.RemoveItemsAsync(selectedItemIds, userId);
        }

        public async Task RemoveAllItemsAsync(string userId)
        {
            await this.equipmentDataHandler.Value.RemoveAllItemsAsync(userId);
        }

        public async Task UpdateAvailableEquipmentsAsync(IList<AvailableEquipmentRequestModel> availableEquipments)
        {
            var mappedAvailableEquipments = this.mapper.Value.Map<IList<AvailableEquipment>>(availableEquipments);
            await this.equipmentDataHandler.Value.UpdateAvailableEquipmentsAsync(mappedAvailableEquipments);
        }

        public async Task<SearchResponseModel<ItemResponseModel>> SearchTransferDataAsync(SearchRequestModelWithUserId request)
        {
            var data = await this.equipmentDataHandler.Value.GetFilteredUserReservedItemsForTransferAsync(request.UserId, request.SortBy, request.SortDir, request.PageNumber, request.PageSize, request.Query);

            return this.mapper.Value.Map<FilteredDataModel<AvailableEquipment>, SearchResponseModel<ItemResponseModel>>(data);
        }

        public async Task<SearchResponseModel<AvailableEquipmentResponseModel>> SearchDailyEquipmentAsync(SearchRequestModelWithUserId request)
        {
            var data = await this.equipmentDataHandler.Value.GetFilteredUserDailyItemsAsync(request.UserId, request.SortBy, request.SortDir, request.PageNumber, request.PageSize, request.Query);

            return new SearchResponseModel<AvailableEquipmentResponseModel>
            {
                Count = data.AllDataRowsCount,
                DataCollection = data.DataRows
                    .AsEnumerable()
                    .Select(equipment =>
                        this.mapper.Value.Map<AvailableEquipmentResponseModel>(equipment, opts =>
                        {
                            opts.Items[AutomapperConstants.UserAdAccount] = equipment.User.Adaccount;
                        })),
            };
        }

        public async Task DeliverItemsAsync(DeliverItemsRequestModel request)
        {
            await this.equipmentDataHandler.Value.DeliverItemsAsync(request.SelectedItems, request.FromUserId, request.ToUserId, request.IsSpecialUser, request.PostOfficeId, request.WaybillNum, request.WaybillDate);
        }

        public async Task<IEnumerable<PostOfficesSelectListResponseModel>> GetAllActivePostOfficesAsync()
        {
            var postOfficess = this.equipmentDataHandler.Value.GetAllActivePostOffices();
            var selectListItems = this.mapper.Value.Map<IEnumerable<PostOfficesSelectListResponseModel>>(postOfficess);
            return await Task.FromResult(selectListItems);
        }

        public async Task<SearchResponseModel<UserItemsToAcceptResponseModel>> SearchUserItemsToAcceptDataAsync(SearchRequestModelWithUserId request)
        {
            var data = await this.equipmentDataHandler.Value.GetFilteredUserItemsToAcceptAsync(request.UserId, request.SortBy, request.SortDir, request.PageNumber, request.PageSize, request.Query);

            return this.mapper.Value.Map<FilteredDataModel<History>, SearchResponseModel<UserItemsToAcceptResponseModel>>(data);
        }

        public async Task<IDictionary<string, TransferedItemsModel>> RefuseItemsAsync(IEnumerable<int> selectedItems, RefuseReasonsList? refuseReason, string userId)
        {
            return await this.equipmentDataHandler.Value.RefuseItemsAsync(selectedItems, refuseReason, userId);
        }

        public async Task<IDictionary<string, TransferedItemsModel>> AcceptItemsAsync(IEnumerable<int> selectedItems, string userId)
        {
            return await this.equipmentDataHandler.Value.AcceptItemsAsync(selectedItems, null, userId);
        }

        public async Task<SearchResponseModel<UserItemsToCancelResponseModel>> SearchUserItemsToCancelDataAsync(SearchRequestModelWithUserId request)
        {
            var data = await this.equipmentDataHandler.Value.GetFilteredUserTransferedItemsAsync(request.UserId, request.SortBy, request.SortDir, request.PageNumber, request.PageSize, request.Query);

            return this.mapper.Value.Map<FilteredDataModel<History>, SearchResponseModel<UserItemsToCancelResponseModel>>(data);
        }

        public async Task<IDictionary<string, TransferedItemsModel>> CancelItemsAsync(IEnumerable<int> selectedItems, RefuseReasonsList? refuseReason, string userId)
        {
            return await this.equipmentDataHandler.Value.CancelItemsAsync(selectedItems, null, userId);
        }
    }
}
