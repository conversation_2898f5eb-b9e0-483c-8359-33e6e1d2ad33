﻿namespace EtDb.DataHandlers.Interfaces
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Enums;
    using EtDb.DataHandlers.Models;

    public interface IEquipmentDataHandler
    {
        IQueryable<Item> GetAllItems();

        IQueryable<SapmatMapp> GetSAPMatMapp();

        Task<List<SapmatMapp>> GetSAPMatMappAsync();

        IQueryable<AvailableEquipment> GetNonZeroQuantityAvailableItems();

        Task UpdateFromUserAvailableItemAsync(string fromUserId, int itemId, int deliveryItemQty);

        Task UpdateToUserAvailableItemAsync(string toUserId, int itemId, int deliveryItemQty);

        Task<IQueryable<AvailableEquipment>> GetAllUserAvailableItemsWithReservedForTransferAsync(string userId);

        Task<IQueryable<AvailableEquipment>> GetAllUserReservedItemsForTransferAsync(string userId);

        Task<FilteredDataModel<AvailableEquipment>> GetFilteredUserAvailableItemsAsync(string userId, string sortBy, string sortDir, int pageNumber, int pageSize, string query);

        Task<FilteredDataModel<AvailableEquipment>> GetFilteredUserDailyItemsAsync(string userId, string sortBy, string sortDir, int pageNumber, int pageSize, string query);

        Task<FilteredDataModel<AvailableEquipment>> GetFilteredUserReservedItemsForTransferAsync(string userId, string sortBy, string sortDir, int pageNumber, int pageSize, string query);

        Task<History> ReserveItemAsync(int itemId, string userId, int quantity);

        Task<History> ReserveItemAsync(string itemSerialNumber, string userId, int quantity);

        Task ReserveAllItems(string userId);

        Task<string> RemoveItemAsync(int itemId, string userId);

        Task RemoveItemsAsync(IEnumerable<int> selectedItems, string userId);

        Task RemoveAllItemsAsync(string userId);

        IQueryable<AvailableEquipment> GetUserAllAvailableItems(string userId);

        Task UpdateAvailableEquipmentsAsync(IList<AvailableEquipment> availableEquipments);

        Task DeliverItemsAsync(IEnumerable<int> selectedItems, string fromUserId, string toUserId, bool isSpecialUser, int? postOfficeId, string waybillNum, DateTime? waybillDate);

        Task<FilteredDataModel<History>> GetFilteredUserItemsToAcceptAsync(string userId, string sortBy, string sortDir, int pageNumber, int pageSize, string query);

        Task<FilteredDataModel<History>> GetFilteredUserTransferedItemsAsync(string userId, string sortBy, string sortDir, int pageNumber, int pageSize, string query);

        IQueryable<PostOffice> GetAllActivePostOffices();

        Task<IDictionary<string, TransferedItemsModel>> RefuseItemsAsync(IEnumerable<int> selectedItems, RefuseReasonsList? refuseReason, string userId);

        Task<IDictionary<string, TransferedItemsModel>> AcceptItemsAsync(IEnumerable<int> selectedItems, RefuseReasonsList? refuseReason, string userId);

        Task<IDictionary<string, TransferedItemsModel>> CancelItemsAsync(IEnumerable<int> selectedItems, RefuseReasonsList? refuseReason, string userId);
    }
}
