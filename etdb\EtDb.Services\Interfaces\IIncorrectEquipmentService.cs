﻿namespace EtDb.Services.Interfaces
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.Models.Requests;
    using EtDb.Models.Requests.EquipmentModels;
    using EtDb.Models.Responses.UserModels;

    public interface IIncorrectEquipmentService : IService
    {
        Task<int?> GetItemIdBySerialNumberAsync(string equipmentSerialNum);

        Task UpdateItemAdditionDateAsync(int itemId);

        Task<IEnumerable<History>> GetAllHistoriesByItemId(int itemId);

        Task<int> AddHistoryAsync(HistoryRequestModel request);

        Task<bool> UpdateAvailableEquipmentById(AvailableEquipmentRequestModel request);

        Task AddStatusHistoryAsync(StatusHistoryRequestModel request);

        Task<bool> DeleteStatusHistoryAsync(int historyId);

        Task<bool> DeleteErrorsByHistoryIdAsync(int historyId);

        Task<bool> DeleteHistoryByIdAsync(int id);

        Task<string> GetTechnicianIdByNameAsync(UserRequestModel request);

        Task<IEnumerable<UsersWithOpCodeResponseModel>> GetAllUsersWithOpCodeAsync();

        Task<int?> GetAvailableEquipmentIdByTechnicianNameAndItemIdAsync(AvailableEquipmentRequestModel request);

        Task AddAvailableEquipmentAsync(AvailableEquipmentRequestModel request);

        Task<bool> UpdateAvailableEquipmentToZeroAsync(int itemId);

        Task<bool> DoesSerialNumberExistAsync(string equipmentSerialNum);

        Task<IEnumerable<UserResponseModel>> GetUserDisplayAndIptuNames();
    }
}
