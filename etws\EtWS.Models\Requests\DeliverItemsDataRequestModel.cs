﻿namespace EtWS.Models.Requests
{
    public class DeliverItemsDataRequestModel
    {
        [Required]
        public IEnumerable<int> SelectedItems { get; set; }

        [Required]
        public string ToUserId { get; set; }

        public bool IsSpecialUser { get; set; }

        public int? PostOfficeId { get; set; }

        public string WaybillNum { get; set; }

        public DateTime? WaybillDate { get; set; }
    }
}
