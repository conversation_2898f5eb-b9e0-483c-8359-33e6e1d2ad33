﻿namespace EtWS.Api.Controllers
{
    using EtWS.Api.Infrastructure.Utils;
    using EtWS.ApiClients.ETDB;
    using EtWS.Services.EquipmentGroupService;
    using Microsoft.AspNetCore.Authentication.Cookies;
    using Microsoft.AspNetCore.Authorization;

    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class EquipmentGroupController : BaseController
    {
        private readonly Lazy<IEquipmentGroupService> equipmentGroupService;

        public EquipmentGroupController(Lazy<IEquipmentGroupService> equipmentGroupService)
        {
            this.equipmentGroupService = equipmentGroupService;
        }

        [HttpGet("all")]
        [ProducesResponseType(typeof(IEnumerable<EquipmentGroupResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<EquipmentGroupResponseModel>>> GetEquipmentGroups()
        {
            try
            {
                return this.Ok(await this.equipmentGroupService.Value.GetEquipmentGroupsAsync());
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("all/names")]
        [ProducesResponseType(typeof(IEnumerable<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<string>>> GetEquipmentGroupsNames()
        {
            try
            {
                return this.Ok(await this.equipmentGroupService.Value.GetEquipmentGroupsNamesAsync());
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("name/{sapMaterialNum}")]
        [ProducesResponseType(typeof(IEnumerable<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<string>>> GetEquipmentGroupNameAsync(string sapMaterialNum)
        {
            try
            {
                return this.Ok(await this.equipmentGroupService.Value.GetEquipmentGroupNameAsync(sapMaterialNum));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("by-id/{id}")]
        [ProducesResponseType(typeof(EquipmentGroupResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<EquipmentGroupResponseModel>> GetEquipmentGroupByIdAsync(int id)
        {
            try
            {
                return this.Ok(await this.equipmentGroupService.Value.GetEquipmentGroupByIdAsync(id));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
