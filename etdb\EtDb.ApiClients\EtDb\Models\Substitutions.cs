﻿using System;
using System.Collections.Generic;

namespace EtDb.ApiClients.EtDb.Models
{
    public partial class Substitutions
    {
        public int Id { get; set; }
        public string Opcode { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string ForUserId { get; set; }
        public string SubstituteUserId { get; set; }
        public bool IsActive { get; set; }

        public virtual User ForUser { get; set; }
        public virtual User SubstituteUser { get; set; }
    }
}
