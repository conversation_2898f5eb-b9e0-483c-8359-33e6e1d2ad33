﻿namespace EtDb.Api.Infrastructure.Mapper
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Models;
    using EtDb.Infrastructure.Enumerations;
    using EtDb.Models.Requests;
    using EtDb.Models.Requests.EquipmentModels;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.UserModels;
    using Net.Ams;

    public class DefaultProfile : Profile
    {
        public DefaultProfile()
        {
            this.CreateMap<HistoryRequestModel, History>();

            this.CreateMap<StatusHistoryRequestModel, StatusHistory>();

            this.CreateMap<AvailableEquipmentRequestModel, AvailableEquipment>();

            this.CreateMap<AdministrateProductRequestModel, AdministrateProductDto>();

            this.CreateMap<AdministrateProductDto, AdministrateProductResponseModel>()
                .ForMember(m => m.Name, opts => opts.MapFrom(x => string.IsNullOrEmpty(x.Name) ? string.Empty : x.Name.Trim()))
                .ForMember(m => m.SAPMaterialNum, opts => opts.MapFrom(x => string.IsNullOrEmpty(x.SAPMaterialNum) ? string.Empty : x.SAPMaterialNum.Trim()))
                .ForMember(m => m.EquipmentNameFirst, opts => opts.MapFrom(x => string.IsNullOrEmpty(x.EquipmentNameFirst) ? string.Empty : x.EquipmentNameFirst.Trim()))
                .ForMember(m => m.MatFirst, opts => opts.MapFrom(x => string.IsNullOrEmpty(x.MatFirst) ? string.Empty : x.MatFirst.Trim()))
                .ForMember(m => m.SerialNumbersRequiredStatus, opts => opts.MapFrom(x => x.SerialNumberRequired == 1
                    ? SerialNumberRequiredStatus.Required
                    : SerialNumberRequiredStatus.Optional));

            this.CreateMap<FilteredDataModel<AdministrateProductDto>, SearchResponseModel<AdministrateProductResponseModel>>()
                .ForMember(m => m.DataCollection, opts => opts.MapFrom(x => x.DataRows.ToList()))
                .ForMember(m => m.Count, opts => opts.MapFrom(x => x.AllDataRowsCount));

            this.CreateMap<Schenkers, SchenkersResponseModel>()
                .ForMember(m => m.IPTUName, opt => opt.MapFrom(s => s.Molid != null ? s.User.First().Iptuname : string.Empty))
                .ForMember(m => m.RegionName, opt => opt.MapFrom(s => s.Molid != null && s.User.First().CityId != null ? Enum.GetName(typeof(Regions), s.User.First().City.Region) : string.Empty))
                .ForMember(m => m.LocalWarehouseId, opt => opt.MapFrom(s => s.WarehouseId != null ? (int?)s.Warehouse.Id : null))
                .ForMember(m => m.LocalWarehouseName, opt => opt.MapFrom(s => s.WarehouseId != null ? s.Warehouse.Name : string.Empty));

            this.CreateMap<Schenkers, SchenkersResponseModel>();

            this.CreateMap<Schenkers, SchenkerOpCodeResponseModel>();

            this.CreateMap<Schenkers, SchenkersResponseModel>();

            this.CreateMap<SchenkersRequestModel, SchenkerDto>();

            this.CreateMap<SchenkerDto, Schenkers>()
                .ForMember(m => m.WarehouseId, opt => opt.MapFrom(s => s.LocalWarehouseId))
                .ReverseMap();

            this.CreateMap<FilteredDataModel<Schenkers>, SearchResponseModel<SchenkersResponseModel>>()
                .ForMember(m => m.DataCollection, opts => opts.MapFrom(x => x.DataRows.ToList()))
                .ForMember(m => m.Count, opts => opts.MapFrom(x => x.AllDataRowsCount));

            this.CreateMap<FilteredDataModel<CitiesDto>, SearchResponseModel<CitiesResponseModel>>()
                .ForMember(dest => dest.DataCollection, opt => opt.MapFrom(s => s.DataRows))
                .ForMember(dest => dest.Count, opt => opt.MapFrom(s => s.AllDataRowsCount));
            this.CreateMap<CitiesDto, CitiesResponseModel>();
            this.CreateMap<CitiesRequestModel, CitiesDto>();
            this.CreateMap<CitiesDto, Cities>()
                .ForMember(m => m.SapcityCode, opt => opt.MapFrom(s => s.SAPCityCode));
            this.CreateMap<Cities, CitiesDto>()
                .ForMember(m => m.SAPCityCode, opt => opt.MapFrom(s => s.SapcityCode));

            this.CreateMap<FilteredDataModel<Substitutions>, SearchResponseModel<SubstitutionsResponseModel>>()
                .ForMember(dest => dest.DataCollection, opt => opt.MapFrom(s => s.DataRows))
                .ForMember(dest => dest.Count, opt => opt.MapFrom(s => s.AllDataRowsCount));
            this.CreateMap<Substitutions, SubstitutionsResponseModel>()
                .ForMember(m => m.OPCode, opt => opt.MapFrom(s => s.Opcode))
                .ForMember(m => m.ForUserFullName, opt => opt.MapFrom(s => s.ForUser.FullName))
                .ForMember(m => m.SubstituteUserFullName, opt => opt.MapFrom(s => s.SubstituteUser.FullName))
                .ForMember(m => m.ForUserAdAccount, opt => opt.MapFrom(s => s.ForUser.Adaccount))
                .ForMember(m => m.ForSubstituteAdAccount, opt => opt.MapFrom(s => s.SubstituteUser.Adaccount));
            this.CreateMap<SubstitutionsRequestModel, Substitutions>();
            this.CreateMap<Substitutions, SubstitutionsResponseModel>()
                .ForMember(m => m.OPCode, opt => opt.MapFrom(s => s.Opcode))
                .ForMember(m => m.ForUserFullName, opt => opt.MapFrom(s => $"{s.ForUser.FullName} - {s.ForUser.Eln}"))
                .ForMember(m => m.SubstituteUserFullName, opt => opt.MapFrom(s => $"{s.SubstituteUser.FullName} - {s.SubstituteUser.Eln}"))
                .ForMember(m => m.ForUserAdAccount, opt => opt.MapFrom(s => s.ForUser.Adaccount))
                .ForMember(m => m.ForSubstituteAdAccount, opt => opt.MapFrom(s => s.SubstituteUser.Adaccount));
            this.CreateMap<User, UserConciseResponseModel>();
        }
    }
}
