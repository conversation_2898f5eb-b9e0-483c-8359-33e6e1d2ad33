﻿namespace EtWS.Api.Infrastructure.Mapper
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.UserModels;

    public class UserProfile : Profile
    {
        public UserProfile()
        {
            this.CreateMap<SearchDataRequestModel, SearchUsersRequestModel>();

            this.CreateMap<UserResponseModel, UserDataResponseModel>();

            this.CreateMap<SearchUserResponseModel, SearchUserDataResponseModel>()
                .ForMember(m => m.IsMolOfOp, opt => opt.MapFrom(u => u.Opcode != null));

            this.CreateMap<UserConciseResponseModel, UserDataConciseResponseModel>();

            this.CreateMap<UserResponseModel, UsersSelectListResponseModel>()
                .ForMember(m => m.Id, opt => opt.MapFrom(u => u.Id))
                .ForMember(m => m.Name, opt => opt.MapFrom(u => u.DisplayName + ", " + u.Eln));

            this.CreateMap<EditUserRequest, EditUsersRequestModel>();

            this.CreateMap<SearchUserResponseModelSearchResponseModel, SearchResponseModel<SearchUserDataResponseModel>>()
                .ForMember(r => r.Count, opt => opt.MapFrom(c => c.Count));

            this.CreateMap<UserResponseModel, UserDisplayNameResponseModel>();

            this.CreateMap<UserRequest, UserRequestModel>();
        }
    }
}
