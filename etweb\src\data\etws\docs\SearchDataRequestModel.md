# SearchDataRequestModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**pageNumber** | **number** |  | [optional] [default to undefined]
**pageSize** | **number** |  | [optional] [default to undefined]
**sortBy** | **string** |  | [optional] [default to undefined]
**sortDir** | **string** |  | [optional] [default to undefined]
**query** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { SearchDataRequestModel } from './api';

const instance: SearchDataRequestModel = {
    pageNumber,
    pageSize,
    sortBy,
    sortDir,
    query,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
