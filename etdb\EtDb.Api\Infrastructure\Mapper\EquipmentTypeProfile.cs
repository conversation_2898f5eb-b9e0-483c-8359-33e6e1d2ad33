﻿namespace EtDb.Api.Infrastructure.Mapper
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.Infrastructure.Enumerations;
    using EtDb.Models.Responses.EquipmentModels;

    public class EquipmentTypeProfile : Profile
    {
        public EquipmentTypeProfile()
        {
            this.CreateMap<EquipmentType, EquipmentTypeResponseModel>()
                .ForMember(e => e.SendMethod, opts => opts.MapFrom(m => Enum.GetName(typeof(SendMethod), m.SendMethod) ?? string.Empty));
        }
    }
}
