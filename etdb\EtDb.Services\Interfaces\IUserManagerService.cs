﻿namespace EtDb.Services.Interfaces
{
    using EtDb.ApiClients.EtDb.Enums;
    using EtDb.Models.Requests;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.UserModels;

    public interface IUserManagerService : IService
    {
        Task<SearchUserResponseModel> GetUserByIdAsync(string userId);

        Task<string> GetUserAdAccountAsync(string userId);

        Task<SearchUserResponseModel> GetUserByUsernameAsync(string username);

        IEnumerable<UserResponseModel> GetUsersByUserElnsCollection(IEnumerable<string> elns);

        IEnumerable<UserResponseModel> FilterUsersByIptuAndSchenker(IEnumerable<string> customIptuNames, int? userSchenkerId);

        Task<bool> IsUserMolAsync(string userId);

        IEnumerable<UserConciseResponseModel> GetAllMols();

        IEnumerable<UserResponseModel> GetUserMostFrequentTransfersTo(string fromUserId);

        Task<SearchResponseModel<SearchUserResponseModel>> SearchUsers(SearchUsersRequestModel request);

        Task UpdateUserAsync(EditUsersRequestModel request);

        Task ActivateSelectedUsersAsync(IEnumerable<string> selectedUsers, string userId);

        Task BlockSelectedUsersAsync(IEnumerable<string> selectedUsers, string userId);

        Task UpdateUserNotificationAsync(string userId, NotificationType notificationType, string text = null);

        Task RemoveUserNotificationAsync(string userId, int notificationId);

        Task RemoveUserNotificationAsync(string userId, NotificationType notificationType);
    }
}
