import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "../ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit, MoreHorizontal } from "lucide-react";
import type { SearchUserDataResponseModel } from "@/data/etws";
import { useTranslation } from "react-i18next";

interface UserTableProps {
  users: SearchUserDataResponseModel[];
  isLoading: boolean;
  selectedUsers: string[];
  handleSelectUser: (userId: string, checked: boolean) => void;
  handleSelectAll: (checked: boolean) => void;
  handleEditUser: (user: SearchUserDataResponseModel) => void;
}

const UserTable = ({
  users,
  isLoading,
  selectedUsers,
  handleSelectUser,
  handleSelectAll,
  handleEditUser,
}: UserTableProps) => {
  const { t } = useTranslation();

      const getStatusBadge = (blocked: string | undefined) => {
        if (blocked === "Blocked") {
          return <Badge variant="destructive">{t("blocked")}</Badge>;
        }
        return <Badge variant="default">{t("active")}</Badge>;
      };

  return (
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={
                    selectedUsers.length === users.length && users.length > 0
                  }
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>{t("eln")}</TableHead>
              <TableHead>{t("firstName")}</TableHead>
              <TableHead>{t("surname")}</TableHead>
              <TableHead>{t("familyName")}</TableHead>
              <TableHead>{t("op")}</TableHead>
              <TableHead>{t("region")}</TableHead>
              <TableHead>{t("iptuName")}</TableHead>
              <TableHead>{t("city")}</TableHead>
              <TableHead>{t("clientNumber")}</TableHead>
              <TableHead>{t("status")}</TableHead>
              <TableHead className="w-16"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  {t("loading")}...
                </TableCell>
              </TableRow>
            ) : users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  {t("noUsersFound")}
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedUsers.includes(user.id || "")}
                      onCheckedChange={(checked) =>
                        handleSelectUser(user.id || "", checked as boolean)
                      }
                    />
                  </TableCell>
                  <TableCell className="font-medium">{user.eln}</TableCell>
                  <TableCell>{user.firstName}</TableCell>
                  <TableCell>{user.surname}</TableCell>
                  <TableCell>{user.familyName}</TableCell>
                  <TableCell>{user.op}</TableCell>
                  <TableCell>{user.region}</TableCell>
                  <TableCell>{user.iptuName}</TableCell>
                  <TableCell>{user.city}</TableCell>
                  <TableCell>{user.clientNumber}</TableCell>
                  <TableCell>{getStatusBadge(user.blocked)}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditUser(user)}>
                          <Edit className="h-4 w-4 mr-2" />
                          {t("edit")}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
  )
}

export default UserTable
