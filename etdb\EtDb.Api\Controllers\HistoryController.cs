﻿namespace EtDb.Api.Controllers
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Models;
    using EtDb.Models.Responses;
    using EtDb.Services.Interfaces;

    [ApiController]
    [Route("api/[controller]")]
    public class HistoryController : BaseApiController
    {
        private readonly Lazy<IHistoryService> historyService;

        public HistoryController(Lazy<IHistoryService> historyService)
        {
            this.historyService = historyService;
        }

        [HttpPost("block-user-transfers")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateHistoryTransfersToBlockedUsers([FromBody] List<string> userIds)
        {
            try
            {
                if (userIds == null || userIds.Count == 0)
                {
                    return this.BadRequest(new BaseResponseModel { Success = false, Message = "User list is empty." });
                }

                await this.historyService.Value.UpdateHistoryTransfersToBlockedUsersAsync(userIds);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpPost("cancel-expired-transfers")]
        [ProducesResponseType(typeof(IDictionary<string, TransferedItemsModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CancelExpiredTransfers([FromQuery] double? cancellationDays)
        {
            try
            {
                if (!cancellationDays.HasValue || cancellationDays <= 0)
                {
                    return this.BadRequest(new BaseResponseModel { Success = false, Message = "Invalid cancellationDays value." });
                }

                var result = await this.historyService.Value.CancelHistoryExpiredTransfersAsync(cancellationDays.Value);

                return this.Ok(result);
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpGet("reserved-items")]
        [ProducesResponseType(typeof(IEnumerable<History>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetReservedItemsByFromUserId([FromQuery] string userId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(userId))
                {
                    return this.BadRequest(new BaseResponseModel { Success = false, Message = "User ID is required." });
                }

                var result = await this.historyService.Value.GetReservedItemsByFromUserIdAsync(userId);
                return this.Ok(result);
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }
    }
}
