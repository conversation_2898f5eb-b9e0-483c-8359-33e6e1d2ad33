﻿namespace EtWS.Api.Controllers
{
    using EtWS.Api.Infrastructure.Utils;
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.SchenkerModels;
    using EtWS.Models.Responses.UserModels;
    using EtWS.Services.Interfaces;

    public class SchenkersController : BaseApiController
    {
        private readonly Lazy<ISchenkersService> schenkersService;

        public SchenkersController(Lazy<ISchenkersService> schenkerService)
        {
            this.schenkersService = schenkerService;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(typeof(SchenkerResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SchenkerResponseModel>> GetAsync([Required, FromRoute] int id)
        {
            try
            {
                var schenker = await this.schenkersService.Value.GetSchenkerByIdAsync(id);
                return this.Ok(schenker);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("users/{schenkerId}")]
        [ProducesResponseType(typeof(IEnumerable<UserDataConciseResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<UserDataConciseResponseModel>>> GetSchenkerUsersAsync([Required] int schenkerId)
        {
            try
            {
                var users = await this.schenkersService.Value.GetSchenkerUsersAsync(schenkerId);
                return this.Ok(users);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("select-list")]
        [ProducesResponseType(typeof(IEnumerable<SchenkerSelectItemResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<SchenkerSelectItemResponseModel>>> GetSchenkersSelectListAsync()
        {
            try
            {
                var items = await this.schenkersService.Value.GetSchenkerOpCodesListAsync();
                return this.Ok(items);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("search")]
        [ProducesResponseType(typeof(SearchResponseModel<SchenkerResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<SchenkerResponseModel>>> GetSchenkersAsync([Required] SearchDataRequestModel request)
        {
            try
            {
                var schenkers = await this.schenkersService.Value.SearchSchenkersAsync(request);
                return this.Ok(schenkers);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("add")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> AddSchenkerAsync([Required] SchenkerRequestModel request)
        {
            try
            {
                var newSchenkerId = await this.schenkersService.Value.AddSchenkerAsync(request);
                return this.Ok(newSchenkerId);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPut("update")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateSchenkerAsync([Required, FromBody] SchenkerRequestModel request)
        {
            try
            {
                await this.schenkersService.Value.UpdateSchenkerAsync(request);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
