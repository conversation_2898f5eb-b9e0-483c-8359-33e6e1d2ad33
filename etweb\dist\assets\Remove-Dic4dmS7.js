import{v as f,w as b,M as y,r as I,j as e,L as D,K as p}from"./index-CFpwFZya.js";const E=()=>{var m,x;const{t:s}=f(),{register:h,watch:d,setValue:i,formState:{errors:o}}=b(),v=d("equipmentSerialNum"),{data:r,isLoading:c,error:l}=y(v),j=!!o.removalDate,g=(x=(m=o.removalDate)==null?void 0:m.message)==null?void 0:x.toString(),N=d("deleteId"),t=r==null?void 0:r.find(a=>a.id===Number(N));return I.useEffect(()=>{t!=null&&t.itemId&&i("deleteItemId",t.itemId)},[t,i]),e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:"removalDate",children:s("selectRemovalDate")}),e.jsxs("div",{children:[c?e.jsxs("div",{className:"flex items-center space-x-2 py-3",children:[e.jsx("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent"}),e.jsx("span",{className:"text-sm text-gray-500",children:s("loading")})]}):l?e.jsxs("div",{className:"flex items-center rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-sm text-destructive",children:[e.jsx(p,{className:"mr-2 h-5 w-5"}),e.jsx("span",{children:(()=>{var n,u;return((u=(n=l.response)==null?void 0:n.data)==null?void 0:u.message)||l.message||s("errorOccurred")})()})]}):e.jsxs("select",{id:"deleteId",...h("deleteId"),className:"w-full rounded-lg border px-4 py-3 text-sm outline-none transition-all focus:ring-2",children:[e.jsx("option",{value:"",children:s("pleaseSelectADate")}),r?r.map((a,n)=>e.jsx("option",{value:a.id,children:a.insertDate},n)):"No dates available"]}),j&&!c&&!l&&e.jsxs("div",{className:"mt-1.5 flex items-center text-sm text-destructive",children:[e.jsx(p,{className:"mr-1.5 h-4 w-4"}),s(g||"")]})]})]})};export{E as default};
