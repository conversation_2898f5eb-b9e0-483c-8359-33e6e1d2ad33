﻿namespace EtDb.DataHandlers.Models
{
    public class SubstitutionsDto
    {
        public int Id { get; set; }

        public string OPCode { get; set; }

        public DateTime FromDate { get; set; }

        public DateTime ToDate { get; set; }

        public string ForUserId { get; set; }

        public string SubstituteUserId { get; set; }

        public bool IsActive { get; set; }
    }
}
