﻿namespace EtDb.Services.Interfaces
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Models;

    public interface IHistoryService : IService
    {
        Task UpdateHistoryTransfersToBlockedUsersAsync(IEnumerable<string> userIds);

        Task<IDictionary<string, TransferedItemsModel>> CancelHistoryExpiredTransfersAsync(double cancellationDays);

        Task<IEnumerable<History>> GetReservedItemsByFromUserIdAsync(string userId);
    }
}
