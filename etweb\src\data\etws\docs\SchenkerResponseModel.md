# SchenkerResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **string** |  | [optional] [default to undefined]
**opCode** | **string** |  | [optional] [default to undefined]
**transportArea** | **number** |  | [optional] [default to undefined]
**processingTime** | **number** |  | [optional] [default to undefined]
**protectiveTime** | **number** |  | [optional] [default to undefined]
**cityId** | **number** |  | [optional] [default to undefined]
**address** | **string** |  | [optional] [default to undefined]
**regionName** | **string** |  | [optional] [default to undefined]
**iptuName** | **string** |  | [optional] [default to undefined]
**localWarehouseId** | **number** |  | [optional] [default to undefined]
**localWarehouseName** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { SchenkerResponseModel } from './api';

const instance: SchenkerResponseModel = {
    id,
    opCode,
    transportArea,
    processingTime,
    protectiveTime,
    cityId,
    address,
    regionName,
    iptuName,
    localWarehouseId,
    localWarehouseName,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
