# UserItemsToAcceptResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**equipmentName** | **string** |  | [optional] [default to undefined]
**equipmentValidity** | **string** |  | [optional] [default to undefined]
**typeOfUsage** | **number** |  | [optional] [default to undefined]
**fromUserId** | **string** |  | [optional] [default to undefined]
**fromUserFullName** | **string** |  | [optional] [default to undefined]
**insertDate** | **string** |  | [optional] [default to undefined]
**equipmentSerialNum** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { UserItemsToAcceptResponseModel } from './api';

const instance: UserItemsToAcceptResponseModel = {
    id,
    equipmentName,
    equipmentValidity,
    typeOfUsage,
    fromUserId,
    fromUserFullName,
    insertDate,
    equipmentSerialNum,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
