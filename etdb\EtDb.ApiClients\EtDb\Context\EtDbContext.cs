﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using EtDb.ApiClients.EtDb.Models;

namespace EtDb.ApiClients.EtDb.Context
{
    public partial class EtDbContext : DbContext
    {
        private EtDbContext()
        {
        }

        public EtDbContext(DbContextOptions<EtDbContext> options)
            : base(options)
        {
        }

        public virtual DbSet<AggregatedCounter> AggregatedCounters { get; set; }
        public virtual DbSet<AvailableEquipment> AvailableEquipments { get; set; }
        public virtual DbSet<AvailableEquipmentByOpsnapshots> AvailableEquipmentByOpsnapshots { get; set; }
        public virtual DbSet<Cities> Cities { get; set; }
        public virtual DbSet<Configurations> Configurations { get; set; }
        public virtual DbSet<Counter> Counters { get; set; }
        public virtual DbSet<EquipmentGroups> EquipmentGroups { get; set; }
        public virtual DbSet<EquipmentType> EquipmentTypes { get; set; }
        public virtual DbSet<Error> Errors { get; set; }
        public virtual DbSet<Hash> Hash { get; set; }
        public virtual DbSet<History> Histories { get; set; }
        public virtual DbSet<IntDeliveryNumber> IntDeliveryNumbers { get; set; }
        public virtual DbSet<Item> Items { get; set; }
        public virtual DbSet<Job> Jobs { get; set; }
        public virtual DbSet<JobParameter> JobParameters { get; set; }
        public virtual DbSet<JobQueue> JobQueu { get; set; }
        public virtual DbSet<List> Lists { get; set; }
        public virtual DbSet<MigrationHistory> MigrationHistories { get; set; }
        public virtual DbSet<Notification> Notifications { get; set; }
        public virtual DbSet<PostOffice> PostOffices { get; set; }
        public virtual DbSet<QuantityCalculationObject> QuantityCalculationObjects { get; set; }
        public virtual DbSet<RecipientHistory> RecipientHistories { get; set; }
        public virtual DbSet<SapmatMapp> SapmatMapp { get; set; }
        public virtual DbSet<Schema> Schemas { get; set; }
        public virtual DbSet<Schenkers> Schenkers { get; set; }
        public virtual DbSet<Server> Servesr { get; set; }
        public virtual DbSet<Set> Sets { get; set; }
        public virtual DbSet<SnapshotDates> SnapshotDates { get; set; }
        public virtual DbSet<State> States { get; set; }
        public virtual DbSet<StatusHistory> StatusHistories { get; set; }
        public virtual DbSet<Substitutions> Substitutions { get; set; }
        public virtual DbSet<TransferListItems> TransferListItems { get; set; }
        public virtual DbSet<TransferNumberMetadata> TransferNumberMetadatas { get; set; }
        public virtual DbSet<Transfers> Transfers { get; set; }
        public virtual DbSet<User> Users { get; set; }
        public virtual DbSet<Warehouses> Warehouses { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                throw new ArgumentException(nameof(optionsBuilder.IsConfigured));
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<AggregatedCounter>(entity =>
            {
                entity.ToTable("AggregatedCounter", "HangFire");

                entity.HasIndex(e => e.Key, "UX_HangFire_CounterAggregated_Key")
                    .IsUnique();

                entity.Property(e => e.ExpireAt).HasColumnType("datetime");

                entity.Property(e => e.Key)
                    .IsRequired()
                    .HasMaxLength(100);
            });

            modelBuilder.Entity<AvailableEquipment>(entity =>
            {
                entity.ToTable("AvailableEquipment", "ET");

                entity.HasIndex(e => new { e.UserId, e.ItemQuantity }, "IX_AvailableEquipment_UserId_ItemQuantity");

                entity.HasIndex(e => new { e.ItemId, e.UserId }, "IX_UniAvailableEquipment")
                    .IsUnique();

                entity.Property(e => e.UserId).HasMaxLength(128);

                entity.HasOne(d => d.Item)
                    .WithMany(p => p.AvailableEquipment)
                    .HasForeignKey(d => d.ItemId)
                    .HasConstraintName("FK_ET.AvailableEquipment_ET.Item_ItemId");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.AvailableEquipment)
                    .HasForeignKey(d => d.UserId)
                    .HasConstraintName("FK_ET.AvailableEquipment_ET.User_UserId");
            });

            modelBuilder.Entity<AvailableEquipmentByOpsnapshots>(entity =>
            {
                entity.ToTable("AvailableEquipmentByOPSnapshots", "ET");

                entity.Property(e => e.Opid).HasColumnName("OPId");

                entity.HasOne(d => d.EquipmentType)
                    .WithMany(p => p.AvailableEquipmentByOpsnapshots)
                    .HasForeignKey(d => d.EquipmentTypeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AvailableEquipmentByOPSnapshots_EquipmentType");

                entity.HasOne(d => d.Op)
                    .WithMany(p => p.AvailableEquipmentByOpsnapshots)
                    .HasForeignKey(d => d.Opid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AvailableEquipmentByOPSnapshots_Schenkers");

                entity.HasOne(d => d.SnapshotDate)
                    .WithMany(p => p.AvailableEquipmentByOpsnapshots)
                    .HasForeignKey(d => d.SnapshotDateId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AvailableEquipmentByOPSnapshots_SnapshotDates");
            });

            modelBuilder.Entity<Cities>(entity =>
            {
                entity.ToTable("Cities", "ET");

                entity.HasIndex(e => e.Name, "IX_Name")
                    .IsUnique();

                entity.Property(e => e.Cluster).HasMaxLength(50);

                entity.Property(e => e.CreatedOn).HasColumnType("datetime");

                entity.Property(e => e.DeletedOn).HasColumnType("datetime");

                entity.Property(e => e.ModifiedOn).HasColumnType("datetime");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.SapcityCode)
                    .HasMaxLength(50)
                    .HasColumnName("SAPCityCode");
            });

            modelBuilder.Entity<Configurations>(entity =>
            {
                entity.ToTable("Configurations", "ET");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Value)
                    .IsRequired()
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<Counter>(entity =>
            {
                entity.ToTable("Counter", "HangFire");

                entity.HasIndex(e => e.Key, "IX_HangFire_Counter_Key");

                entity.Property(e => e.ExpireAt).HasColumnType("datetime");

                entity.Property(e => e.Key)
                    .IsRequired()
                    .HasMaxLength(100);
            });

            modelBuilder.Entity<EquipmentGroups>(entity =>
            {
                entity.ToTable("EquipmentGroups", "ET");
            });

            modelBuilder.Entity<EquipmentType>(entity =>
            {
                entity.ToTable("EquipmentType", "ET");

                entity.HasIndex(e => e.SapmaterialNum, "IX_SAPMaterialNum")
                    .IsUnique();

                entity.Property(e => e.BrprojectCode)
                    .HasMaxLength(50)
                    .HasColumnName("BRProjectCode");

                entity.Property(e => e.EquipmentGroupId).HasDefaultValueSql("((1))");

                entity.Property(e => e.Name).HasMaxLength(100);

                entity.Property(e => e.SapmaterialNum)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("SAPMaterialNum");

                entity.Property(e => e.SapsupplyCode)
                    .HasMaxLength(50)
                    .HasColumnName("SAPSupplyCode");

                entity.Property(e => e.SendMethod).HasDefaultValueSql("((1))");

                entity.Property(e => e.UnitOfMeasure).HasDefaultValueSql("((1))");

                entity.HasOne(d => d.EquipmentGroup)
                    .WithMany(p => p.EquipmentType)
                    .HasForeignKey(d => d.EquipmentGroupId)
                    .OnDelete(DeleteBehavior.ClientSetNull);
            });

            modelBuilder.Entity<Error>(entity =>
            {
                entity.ToTable("Error", "ET");

                entity.HasIndex(e => e.HistoryId, "IX_HistoryId");

                entity.HasIndex(e => e.User2Id, "IX_User2Id");

                entity.HasIndex(e => e.UserId, "IX_UserId");

                entity.Property(e => e.ErrorCode).HasMaxLength(50);

                entity.Property(e => e.ErrorText).HasMaxLength(300);

                entity.Property(e => e.RequestId)
                    .HasMaxLength(50)
                    .HasColumnName("RequestID");

                entity.Property(e => e.RequestText).HasColumnType("ntext");

                entity.Property(e => e.User2Id).HasMaxLength(128);

                entity.Property(e => e.UserId).HasMaxLength(128);

                entity.HasOne(d => d.History)
                    .WithMany(p => p.Error)
                    .HasForeignKey(d => d.HistoryId)
                    .HasConstraintName("FK_ET.Error_ET.History_HistoryId");

                entity.HasOne(d => d.User2)
                    .WithMany(p => p.ErrorUser2)
                    .HasForeignKey(d => d.User2Id)
                    .HasConstraintName("FK_ET.Error_ET.User_User2Id");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.ErrorUser)
                    .HasForeignKey(d => d.UserId)
                    .HasConstraintName("FK_ET.Error_ET.User_UserId");
            });

            modelBuilder.Entity<Hash>(entity =>
            {
                entity.ToTable("Hash", "HangFire");

                entity.HasIndex(e => e.ExpireAt, "IX_HangFire_Hash_ExpireAt");

                entity.HasIndex(e => e.Key, "IX_HangFire_Hash_Key");

                entity.HasIndex(e => new { e.Key, e.Field }, "UX_HangFire_Hash_Key_Field")
                    .IsUnique();

                entity.Property(e => e.Field)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.Key)
                    .IsRequired()
                    .HasMaxLength(100);
            });

            modelBuilder.Entity<History>(entity =>
            {
                entity.ToTable("History", "ET");

                entity.HasIndex(e => new { e.DocStatus, e.ToUserId }, "IX_DocStatusToUserId");

                entity.HasIndex(e => e.FromUserId, "IX_FromUserId");

                entity.HasIndex(e => e.ItemId, "IX_ItemId");

                entity.HasIndex(e => e.PostOfficeId, "IX_PostOfficeId");

                entity.HasIndex(e => e.ToUserId, "IX_ToUserId");

                entity.Property(e => e.CrmorderId)
                    .HasMaxLength(50)
                    .HasColumnName("CRMOrderId");

                entity.Property(e => e.DelivelyFromPoint).HasMaxLength(50);

                entity.Property(e => e.DeliveryDateSap)
                    .HasColumnType("datetime")
                    .HasColumnName("DeliveryDateSAP");

                entity.Property(e => e.DeliveryNumSap).HasMaxLength(50);

                entity.Property(e => e.DeliveryShop).HasMaxLength(10);

                entity.Property(e => e.DeliveryType).HasMaxLength(10);

                entity.Property(e => e.FromUserId).HasMaxLength(128);

                entity.Property(e => e.IcmIdSgwId)
                    .HasMaxLength(50)
                    .HasColumnName("IcmId_SgwId");

                entity.Property(e => e.InsertDate).HasColumnType("datetime");

                entity.Property(e => e.IntDeliveryNum).HasMaxLength(50);

                entity.Property(e => e.ItemValidity).HasMaxLength(10);

                entity.Property(e => e.ServiceIdFrom).HasMaxLength(50);

                entity.Property(e => e.ServiceIdTo).HasMaxLength(50);

                entity.Property(e => e.SourceSystem).HasMaxLength(20);

                entity.Property(e => e.ToUserId).HasMaxLength(128);

                entity.Property(e => e.WaybillDate).HasColumnType("datetime");

                entity.Property(e => e.WaybillNum).HasMaxLength(50);

                entity.HasOne(d => d.FromUser)
                    .WithMany(p => p.HistoryFromUser)
                    .HasForeignKey(d => d.FromUserId)
                    .HasConstraintName("FK_ET.History_ET.User_FromUserId");

                entity.HasOne(d => d.Item)
                    .WithMany(p => p.Histories)
                    .HasForeignKey(d => d.ItemId)
                    .HasConstraintName("FK_ET.History_ET.Item_ItemId");

                entity.HasOne(d => d.PostOffice)
                    .WithMany(p => p.History)
                    .HasForeignKey(d => d.PostOfficeId)
                    .HasConstraintName("FK_ET.History_ET.PostOffice_PostOfficeId");

                entity.HasOne(d => d.ToUser)
                    .WithMany(p => p.HistoryToUser)
                    .HasForeignKey(d => d.ToUserId)
                    .HasConstraintName("FK_ET.History_ET.User_ToUserId");
            });

            modelBuilder.Entity<IntDeliveryNumber>(entity =>
            {
                entity.HasKey(e => e.UniqueId)
                    .HasName("PK_ET.IntDeliveryNumber");

                entity.ToTable("IntDeliveryNumber", "ET");
            });

            modelBuilder.Entity<Item>(entity =>
            {
                entity.ToTable("Item", "ET");

                entity.HasIndex(e => e.EquipmentSerialNum, "IX_EquipmentSerialNum")
                    .IsUnique();

                entity.HasIndex(e => e.EquipmentTypeId, "IX_EquipmentTypeId");

                entity.HasIndex(e => e.SapserialNum, "IX_SAPSerialNum")
                    .IsUnique();

                entity.Property(e => e.AdditionDate).HasColumnType("datetime");

                entity.Property(e => e.EquipmentMaterialGroup).HasMaxLength(50);

                entity.Property(e => e.EquipmentName).HasMaxLength(100);

                entity.Property(e => e.EquipmentSerialNum).HasMaxLength(50);

                entity.Property(e => e.EquipmentValidity).HasMaxLength(10);

                entity.Property(e => e.IcmIdSgwId)
                    .HasMaxLength(50)
                    .HasColumnName("IcmId_SgwId");

                entity.Property(e => e.ModifyDate).HasColumnType("datetime");

                entity.Property(e => e.SapmaterialNum)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("SAPMaterialNum");

                entity.Property(e => e.SapserialNum)
                    .HasMaxLength(50)
                    .HasColumnName("SAPSerialNum");

                entity.Property(e => e.SourceSystem).HasMaxLength(10);

                entity.HasOne(d => d.EquipmentType)
                    .WithMany(p => p.Item)
                    .HasForeignKey(d => d.EquipmentTypeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ET.Item_ET.EquipmentType_EquipmentTypeId");
            });

            modelBuilder.Entity<Job>(entity =>
            {
                entity.ToTable("Job", "HangFire");

                entity.HasIndex(e => e.ExpireAt, "IX_HangFire_Job_ExpireAt");

                entity.HasIndex(e => e.StateName, "IX_HangFire_Job_StateName");

                entity.Property(e => e.Arguments).IsRequired();

                entity.Property(e => e.CreatedAt).HasColumnType("datetime");

                entity.Property(e => e.ExpireAt).HasColumnType("datetime");

                entity.Property(e => e.InvocationData).IsRequired();

                entity.Property(e => e.StateName).HasMaxLength(20);
            });

            modelBuilder.Entity<JobParameter>(entity =>
            {
                entity.ToTable("JobParameter", "HangFire");

                entity.HasIndex(e => new { e.JobId, e.Name }, "IX_HangFire_JobParameter_JobIdAndName");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(40);

                entity.HasOne(d => d.Job)
                    .WithMany(p => p.JobParameter)
                    .HasForeignKey(d => d.JobId)
                    .HasConstraintName("FK_HangFire_JobParameter_Job");
            });

            modelBuilder.Entity<JobQueue>(entity =>
            {
                entity.ToTable("JobQueue", "HangFire");

                entity.HasIndex(e => new { e.Queue, e.FetchedAt }, "IX_HangFire_JobQueue_QueueAndFetchedAt");

                entity.Property(e => e.FetchedAt).HasColumnType("datetime");

                entity.Property(e => e.Queue)
                    .IsRequired()
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<List>(entity =>
            {
                entity.ToTable("List", "HangFire");

                entity.HasIndex(e => e.ExpireAt, "IX_HangFire_List_ExpireAt");

                entity.HasIndex(e => e.Key, "IX_HangFire_List_Key");

                entity.Property(e => e.ExpireAt).HasColumnType("datetime");

                entity.Property(e => e.Key)
                    .IsRequired()
                    .HasMaxLength(100);
            });

            modelBuilder.Entity<MigrationHistory>(entity =>
            {
                entity.HasKey(e => new { e.MigrationId, e.ContextKey })
                    .HasName("PK_dbo.__MigrationHistory");

                entity.ToTable("__MigrationHistory");

                entity.Property(e => e.MigrationId).HasMaxLength(150);

                entity.Property(e => e.ContextKey).HasMaxLength(300);

                entity.Property(e => e.Model).IsRequired();

                entity.Property(e => e.ProductVersion)
                    .IsRequired()
                    .HasMaxLength(32);
            });

            modelBuilder.Entity<Notification>(entity =>
            {
                entity.ToTable("Notification", "ET");

                entity.HasIndex(e => e.UserId, "IX_UserId");

                entity.Property(e => e.Text).HasMaxLength(300);

                entity.Property(e => e.UserId).HasMaxLength(128);

                entity.HasOne(d => d.User)
                    .WithMany(p => p.Notification)
                    .HasForeignKey(d => d.UserId)
                    .HasConstraintName("FK_ET.Notification_ET.User_UserId");
            });

            modelBuilder.Entity<PostOffice>(entity =>
            {
                entity.ToTable("PostOffice", "ET");

                entity.Property(e => e.Name).HasMaxLength(50);
            });

            modelBuilder.Entity<QuantityCalculationObject>(entity =>
            {
                entity.ToTable("QuantityCalculationObject", "ET");

                entity.Property(e => e.LastUpdateDate).HasColumnType("datetime");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Opid).HasColumnName("OPId");

                entity.HasOne(d => d.EquipmentGroup)
                    .WithMany(p => p.QuantityCalculationObject)
                    .HasForeignKey(d => d.EquipmentGroupId)
                    .HasConstraintName("FK_QuantityCalculationObject_EquipmentGroups");

                entity.HasOne(d => d.EquipmentType)
                    .WithMany(p => p.QuantityCalculationObject)
                    .HasForeignKey(d => d.EquipmentTypeId)
                    .HasConstraintName("FK_QuantityCalculationObject_EquipmentType");

                entity.HasOne(d => d.Op)
                    .WithMany(p => p.QuantityCalculationObject)
                    .HasForeignKey(d => d.Opid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_QuantityCalculationObject_Schenkers");
            });

            modelBuilder.Entity<RecipientHistory>(entity =>
            {
                entity.ToTable("RecipientHistory", "ET");

                entity.HasIndex(e => e.FromUserId, "IX_FromUserId");

                entity.HasIndex(e => e.ToUserId, "IX_ToUserId");

                entity.Property(e => e.FromUserId).HasMaxLength(128);

                entity.Property(e => e.ToUserId).HasMaxLength(128);

                entity.HasOne(d => d.FromUser)
                    .WithMany(p => p.RecipientHistoryFromUser)
                    .HasForeignKey(d => d.FromUserId)
                    .HasConstraintName("FK_ET.RecipientHistory_ET.User_FromUserId");

                entity.HasOne(d => d.ToUser)
                    .WithMany(p => p.RecipientHistoryToUser)
                    .HasForeignKey(d => d.ToUserId)
                    .HasConstraintName("FK_ET.RecipientHistory_ET.User_ToUserId");
            });

            modelBuilder.Entity<SapmatMapp>(entity =>
            {
                entity.ToTable("SAPMatMapp", "ET");

                entity.HasIndex(e => e.SapmaterialNum, "IX_SAPMaterialNum")
                    .IsUnique();

                entity.Property(e => e.EquipmentNameFirst)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.MatFirst)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.SapmaterialNum)
                    .HasMaxLength(50)
                    .HasColumnName("SAPMaterialNum");
            });

            modelBuilder.Entity<Schema>(entity =>
            {
                entity.HasKey(e => e.Version)
                    .HasName("PK_HangFire_Schema");

                entity.ToTable("Schema", "HangFire");

                entity.Property(e => e.Version).ValueGeneratedNever();
            });

            modelBuilder.Entity<Schenkers>(entity =>
            {
                entity.ToTable("Schenkers", "ET");

                entity.HasIndex(e => e.Molid, "IX_MOLId");

                entity.HasIndex(e => e.Opcode, "IX_OPCode")
                    .IsUnique();

                entity.Property(e => e.CityId).HasDefaultValueSql("((1))");

                entity.Property(e => e.IncludedInMonthlyCalculations)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.Molid)
                    .HasMaxLength(128)
                    .HasColumnName("MOLId");

                entity.Property(e => e.Opcode)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("OPCode");

                entity.HasOne(d => d.City)
                    .WithMany(p => p.Schenkers)
                    .HasForeignKey(d => d.CityId)
                    .OnDelete(DeleteBehavior.ClientSetNull);

                entity.HasOne(d => d.Mol)
                    .WithMany(p => p.Schenkers)
                    .HasForeignKey(d => d.Molid)
                    .HasConstraintName("FK_ET.Schenkers_ET.User_MOLId");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.Schenkers)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_Schenkers_Warehouses");
            });

            modelBuilder.Entity<Server>(entity =>
            {
                entity.ToTable("Server", "HangFire");

                entity.Property(e => e.Id).HasMaxLength(100);

                entity.Property(e => e.LastHeartbeat).HasColumnType("datetime");
            });

            modelBuilder.Entity<Set>(entity =>
            {
                entity.ToTable("Set", "HangFire");

                entity.HasIndex(e => e.ExpireAt, "IX_HangFire_Set_ExpireAt");

                entity.HasIndex(e => e.Key, "IX_HangFire_Set_Key");

                entity.HasIndex(e => new { e.Key, e.Value }, "UX_HangFire_Set_KeyAndValue")
                    .IsUnique();

                entity.Property(e => e.ExpireAt).HasColumnType("datetime");

                entity.Property(e => e.Key)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.Value)
                    .IsRequired()
                    .HasMaxLength(256);
            });

            modelBuilder.Entity<SnapshotDates>(entity =>
            {
                entity.ToTable("SnapshotDates", "ET");

                entity.Property(e => e.Date).HasColumnType("datetime");
            });

            modelBuilder.Entity<State>(entity =>
            {
                entity.ToTable("State", "HangFire");

                entity.HasIndex(e => e.JobId, "IX_HangFire_State_JobId");

                entity.Property(e => e.CreatedAt).HasColumnType("datetime");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(20);

                entity.Property(e => e.Reason).HasMaxLength(100);

                entity.HasOne(d => d.Job)
                    .WithMany(p => p.State)
                    .HasForeignKey(d => d.JobId)
                    .HasConstraintName("FK_HangFire_State_Job");
            });

            modelBuilder.Entity<StatusHistory>(entity =>
            {
                entity.ToTable("StatusHistory", "ET");

                entity.HasIndex(e => e.HistoryId, "IX_HistoryId");

                entity.HasIndex(e => e.UserId, "IX_UserId");

                entity.HasIndex(e => e.DateInsert, "ix_statusHistory_DateInsert_12");

                entity.Property(e => e.DateInsert).HasColumnType("datetime");

                entity.Property(e => e.SourceSystem).HasMaxLength(20);

                entity.Property(e => e.UserId).HasMaxLength(128);

                entity.HasOne(d => d.History)
                    .WithMany(p => p.StatusHistory)
                    .HasForeignKey(d => d.HistoryId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ET.StatusHistory_ET.History_HistoryId");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.StatusHistory)
                    .HasForeignKey(d => d.UserId)
                    .HasConstraintName("FK_ET.StatusHistory_ET.User_UserId");
            });

            modelBuilder.Entity<Substitutions>(entity =>
            {
                entity.ToTable("Substitutions", "ET");

                entity.Property(e => e.ForUserId)
                    .IsRequired()
                    .HasMaxLength(128);

                entity.Property(e => e.FromDate).HasColumnType("datetime");

                entity.Property(e => e.Opcode)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("OPCode");

                entity.Property(e => e.SubstituteUserId)
                    .IsRequired()
                    .HasMaxLength(128);

                entity.Property(e => e.ToDate).HasColumnType("datetime");

                entity.HasOne(d => d.ForUser)
                    .WithMany(p => p.SubstitutionsForUser)
                    .HasForeignKey(d => d.ForUserId)
                    .OnDelete(DeleteBehavior.ClientSetNull);

                entity.HasOne(d => d.SubstituteUser)
                    .WithMany(p => p.SubstitutionsSubstituteUser)
                    .HasForeignKey(d => d.SubstituteUserId)
                    .OnDelete(DeleteBehavior.ClientSetNull);
            });

            modelBuilder.Entity<TransferListItems>(entity =>
            {
                entity.ToTable("TransferListItems", "ET");

                entity.HasIndex(e => new { e.TransferId, e.Quantity }, "IX_TransferIdQuantity");

                entity.HasOne(d => d.EquipmentType)
                    .WithMany(p => p.TransferListItems)
                    .HasForeignKey(d => d.EquipmentTypeId)
                    .HasConstraintName("FK_ET.EquipmentTypeOrders_ET.EquipmentType_EquipmentTypeId");

                entity.HasOne(d => d.Transfer)
                    .WithMany(p => p.TransferListItems)
                    .HasForeignKey(d => d.TransferId)
                    .HasConstraintName("FK_ET.EquipmentTypeOrders_ET.TransferRequests_TransferRequestId");
            });

            modelBuilder.Entity<TransferNumberMetadata>(entity =>
            {
                entity.ToTable("TransferNumberMetadata", "ET");

                entity.Property(e => e.CreatedBy).HasMaxLength(50);

                entity.Property(e => e.CreatedOn).HasColumnType("datetime");

                entity.Property(e => e.ModifiedBy).HasMaxLength(50);

                entity.Property(e => e.ModifiedOn).HasColumnType("datetime");
            });

            modelBuilder.Entity<Transfers>(entity =>
            {
                entity.ToTable("Transfers", "ET");

                entity.HasIndex(e => e.TransferNumSerCwtoMs, "IX_Transfers_TransferNumSerCWtoMS");

                entity.HasIndex(e => e.TransferNumSerMstoRs, "IX_Transfers_TransferNumSerMStoRS");

                entity.HasIndex(e => e.TransferNumSerRstoOp, "IX_Transfers_TransferNumSerRStoOP");

                entity.Property(e => e.BrdeliveryNumbers).HasColumnName("BRDeliveryNumbers");

                entity.Property(e => e.BrprojectDeliveryNumbers).HasColumnName("BRProjectDeliveryNumbers");

                entity.Property(e => e.CreatedOn).HasColumnType("datetime");

                entity.Property(e => e.DeletedOn).HasColumnType("datetime");

                entity.Property(e => e.DeliveryDate).HasColumnType("datetime");

                entity.Property(e => e.ModifiedOn).HasColumnType("datetime");

                entity.Property(e => e.RequestNumber).IsRequired();

                entity.Property(e => e.TransferNumSerCwtoMs).HasColumnName("TransferNumSerCWtoMS");

                entity.Property(e => e.TransferNumSerMstoRs).HasColumnName("TransferNumSerMStoRS");

                entity.Property(e => e.TransferNumSerRstoOp).HasColumnName("TransferNumSerRStoOP");

                entity.HasOne(d => d.Schenker)
                    .WithMany(p => p.Transfers)
                    .HasForeignKey(d => d.SchenkerId)
                    .OnDelete(DeleteBehavior.ClientSetNull);
            });

            modelBuilder.Entity<User>(entity =>
            {
                entity.ToTable("User", "ET");

                entity.HasIndex(e => e.Adaccount, "IX_ADAccount")
                    .IsUnique();

                entity.HasIndex(e => e.BlockedByUserId, "IX_BlockedByUserId");

                entity.HasIndex(e => e.CityId, "IX_CityId");

                entity.HasIndex(e => e.Eln, "IX_ELN")
                    .IsUnique();

                entity.HasIndex(e => e.SchenkerId, "IX_SchenkerId");

                entity.Property(e => e.Id).HasMaxLength(128);

                entity.Property(e => e.Adaccount)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("ADAccount");

                entity.Property(e => e.BlockedByUserId).HasMaxLength(128);

                entity.Property(e => e.CreatedOn).HasColumnType("datetime");

                entity.Property(e => e.DataBlocked).HasColumnType("datetime");

                entity.Property(e => e.DateInactive).HasColumnType("datetime");

                entity.Property(e => e.DeletedOn).HasColumnType("datetime");

                entity.Property(e => e.DisplayName).HasMaxLength(100);

                entity.Property(e => e.Eln)
                    .HasMaxLength(128)
                    .HasColumnName("ELN");

                entity.Property(e => e.Email)
                    .HasMaxLength(100)
                    .HasColumnName("EMail");

                entity.Property(e => e.FamilyName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.FirstName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.FullName).HasMaxLength(100);

                entity.Property(e => e.Iptuname)
                    .HasMaxLength(100)
                    .HasColumnName("IPTUName");

                entity.Property(e => e.ModifiedOn).HasColumnType("datetime");

                entity.Property(e => e.Opcode)
                    .HasMaxLength(50)
                    .HasColumnName("OPCode");

                entity.Property(e => e.Position).HasMaxLength(100);

                entity.Property(e => e.Surname)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.HasOne(d => d.BlockedByUser)
                    .WithMany(p => p.InverseBlockedByUser)
                    .HasForeignKey(d => d.BlockedByUserId)
                    .HasConstraintName("FK_ET.User_ET.User_BlockedByUserId");

                entity.HasOne(d => d.City)
                    .WithMany(p => p.User)
                    .HasForeignKey(d => d.CityId)
                    .HasConstraintName("FK_ET.User_ET.Cities_CityId");

                entity.HasOne(d => d.Schenker)
                    .WithMany(p => p.User)
                    .HasForeignKey(d => d.SchenkerId)
                    .HasConstraintName("FK_ET.User_ET.Schenkers_SchenkerId");
            });

            modelBuilder.Entity<Warehouses>(entity =>
            {
                entity.ToTable("Warehouses", "ET");

                entity.Property(e => e.Name).HasMaxLength(50);
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
