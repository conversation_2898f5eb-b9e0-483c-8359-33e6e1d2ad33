# EquipmentGroupApi

All URIs are relative to *http://microit9app1.drcenter.btk.bg:26121/et-ws*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**apiEquipmentGroupAllGet**](#apiequipmentgroupallget) | **GET** /api/equipment-group/all | |
|[**apiEquipmentGroupAllNamesGet**](#apiequipmentgroupallnamesget) | **GET** /api/equipment-group/all/names | |
|[**apiEquipmentGroupByIdByIdGet**](#apiequipmentgroupbyidbyidget) | **GET** /api/equipment-group/by-id/{id} | |
|[**apiEquipmentGroupNameBySapMaterialNumGet**](#apiequipmentgroupnamebysapmaterialnumget) | **GET** /api/equipment-group/name/{sapMaterialNum} | |

# **apiEquipmentGroupAllGet**
> Array<EquipmentGroupResponseModel> apiEquipmentGroupAllGet()


### Example

```typescript
import {
    EquipmentGroupApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentGroupApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentGroupAllGet(
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**Array<EquipmentGroupResponseModel>**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentGroupAllNamesGet**
> Array<string> apiEquipmentGroupAllNamesGet()


### Example

```typescript
import {
    EquipmentGroupApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentGroupApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentGroupAllNamesGet(
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**Array<string>**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentGroupByIdByIdGet**
> EquipmentGroupResponseModel apiEquipmentGroupByIdByIdGet()


### Example

```typescript
import {
    EquipmentGroupApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentGroupApi(configuration);

let id: number; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentGroupByIdByIdGet(
    id,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**EquipmentGroupResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentGroupNameBySapMaterialNumGet**
> Array<string> apiEquipmentGroupNameBySapMaterialNumGet()


### Example

```typescript
import {
    EquipmentGroupApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentGroupApi(configuration);

let sapMaterialNum: string; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentGroupNameBySapMaterialNumGet(
    sapMaterialNum,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **sapMaterialNum** | [**string**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**Array<string>**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

