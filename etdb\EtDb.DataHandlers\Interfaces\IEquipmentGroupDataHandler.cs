﻿using EtDb.ApiClients.EtDb.Models;

namespace EtDb.DataHandlers.Interfaces
{
    public interface IEquipmentGroupDataHandler
    {
        IQueryable<EquipmentGroups> GetEquipmentGroups();

        IQueryable<string> GetEquipmentGroupsNames();

        Task<string?> GetEquipmentGroupNameAsync(string sapMaterialNum);

        Task<EquipmentGroups?> GetEquipmentGroupByIdAsync(int equipmentGroupId);
    }
}
