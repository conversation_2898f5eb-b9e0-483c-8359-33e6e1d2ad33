﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <CodeAnalysisRuleSet>../Rules.ruleset</CodeAnalysisRuleSet>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <Using Include="AutoMapper" />
    <Using Include="Net.Common" />
    <Using Include="EtWS.Infrastructure.Constants" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
    <PackageReference Include="Net.Ams" Version="0.2.20" />
    <PackageReference Include="Net.Common.Mvc" Version="0.2.20" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="6.0.0" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EtWS.ApiClients\EtWS.ApiClients.csproj" />
    <ProjectReference Include="..\EtWS.Infrastructure\EtWS.Infrastructure.csproj" />
    <ProjectReference Include="..\EtWS.Models\EtWS.Models.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Microsoft.Extensions.Options">
      <HintPath>C:\Program Files\dotnet\sdk\NuGetFallbackFolder\microsoft.extensions.options\2.2.0\lib\netstandard2.0\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
