﻿namespace EtWS.Api.Infrastructure.Utils
{
    using EtWS.ApiClients.ETDB;
    using Microsoft.EntityFrameworkCore;

    public static class ExceptionHandler
    {
        // add InvalidInputException (Status400BadRequest) and ExternalServiceFailureException (Status424FailedDependency)
        public static ActionResult HandleException(Exception ex)
        {
            if (ex is TimeoutException)
            {
                return new ObjectResult(ex.Message) { StatusCode = StatusCodes.Status408RequestTimeout };
            }
            else if (ex is ApiException<ProblemDetails> exception)
            {
                return new ObjectResult(new BaseResponseModel
                {
                    Success = false,
                    Message = exception.Result?.Title,
                })
                {
                    StatusCode = StatusCodes.Status500InternalServerError,
                };
            }
            else if (ex is ApiException<BaseResponseModel> apiBaseException)
            {
                return new ObjectResult(new BaseResponseModel
                {
                    Success = false,
                    Message = apiBaseException.Result?.Message,
                })
                {
                    StatusCode = apiBaseException.StatusCode,
                };
            }
            else if (ex is ApiException<ValidationProblemDetails> validationProblemDetailsException)
            {
                return new ObjectResult(new BaseResponseModel
                {
                    Success = false,
                    Message = validationProblemDetailsException.Result?.Title,
                })
                {
                    StatusCode = StatusCodes.Status500InternalServerError,
                };
            }
            else if (ex is ApiException generalApiException)
            {
                return new ObjectResult(new BaseResponseModel
                {
                    Success = false,
                    Message = generalApiException.Message,
                })
                {
                    StatusCode = StatusCodes.Status500InternalServerError,
                };
            }
            else if (ex is DbUpdateException)
            {
                return new ObjectResult(new BaseResponseModel
                {
                    Success = false,
                    Message = ex.Message,
                })
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                };
            }
            else if (ex is ArgumentNullException)
            {
                return new ObjectResult(new BaseResponseModel
                {
                    Success = false,
                    Message = ex.Message,
                })
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                };
            }
            else if (ex is ArgumentException || ex is InvalidOperationException)
            {
                return new ObjectResult(new BaseResponseModel
                {
                    Success = false,
                    Message = ex.Message,
                })
                {
                    StatusCode = StatusCodes.Status400BadRequest,
                };
            }
            else if (ex is KeyNotFoundException)
            {
                return new ObjectResult(new BaseResponseModel
                {
                    Success = false,
                    Message = ex.Message,
                })
                {
                    StatusCode = StatusCodes.Status404NotFound,
                };
            }
            else
            {
                return new ObjectResult(new BaseResponseModel
                {
                    Success = false,
                    Message = ex.Message,
                })
                {
                    StatusCode = StatusCodes.Status500InternalServerError,
                };
            }
        }

        public static string GetApiExceptionErrorMessage(ApiException ex)
        {
            var method = ex.TargetSite?.DeclaringType?.FullName;
            if (method != null)
            {
                int index = method.LastIndexOf('d');
                if (index >= 0)
                {
                    method = method.Substring(0, index);
                }
            }

            return $"Error occured in: {method}, Message from api: {ex.Message}.";
        }
    }
}
