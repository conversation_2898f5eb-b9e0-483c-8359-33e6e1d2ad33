/* tslint:disable */
/* eslint-disable */
/**
 * et-ws
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, BaseAPI, RequiredError, operationServerMap } from './base';

/**
 * 
 * @export
 * @interface AcceptItemsForTransferRequestModel
 */
export interface AcceptItemsForTransferRequestModel {
    /**
     * 
     * @type {Array<number>}
     * @memberof AcceptItemsForTransferRequestModel
     */
    'selectedItems'?: Array<number> | null;
}
/**
 * 
 * @export
 * @interface AvailableEquipmentDataResponseModel
 */
export interface AvailableEquipmentDataResponseModel {
    /**
     * 
     * @type {number}
     * @memberof AvailableEquipmentDataResponseModel
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof AvailableEquipmentDataResponseModel
     */
    'itemId'?: number;
    /**
     * 
     * @type {string}
     * @memberof AvailableEquipmentDataResponseModel
     */
    'userId'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof AvailableEquipmentDataResponseModel
     */
    'itemQuantity'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof AvailableEquipmentDataResponseModel
     */
    'isDaily'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AvailableEquipmentDataResponseModel
     */
    'isReserved'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AvailableEquipmentDataResponseModel
     */
    'isWaitingForConfirmation'?: boolean;
    /**
     * 
     * @type {ItemDataResponseModel}
     * @memberof AvailableEquipmentDataResponseModel
     */
    'item'?: ItemDataResponseModel | null;
}
/**
 * 
 * @export
 * @interface AvailableEquipmentDataResponseModelSearchResponseModel
 */
export interface AvailableEquipmentDataResponseModelSearchResponseModel {
    /**
     * 
     * @type {number}
     * @memberof AvailableEquipmentDataResponseModelSearchResponseModel
     */
    'count'?: number;
    /**
     * 
     * @type {Array<AvailableEquipmentDataResponseModel>}
     * @memberof AvailableEquipmentDataResponseModelSearchResponseModel
     */
    'dataCollection'?: Array<AvailableEquipmentDataResponseModel> | null;
}
/**
 * 
 * @export
 * @interface BaseResponseModel
 */
export interface BaseResponseModel {
    /**
     * 
     * @type {string}
     * @memberof BaseResponseModel
     */
    'message'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof BaseResponseModel
     */
    'success'?: boolean;
}
/**
 * 
 * @export
 * @interface CancelItemsForTransferRequestModel
 */
export interface CancelItemsForTransferRequestModel {
    /**
     * 
     * @type {Array<number>}
     * @memberof CancelItemsForTransferRequestModel
     */
    'selectedItems'?: Array<number> | null;
    /**
     * 
     * @type {string}
     * @memberof CancelItemsForTransferRequestModel
     */
    'refuseReason'?: string | null;
}
/**
 * 
 * @export
 * @interface CitiesSelectItemResponseModel
 */
export interface CitiesSelectItemResponseModel {
    /**
     * 
     * @type {number}
     * @memberof CitiesSelectItemResponseModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CitiesSelectItemResponseModel
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface CityRequestModel
 */
export interface CityRequestModel {
    /**
     * 
     * @type {string}
     * @memberof CityRequestModel
     */
    'name': string;
    /**
     * 
     * @type {number}
     * @memberof CityRequestModel
     */
    'region': number;
    /**
     * 
     * @type {string}
     * @memberof CityRequestModel
     */
    'sapCityCode'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CityRequestModel
     */
    'cluster'?: string | null;
}
/**
 * 
 * @export
 * @interface CityResponseModel
 */
export interface CityResponseModel {
    /**
     * 
     * @type {number}
     * @memberof CityResponseModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CityResponseModel
     */
    'name'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof CityResponseModel
     */
    'region'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof CityResponseModel
     */
    'isDeleted'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CityResponseModel
     */
    'sapCityCode'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CityResponseModel
     */
    'cluster'?: string | null;
}
/**
 * 
 * @export
 * @interface CityResponseModelSearchResponseModel
 */
export interface CityResponseModelSearchResponseModel {
    /**
     * 
     * @type {number}
     * @memberof CityResponseModelSearchResponseModel
     */
    'count'?: number;
    /**
     * 
     * @type {Array<CityResponseModel>}
     * @memberof CityResponseModelSearchResponseModel
     */
    'dataCollection'?: Array<CityResponseModel> | null;
}
/**
 * 
 * @export
 * @interface ConciseEquipmentTypesDataRequestModel
 */
export interface ConciseEquipmentTypesDataRequestModel {
    /**
     * 
     * @type {Array<number>}
     * @memberof ConciseEquipmentTypesDataRequestModel
     */
    'equipmentTypeIds': Array<number>;
    /**
     * 
     * @type {boolean}
     * @memberof ConciseEquipmentTypesDataRequestModel
     */
    'serialNumbersRequired'?: boolean;
}
/**
 * 
 * @export
 * @interface DeleteTransferRequest
 */
export interface DeleteTransferRequest {
    /**
     * 
     * @type {number}
     * @memberof DeleteTransferRequest
     */
    'id': number;
    /**
     * 
     * @type {number}
     * @memberof DeleteTransferRequest
     */
    'itemId': number;
}
/**
 * 
 * @export
 * @interface DeliverItemsDataRequestModel
 */
export interface DeliverItemsDataRequestModel {
    /**
     * 
     * @type {Array<number>}
     * @memberof DeliverItemsDataRequestModel
     */
    'selectedItems': Array<number>;
    /**
     * 
     * @type {string}
     * @memberof DeliverItemsDataRequestModel
     */
    'toUserId': string;
    /**
     * 
     * @type {boolean}
     * @memberof DeliverItemsDataRequestModel
     */
    'isSpecialUser'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof DeliverItemsDataRequestModel
     */
    'postOfficeId'?: number | null;
    /**
     * 
     * @type {string}
     * @memberof DeliverItemsDataRequestModel
     */
    'waybillNum'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof DeliverItemsDataRequestModel
     */
    'waybillDate'?: string | null;
}
/**
 * 
 * @export
 * @interface EditUserRequest
 */
export interface EditUserRequest {
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'eln'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'firstName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'surname'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'familyName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'status'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof EditUserRequest
     */
    'schenkerId': number;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'op'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'type'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'userType'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'clientNumber'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'city'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof EditUserRequest
     */
    'cityId': number;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'region'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'iptuName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'phoneNumber'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'statusClass'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EditUserRequest
     */
    'userStatuses'?: string | null;
}
/**
 * 
 * @export
 * @interface EquipmentGroupResponseModel
 */
export interface EquipmentGroupResponseModel {
    /**
     * 
     * @type {number}
     * @memberof EquipmentGroupResponseModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof EquipmentGroupResponseModel
     */
    'name'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof EquipmentGroupResponseModel
     */
    'containsSubstitutes'?: boolean;
}
/**
 * 
 * @export
 * @interface EquipmentTypeConciseDto
 */
export interface EquipmentTypeConciseDto {
    /**
     * 
     * @type {number}
     * @memberof EquipmentTypeConciseDto
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof EquipmentTypeConciseDto
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EquipmentTypeConciseDto
     */
    'sapMaterialNum'?: string | null;
}
/**
 * 
 * @export
 * @interface EquipmentTypeResponseModel
 */
export interface EquipmentTypeResponseModel {
    /**
     * 
     * @type {number}
     * @memberof EquipmentTypeResponseModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof EquipmentTypeResponseModel
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EquipmentTypeResponseModel
     */
    'sapmaterialNum'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof EquipmentTypeResponseModel
     */
    'serialNumberRequired'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof EquipmentTypeResponseModel
     */
    'equipmentGroupId'?: number;
    /**
     * 
     * @type {number}
     * @memberof EquipmentTypeResponseModel
     */
    'unitOfMeasure'?: number;
    /**
     * 
     * @type {string}
     * @memberof EquipmentTypeResponseModel
     */
    'sendMethod'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EquipmentTypeResponseModel
     */
    'sapsupplyCode'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof EquipmentTypeResponseModel
     */
    'minimumQuantity'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof EquipmentTypeResponseModel
     */
    'isTransferFromSapAllowed'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof EquipmentTypeResponseModel
     */
    'boxCapacity'?: number | null;
    /**
     * 
     * @type {number}
     * @memberof EquipmentTypeResponseModel
     */
    'lastMonthlyOrderQuantity'?: number | null;
    /**
     * 
     * @type {string}
     * @memberof EquipmentTypeResponseModel
     */
    'brprojectCode'?: string | null;
}
/**
 * 
 * @export
 * @interface HistoriesResponse
 */
export interface HistoriesResponse {
    /**
     * 
     * @type {number}
     * @memberof HistoriesResponse
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof HistoriesResponse
     */
    'insertDate'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof HistoriesResponse
     */
    'itemId'?: number;
}
/**
 * 
 * @export
 * @interface ItemDataResponseModel
 */
export interface ItemDataResponseModel {
    /**
     * 
     * @type {number}
     * @memberof ItemDataResponseModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof ItemDataResponseModel
     */
    'icmIdSgwId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ItemDataResponseModel
     */
    'equipmentName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ItemDataResponseModel
     */
    'equipmentSerialNum'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ItemDataResponseModel
     */
    'sapserialNum'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ItemDataResponseModel
     */
    'sapmaterialNum'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof ItemDataResponseModel
     */
    'equipmentTypeId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ItemDataResponseModel
     */
    'equipmentMaterialGroup'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof ItemDataResponseModel
     */
    'typeOfUsage'?: number;
    /**
     * 
     * @type {string}
     * @memberof ItemDataResponseModel
     */
    'equipmentValidity'?: string | null;
}
/**
 * 
 * @export
 * @interface ItemDataResponseModelSearchResponseModel
 */
export interface ItemDataResponseModelSearchResponseModel {
    /**
     * 
     * @type {number}
     * @memberof ItemDataResponseModelSearchResponseModel
     */
    'count'?: number;
    /**
     * 
     * @type {Array<ItemDataResponseModel>}
     * @memberof ItemDataResponseModelSearchResponseModel
     */
    'dataCollection'?: Array<ItemDataResponseModel> | null;
}
/**
 * 
 * @export
 * @interface PostOfficesSelectListResponseModel
 */
export interface PostOfficesSelectListResponseModel {
    /**
     * 
     * @type {number}
     * @memberof PostOfficesSelectListResponseModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof PostOfficesSelectListResponseModel
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface ProblemDetails
 */
export interface ProblemDetails {
    [key: string]: any;

    /**
     * 
     * @type {string}
     * @memberof ProblemDetails
     */
    'type'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ProblemDetails
     */
    'title'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof ProblemDetails
     */
    'status'?: number | null;
    /**
     * 
     * @type {string}
     * @memberof ProblemDetails
     */
    'detail'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ProblemDetails
     */
    'instance'?: string | null;
}
/**
 * 
 * @export
 * @interface ProductDataRequestModel
 */
export interface ProductDataRequestModel {
    /**
     * 
     * @type {number}
     * @memberof ProductDataRequestModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProductDataRequestModel
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof ProductDataRequestModel
     */
    'sapMaterialNum': string;
    /**
     * 
     * @type {string}
     * @memberof ProductDataRequestModel
     */
    'equipmentNameFirst': string;
    /**
     * 
     * @type {string}
     * @memberof ProductDataRequestModel
     */
    'matFirst': string;
    /**
     * 
     * @type {number}
     * @memberof ProductDataRequestModel
     */
    'minimumQuantity': number;
    /**
     * 
     * @type {number}
     * @memberof ProductDataRequestModel
     */
    'sapRequestType': number;
    /**
     * 
     * @type {string}
     * @memberof ProductDataRequestModel
     */
    'brProjectName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ProductDataRequestModel
     */
    'sapElementCode'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof ProductDataRequestModel
     */
    'equipmentGroupId': number;
    /**
     * 
     * @type {number}
     * @memberof ProductDataRequestModel
     */
    'unitOfMeasure': number;
    /**
     * 
     * @type {number}
     * @memberof ProductDataRequestModel
     */
    'boxCapacity'?: number | null;
}
/**
 * 
 * @export
 * @interface QuantityCalculationObjectRequestModel
 */
export interface QuantityCalculationObjectRequestModel {
    /**
     * 
     * @type {number}
     * @memberof QuantityCalculationObjectRequestModel
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof QuantityCalculationObjectRequestModel
     */
    'criticalQuantity'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof QuantityCalculationObjectRequestModel
     */
    'toggleToAutomaticGeneration'?: boolean;
}
/**
 * 
 * @export
 * @interface QuantityCalculationObjectResponseModel
 */
export interface QuantityCalculationObjectResponseModel {
    /**
     * 
     * @type {number}
     * @memberof QuantityCalculationObjectResponseModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof QuantityCalculationObjectResponseModel
     */
    'name'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof QuantityCalculationObjectResponseModel
     */
    'criticalQuantity'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof QuantityCalculationObjectResponseModel
     */
    'isGroupWithSubstitutes'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof QuantityCalculationObjectResponseModel
     */
    'isManuallySet'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof QuantityCalculationObjectResponseModel
     */
    'toggleToAutomaticGeneration'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof QuantityCalculationObjectResponseModel
     */
    'lastUpdateDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof QuantityCalculationObjectResponseModel
     */
    'opCode'?: string | null;
}
/**
 * 
 * @export
 * @interface QuantityCalculationObjectResponseModelSearchResponseModel
 */
export interface QuantityCalculationObjectResponseModelSearchResponseModel {
    /**
     * 
     * @type {number}
     * @memberof QuantityCalculationObjectResponseModelSearchResponseModel
     */
    'count'?: number;
    /**
     * 
     * @type {Array<QuantityCalculationObjectResponseModel>}
     * @memberof QuantityCalculationObjectResponseModelSearchResponseModel
     */
    'dataCollection'?: Array<QuantityCalculationObjectResponseModel> | null;
}
/**
 * 
 * @export
 * @interface RefuseItemsForTransferRequestModel
 */
export interface RefuseItemsForTransferRequestModel {
    /**
     * 
     * @type {Array<number>}
     * @memberof RefuseItemsForTransferRequestModel
     */
    'selectedItems'?: Array<number> | null;
    /**
     * 
     * @type {string}
     * @memberof RefuseItemsForTransferRequestModel
     */
    'refuseReason'?: string | null;
}
/**
 * 
 * @export
 * @interface RemoveItemFromTransferResponseModel
 */
export interface RemoveItemFromTransferResponseModel {
    /**
     * 
     * @type {string}
     * @memberof RemoveItemFromTransferResponseModel
     */
    'removedEquipmentTypeName'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof RemoveItemFromTransferResponseModel
     */
    'reservedItemsCount'?: number;
}
/**
 * 
 * @export
 * @interface ReserveItemForTransferDataRequestModel
 */
export interface ReserveItemForTransferDataRequestModel {
    /**
     * 
     * @type {number}
     * @memberof ReserveItemForTransferDataRequestModel
     */
    'itemId'?: number | null;
    /**
     * 
     * @type {string}
     * @memberof ReserveItemForTransferDataRequestModel
     */
    'itemSerialNumber'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof ReserveItemForTransferDataRequestModel
     */
    'quantity'?: number;
}
/**
 * 
 * @export
 * @interface ReserveItemHistoryResponseModel
 */
export interface ReserveItemHistoryResponseModel {
    /**
     * 
     * @type {string}
     * @memberof ReserveItemHistoryResponseModel
     */
    'itemId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ReserveItemHistoryResponseModel
     */
    'itemEquipmentTypeName'?: string | null;
}
/**
 * 
 * @export
 * @interface SapCityCodesResponseModel
 */
export interface SapCityCodesResponseModel {
    /**
     * 
     * @type {string}
     * @memberof SapCityCodesResponseModel
     */
    'sapCityCode'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SapCityCodesResponseModel
     */
    'cluster'?: string | null;
}
/**
 * 
 * @export
 * @interface SchenkerRequestModel
 */
export interface SchenkerRequestModel {
    /**
     * 
     * @type {number}
     * @memberof SchenkerRequestModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof SchenkerRequestModel
     */
    'opCode': string;
    /**
     * 
     * @type {number}
     * @memberof SchenkerRequestModel
     */
    'transportArea'?: number;
    /**
     * 
     * @type {number}
     * @memberof SchenkerRequestModel
     */
    'processingTime'?: number;
    /**
     * 
     * @type {number}
     * @memberof SchenkerRequestModel
     */
    'protectiveTime'?: number;
    /**
     * 
     * @type {number}
     * @memberof SchenkerRequestModel
     */
    'cityId'?: number;
    /**
     * 
     * @type {string}
     * @memberof SchenkerRequestModel
     */
    'address'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof SchenkerRequestModel
     */
    'localWarehouseId'?: number | null;
}
/**
 * 
 * @export
 * @interface SchenkerResponseModel
 */
export interface SchenkerResponseModel {
    /**
     * 
     * @type {string}
     * @memberof SchenkerResponseModel
     */
    'id'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SchenkerResponseModel
     */
    'opCode'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof SchenkerResponseModel
     */
    'transportArea'?: number;
    /**
     * 
     * @type {number}
     * @memberof SchenkerResponseModel
     */
    'processingTime'?: number;
    /**
     * 
     * @type {number}
     * @memberof SchenkerResponseModel
     */
    'protectiveTime'?: number;
    /**
     * 
     * @type {number}
     * @memberof SchenkerResponseModel
     */
    'cityId'?: number;
    /**
     * 
     * @type {string}
     * @memberof SchenkerResponseModel
     */
    'address'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SchenkerResponseModel
     */
    'regionName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SchenkerResponseModel
     */
    'iptuName'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof SchenkerResponseModel
     */
    'localWarehouseId'?: number | null;
    /**
     * 
     * @type {string}
     * @memberof SchenkerResponseModel
     */
    'localWarehouseName'?: string | null;
}
/**
 * 
 * @export
 * @interface SchenkerResponseModelSearchResponseModel
 */
export interface SchenkerResponseModelSearchResponseModel {
    /**
     * 
     * @type {number}
     * @memberof SchenkerResponseModelSearchResponseModel
     */
    'count'?: number;
    /**
     * 
     * @type {Array<SchenkerResponseModel>}
     * @memberof SchenkerResponseModelSearchResponseModel
     */
    'dataCollection'?: Array<SchenkerResponseModel> | null;
}
/**
 * 
 * @export
 * @interface SchenkerSelectItemResponseModel
 */
export interface SchenkerSelectItemResponseModel {
    /**
     * 
     * @type {string}
     * @memberof SchenkerSelectItemResponseModel
     */
    'id'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SchenkerSelectItemResponseModel
     */
    'opCode'?: string | null;
}
/**
 * 
 * @export
 * @interface SearchDataRequestModel
 */
export interface SearchDataRequestModel {
    /**
     * 
     * @type {number}
     * @memberof SearchDataRequestModel
     */
    'pageNumber'?: number;
    /**
     * 
     * @type {number}
     * @memberof SearchDataRequestModel
     */
    'pageSize'?: number;
    /**
     * 
     * @type {string}
     * @memberof SearchDataRequestModel
     */
    'sortBy'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchDataRequestModel
     */
    'sortDir'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchDataRequestModel
     */
    'query'?: string | null;
}
/**
 * 
 * @export
 * @interface SearchProductDataResponseModel
 */
export interface SearchProductDataResponseModel {
    /**
     * 
     * @type {number}
     * @memberof SearchProductDataResponseModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof SearchProductDataResponseModel
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchProductDataResponseModel
     */
    'sapMaterialNum'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchProductDataResponseModel
     */
    'equipmentNameFirst'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchProductDataResponseModel
     */
    'matFirst'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof SearchProductDataResponseModel
     */
    'minimumQuantity'?: number;
    /**
     * 
     * @type {number}
     * @memberof SearchProductDataResponseModel
     */
    'sapRequestType'?: number;
    /**
     * 
     * @type {string}
     * @memberof SearchProductDataResponseModel
     */
    'brProjectName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchProductDataResponseModel
     */
    'sapElementCode'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof SearchProductDataResponseModel
     */
    'equipmentGroupId'?: number;
    /**
     * 
     * @type {number}
     * @memberof SearchProductDataResponseModel
     */
    'unitOfMeasure'?: number;
    /**
     * 
     * @type {number}
     * @memberof SearchProductDataResponseModel
     */
    'boxCapacity'?: number | null;
}
/**
 * 
 * @export
 * @interface SearchProductDataResponseModelSearchResponseModel
 */
export interface SearchProductDataResponseModelSearchResponseModel {
    /**
     * 
     * @type {number}
     * @memberof SearchProductDataResponseModelSearchResponseModel
     */
    'count'?: number;
    /**
     * 
     * @type {Array<SearchProductDataResponseModel>}
     * @memberof SearchProductDataResponseModelSearchResponseModel
     */
    'dataCollection'?: Array<SearchProductDataResponseModel> | null;
}
/**
 * 
 * @export
 * @interface SearchUserDataResponseModel
 */
export interface SearchUserDataResponseModel {
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'id'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'eln'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'firstName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'surname'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'familyName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'fullName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'op'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof SearchUserDataResponseModel
     */
    'regionId'?: number | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'region'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof SearchUserDataResponseModel
     */
    'cityId'?: number;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'city'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'clientNumber'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'phoneNumber'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'displayName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'iptuName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'position'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'adAccount'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'email'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof SearchUserDataResponseModel
     */
    'isMolOfOp'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'opcode'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof SearchUserDataResponseModel
     */
    'schenkerId'?: number | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'blocked'?: SearchUserDataResponseModelBlockedEnum;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'dataBlocked'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchUserDataResponseModel
     */
    'blockedByUserId'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof SearchUserDataResponseModel
     */
    'hasPendingTransfer'?: boolean;
}

export const SearchUserDataResponseModelBlockedEnum = {
    Active: 'Active',
    Blocked: 'Blocked'
} as const;

export type SearchUserDataResponseModelBlockedEnum = typeof SearchUserDataResponseModelBlockedEnum[keyof typeof SearchUserDataResponseModelBlockedEnum];

/**
 * 
 * @export
 * @interface SearchUserDataResponseModelSearchResponseModel
 */
export interface SearchUserDataResponseModelSearchResponseModel {
    /**
     * 
     * @type {number}
     * @memberof SearchUserDataResponseModelSearchResponseModel
     */
    'count'?: number;
    /**
     * 
     * @type {Array<SearchUserDataResponseModel>}
     * @memberof SearchUserDataResponseModelSearchResponseModel
     */
    'dataCollection'?: Array<SearchUserDataResponseModel> | null;
}
/**
 * 
 * @export
 * @interface SubstitutionDataRequestModel
 */
export interface SubstitutionDataRequestModel {
    /**
     * 
     * @type {number}
     * @memberof SubstitutionDataRequestModel
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof SubstitutionDataRequestModel
     */
    'opId'?: number;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionDataRequestModel
     */
    'opCode'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionDataRequestModel
     */
    'fromDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionDataRequestModel
     */
    'toDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionDataRequestModel
     */
    'forUserId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionDataRequestModel
     */
    'forUserFullName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionDataRequestModel
     */
    'substituteUserId'?: string | null;
}
/**
 * 
 * @export
 * @interface SubstitutionsResponseModel
 */
export interface SubstitutionsResponseModel {
    /**
     * 
     * @type {number}
     * @memberof SubstitutionsResponseModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionsResponseModel
     */
    'opCode'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionsResponseModel
     */
    'fromDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionsResponseModel
     */
    'toDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionsResponseModel
     */
    'forUserId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionsResponseModel
     */
    'forUserFullName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionsResponseModel
     */
    'substituteUserId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionsResponseModel
     */
    'substituteUserFullName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionsResponseModel
     */
    'forUserAdAccount'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SubstitutionsResponseModel
     */
    'forSubstituteAdAccount'?: string | null;
}
/**
 * 
 * @export
 * @interface SubstitutionsResponseModelSearchResponseModel
 */
export interface SubstitutionsResponseModelSearchResponseModel {
    /**
     * 
     * @type {number}
     * @memberof SubstitutionsResponseModelSearchResponseModel
     */
    'count'?: number;
    /**
     * 
     * @type {Array<SubstitutionsResponseModel>}
     * @memberof SubstitutionsResponseModelSearchResponseModel
     */
    'dataCollection'?: Array<SubstitutionsResponseModel> | null;
}
/**
 * 
 * @export
 * @interface TransferCorrectionRequest
 */
export interface TransferCorrectionRequest {
    /**
     * 
     * @type {string}
     * @memberof TransferCorrectionRequest
     */
    'equipmentSerialNum': string;
    /**
     * 
     * @type {string}
     * @memberof TransferCorrectionRequest
     */
    'transferTo': string;
    /**
     * 
     * @type {string}
     * @memberof TransferCorrectionRequest
     */
    'serviceId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof TransferCorrectionRequest
     */
    'nameOfTechnician'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof TransferCorrectionRequest
     */
    'userId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof TransferCorrectionRequest
     */
    'deliveryShop'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof TransferCorrectionRequest
     */
    'insertDate'?: string;
}
/**
 * 
 * @export
 * @interface UserDataConciseResponseModel
 */
export interface UserDataConciseResponseModel {
    /**
     * 
     * @type {string}
     * @memberof UserDataConciseResponseModel
     */
    'id'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserDataConciseResponseModel
     */
    'eln'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserDataConciseResponseModel
     */
    'fullName'?: string | null;
}
/**
 * 
 * @export
 * @interface UserDisplayNameResponseModel
 */
export interface UserDisplayNameResponseModel {
    /**
     * 
     * @type {string}
     * @memberof UserDisplayNameResponseModel
     */
    'id'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserDisplayNameResponseModel
     */
    'displayName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserDisplayNameResponseModel
     */
    'iptuName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserDisplayNameResponseModel
     */
    'eln'?: string | null;
}
/**
 * 
 * @export
 * @interface UserItemsToAcceptResponseModel
 */
export interface UserItemsToAcceptResponseModel {
    /**
     * 
     * @type {number}
     * @memberof UserItemsToAcceptResponseModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof UserItemsToAcceptResponseModel
     */
    'equipmentName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserItemsToAcceptResponseModel
     */
    'equipmentValidity'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof UserItemsToAcceptResponseModel
     */
    'typeOfUsage'?: number;
    /**
     * 
     * @type {string}
     * @memberof UserItemsToAcceptResponseModel
     */
    'fromUserId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserItemsToAcceptResponseModel
     */
    'fromUserFullName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserItemsToAcceptResponseModel
     */
    'insertDate'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserItemsToAcceptResponseModel
     */
    'equipmentSerialNum'?: string | null;
}
/**
 * 
 * @export
 * @interface UserItemsToAcceptResponseModelSearchResponseModel
 */
export interface UserItemsToAcceptResponseModelSearchResponseModel {
    /**
     * 
     * @type {number}
     * @memberof UserItemsToAcceptResponseModelSearchResponseModel
     */
    'count'?: number;
    /**
     * 
     * @type {Array<UserItemsToAcceptResponseModel>}
     * @memberof UserItemsToAcceptResponseModelSearchResponseModel
     */
    'dataCollection'?: Array<UserItemsToAcceptResponseModel> | null;
}
/**
 * 
 * @export
 * @interface UserItemsToCancelResponseModel
 */
export interface UserItemsToCancelResponseModel {
    /**
     * 
     * @type {number}
     * @memberof UserItemsToCancelResponseModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof UserItemsToCancelResponseModel
     */
    'intDeliveryNum'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserItemsToCancelResponseModel
     */
    'equipmentName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserItemsToCancelResponseModel
     */
    'toUserId'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserItemsToCancelResponseModel
     */
    'toUserFullName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserItemsToCancelResponseModel
     */
    'insertDate'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserItemsToCancelResponseModel
     */
    'equipmentSerialNum'?: string | null;
}
/**
 * 
 * @export
 * @interface UserItemsToCancelResponseModelSearchResponseModel
 */
export interface UserItemsToCancelResponseModelSearchResponseModel {
    /**
     * 
     * @type {number}
     * @memberof UserItemsToCancelResponseModelSearchResponseModel
     */
    'count'?: number;
    /**
     * 
     * @type {Array<UserItemsToCancelResponseModel>}
     * @memberof UserItemsToCancelResponseModelSearchResponseModel
     */
    'dataCollection'?: Array<UserItemsToCancelResponseModel> | null;
}
/**
 * 
 * @export
 * @interface UserLoginRequestModel
 */
export interface UserLoginRequestModel {
    /**
     * 
     * @type {string}
     * @memberof UserLoginRequestModel
     */
    'username': string;
    /**
     * 
     * @type {string}
     * @memberof UserLoginRequestModel
     */
    'password': string;
}
/**
 * 
 * @export
 * @interface UserLoginResponseModel
 */
export interface UserLoginResponseModel {
    /**
     * 
     * @type {Array<string>}
     * @memberof UserLoginResponseModel
     */
    'userRoles'?: Array<string> | null;
    /**
     * 
     * @type {string}
     * @memberof UserLoginResponseModel
     */
    'username'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserLoginResponseModel
     */
    'profilePicture'?: string | null;
}
/**
 * 
 * @export
 * @interface UsersListResponseModel
 */
export interface UsersListResponseModel {
    /**
     * 
     * @type {{ [key: string]: Array<UsersSelectListResponseModel>; }}
     * @memberof UsersListResponseModel
     */
    'usersSelectListResponseModels'?: { [key: string]: Array<UsersSelectListResponseModel>; } | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof UsersListResponseModel
     */
    'iptuListResponseModel'?: Array<string> | null;
}
/**
 * 
 * @export
 * @interface UsersSelectListResponseModel
 */
export interface UsersSelectListResponseModel {
    /**
     * 
     * @type {string}
     * @memberof UsersSelectListResponseModel
     */
    'id'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UsersSelectListResponseModel
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface UsersWithOPCodeResponse
 */
export interface UsersWithOPCodeResponse {
    /**
     * 
     * @type {string}
     * @memberof UsersWithOPCodeResponse
     */
    'id'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UsersWithOPCodeResponse
     */
    'displayName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UsersWithOPCodeResponse
     */
    'iptuName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UsersWithOPCodeResponse
     */
    'opcode'?: string | null;
}
/**
 * 
 * @export
 * @interface ValidationProblemDetails
 */
export interface ValidationProblemDetails {
    [key: string]: any;

    /**
     * 
     * @type {string}
     * @memberof ValidationProblemDetails
     */
    'type'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ValidationProblemDetails
     */
    'title'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof ValidationProblemDetails
     */
    'status'?: number | null;
    /**
     * 
     * @type {string}
     * @memberof ValidationProblemDetails
     */
    'detail'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ValidationProblemDetails
     */
    'instance'?: string | null;
    /**
     * 
     * @type {{ [key: string]: Array<string>; }}
     * @memberof ValidationProblemDetails
     */
    'errors'?: { [key: string]: Array<string>; } | null;
}
/**
 * 
 * @export
 * @interface WarehousesResponseModel
 */
export interface WarehousesResponseModel {
    /**
     * 
     * @type {number}
     * @memberof WarehousesResponseModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof WarehousesResponseModel
     */
    'name'?: string | null;
}

/**
 * AccountApi - axios parameter creator
 * @export
 */
export const AccountApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiAccountDeleteImpersonationCookieGet: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/account/delete-impersonation-cookie`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {UserLoginRequestModel} userLoginRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiAccountLoginPost: async (userLoginRequestModel: UserLoginRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userLoginRequestModel' is not null or undefined
            assertParamExists('apiAccountLoginPost', 'userLoginRequestModel', userLoginRequestModel)
            const localVarPath = `/api/account/login`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(userLoginRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiAccountLogoutPost: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/account/logout`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiAccountSetImpersonationCookieGet: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/account/set-impersonation-cookie`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AccountApi - functional programming interface
 * @export
 */
export const AccountApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AccountApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiAccountDeleteImpersonationCookieGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BaseResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiAccountDeleteImpersonationCookieGet(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AccountApi.apiAccountDeleteImpersonationCookieGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {UserLoginRequestModel} userLoginRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiAccountLoginPost(userLoginRequestModel: UserLoginRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserLoginResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiAccountLoginPost(userLoginRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AccountApi.apiAccountLoginPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiAccountLogoutPost(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BaseResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiAccountLogoutPost(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AccountApi.apiAccountLogoutPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiAccountSetImpersonationCookieGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BaseResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiAccountSetImpersonationCookieGet(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AccountApi.apiAccountSetImpersonationCookieGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AccountApi - factory interface
 * @export
 */
export const AccountApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AccountApiFp(configuration)
    return {
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiAccountDeleteImpersonationCookieGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<BaseResponseModel> {
            return localVarFp.apiAccountDeleteImpersonationCookieGet(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {UserLoginRequestModel} userLoginRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiAccountLoginPost(userLoginRequestModel: UserLoginRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<UserLoginResponseModel> {
            return localVarFp.apiAccountLoginPost(userLoginRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiAccountLogoutPost(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<BaseResponseModel> {
            return localVarFp.apiAccountLogoutPost(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiAccountSetImpersonationCookieGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<BaseResponseModel> {
            return localVarFp.apiAccountSetImpersonationCookieGet(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AccountApi - object-oriented interface
 * @export
 * @class AccountApi
 * @extends {BaseAPI}
 */
export class AccountApi extends BaseAPI {
    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AccountApi
     */
    public apiAccountDeleteImpersonationCookieGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return AccountApiFp(this.configuration).apiAccountDeleteImpersonationCookieGet(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {UserLoginRequestModel} userLoginRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AccountApi
     */
    public apiAccountLoginPost(userLoginRequestModel: UserLoginRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return AccountApiFp(this.configuration).apiAccountLoginPost(userLoginRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AccountApi
     */
    public apiAccountLogoutPost(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return AccountApiFp(this.configuration).apiAccountLogoutPost(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AccountApi
     */
    public apiAccountSetImpersonationCookieGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return AccountApiFp(this.configuration).apiAccountSetImpersonationCookieGet(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * CitiesApi - axios parameter creator
 * @export
 */
export const CitiesApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Activates selected cities in the database.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {Array<number>} [requestBody] The ids of the cities, that needs to be activated.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesActivatePut: async (inputRequestId?: string, inputTimestamp?: string, requestBody?: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/cities/activate`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(requestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Adds city to the database.
         * @param {CityRequestModel} cityRequestModel The city data to save in the database.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesAddPost: async (cityRequestModel: CityRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'cityRequestModel' is not null or undefined
            assertParamExists('apiCitiesAddPost', 'cityRequestModel', cityRequestModel)
            const localVarPath = `/api/cities/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(cityRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Blocks selected cities in the database.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {Array<number>} [requestBody] The ids of the cities, that needs to be blocked.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesBlockPut: async (inputRequestId?: string, inputTimestamp?: string, requestBody?: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/cities/block`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(requestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Gets city data by its id.
         * @param {number} id The id of the city.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesByIdGet: async (id: number, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('apiCitiesByIdGet', 'id', id)
            const localVarPath = `/api/cities/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Returns the region of the city as string.
         * @param {number} id The id of the city.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesRegionByIdGet: async (id: number, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('apiCitiesRegionByIdGet', 'id', id)
            const localVarPath = `/api/cities/region/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Gets a city by its id (request parameter) and returns the SAPCityCode and Cluster of the city.
         * @param {number} cityId The id of the city.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesSapCityCodeAndClusterByCityIdGet: async (cityId: number, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'cityId' is not null or undefined
            assertParamExists('apiCitiesSapCityCodeAndClusterByCityIdGet', 'cityId', cityId)
            const localVarPath = `/api/cities/sap-city-code-and-cluster/{cityId}`
                .replace(`{${"cityId"}}`, encodeURIComponent(String(cityId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Search cities based on criterias.
         * @param {SearchDataRequestModel} searchDataRequestModel The search parameters of the request.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesSearchPost: async (searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'searchDataRequestModel' is not null or undefined
            assertParamExists('apiCitiesSearchPost', 'searchDataRequestModel', searchDataRequestModel)
            const localVarPath = `/api/cities/search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(searchDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesSelectListGet: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/cities/select-list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Updates an existing city in the databse.
         * @param {number} id The id of the city, being updated.
         * @param {CityRequestModel} cityRequestModel The new city data.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesUpdateByIdPut: async (id: number, cityRequestModel: CityRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('apiCitiesUpdateByIdPut', 'id', id)
            // verify required parameter 'cityRequestModel' is not null or undefined
            assertParamExists('apiCitiesUpdateByIdPut', 'cityRequestModel', cityRequestModel)
            const localVarPath = `/api/cities/update/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(cityRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CitiesApi - functional programming interface
 * @export
 */
export const CitiesApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CitiesApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Activates selected cities in the database.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {Array<number>} [requestBody] The ids of the cities, that needs to be activated.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCitiesActivatePut(inputRequestId?: string, inputTimestamp?: string, requestBody?: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiCitiesActivatePut(inputRequestId, inputTimestamp, requestBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CitiesApi.apiCitiesActivatePut']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Adds city to the database.
         * @param {CityRequestModel} cityRequestModel The city data to save in the database.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCitiesAddPost(cityRequestModel: CityRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<number>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiCitiesAddPost(cityRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CitiesApi.apiCitiesAddPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Blocks selected cities in the database.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {Array<number>} [requestBody] The ids of the cities, that needs to be blocked.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCitiesBlockPut(inputRequestId?: string, inputTimestamp?: string, requestBody?: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiCitiesBlockPut(inputRequestId, inputTimestamp, requestBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CitiesApi.apiCitiesBlockPut']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Gets city data by its id.
         * @param {number} id The id of the city.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCitiesByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CityResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiCitiesByIdGet(id, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CitiesApi.apiCitiesByIdGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Returns the region of the city as string.
         * @param {number} id The id of the city.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCitiesRegionByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiCitiesRegionByIdGet(id, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CitiesApi.apiCitiesRegionByIdGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Gets a city by its id (request parameter) and returns the SAPCityCode and Cluster of the city.
         * @param {number} cityId The id of the city.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCitiesSapCityCodeAndClusterByCityIdGet(cityId: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SapCityCodesResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiCitiesSapCityCodeAndClusterByCityIdGet(cityId, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CitiesApi.apiCitiesSapCityCodeAndClusterByCityIdGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Search cities based on criterias.
         * @param {SearchDataRequestModel} searchDataRequestModel The search parameters of the request.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCitiesSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CityResponseModelSearchResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiCitiesSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CitiesApi.apiCitiesSearchPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCitiesSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<CitiesSelectItemResponseModel>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiCitiesSelectListGet(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CitiesApi.apiCitiesSelectListGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Updates an existing city in the databse.
         * @param {number} id The id of the city, being updated.
         * @param {CityRequestModel} cityRequestModel The new city data.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiCitiesUpdateByIdPut(id: number, cityRequestModel: CityRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiCitiesUpdateByIdPut(id, cityRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CitiesApi.apiCitiesUpdateByIdPut']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CitiesApi - factory interface
 * @export
 */
export const CitiesApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CitiesApiFp(configuration)
    return {
        /**
         * 
         * @summary Activates selected cities in the database.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {Array<number>} [requestBody] The ids of the cities, that needs to be activated.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesActivatePut(inputRequestId?: string, inputTimestamp?: string, requestBody?: Array<number>, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiCitiesActivatePut(inputRequestId, inputTimestamp, requestBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Adds city to the database.
         * @param {CityRequestModel} cityRequestModel The city data to save in the database.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesAddPost(cityRequestModel: CityRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<number> {
            return localVarFp.apiCitiesAddPost(cityRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Blocks selected cities in the database.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {Array<number>} [requestBody] The ids of the cities, that needs to be blocked.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesBlockPut(inputRequestId?: string, inputTimestamp?: string, requestBody?: Array<number>, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiCitiesBlockPut(inputRequestId, inputTimestamp, requestBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Gets city data by its id.
         * @param {number} id The id of the city.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<CityResponseModel> {
            return localVarFp.apiCitiesByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Returns the region of the city as string.
         * @param {number} id The id of the city.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesRegionByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.apiCitiesRegionByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Gets a city by its id (request parameter) and returns the SAPCityCode and Cluster of the city.
         * @param {number} cityId The id of the city.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesSapCityCodeAndClusterByCityIdGet(cityId: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<SapCityCodesResponseModel> {
            return localVarFp.apiCitiesSapCityCodeAndClusterByCityIdGet(cityId, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Search cities based on criterias.
         * @param {SearchDataRequestModel} searchDataRequestModel The search parameters of the request.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<CityResponseModelSearchResponseModel> {
            return localVarFp.apiCitiesSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<CitiesSelectItemResponseModel>> {
            return localVarFp.apiCitiesSelectListGet(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Updates an existing city in the databse.
         * @param {number} id The id of the city, being updated.
         * @param {CityRequestModel} cityRequestModel The new city data.
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCitiesUpdateByIdPut(id: number, cityRequestModel: CityRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiCitiesUpdateByIdPut(id, cityRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * CitiesApi - object-oriented interface
 * @export
 * @class CitiesApi
 * @extends {BaseAPI}
 */
export class CitiesApi extends BaseAPI {
    /**
     * 
     * @summary Activates selected cities in the database.
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {Array<number>} [requestBody] The ids of the cities, that needs to be activated.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CitiesApi
     */
    public apiCitiesActivatePut(inputRequestId?: string, inputTimestamp?: string, requestBody?: Array<number>, options?: RawAxiosRequestConfig) {
        return CitiesApiFp(this.configuration).apiCitiesActivatePut(inputRequestId, inputTimestamp, requestBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Adds city to the database.
     * @param {CityRequestModel} cityRequestModel The city data to save in the database.
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CitiesApi
     */
    public apiCitiesAddPost(cityRequestModel: CityRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return CitiesApiFp(this.configuration).apiCitiesAddPost(cityRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Blocks selected cities in the database.
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {Array<number>} [requestBody] The ids of the cities, that needs to be blocked.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CitiesApi
     */
    public apiCitiesBlockPut(inputRequestId?: string, inputTimestamp?: string, requestBody?: Array<number>, options?: RawAxiosRequestConfig) {
        return CitiesApiFp(this.configuration).apiCitiesBlockPut(inputRequestId, inputTimestamp, requestBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Gets city data by its id.
     * @param {number} id The id of the city.
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CitiesApi
     */
    public apiCitiesByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return CitiesApiFp(this.configuration).apiCitiesByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Returns the region of the city as string.
     * @param {number} id The id of the city.
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CitiesApi
     */
    public apiCitiesRegionByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return CitiesApiFp(this.configuration).apiCitiesRegionByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Gets a city by its id (request parameter) and returns the SAPCityCode and Cluster of the city.
     * @param {number} cityId The id of the city.
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CitiesApi
     */
    public apiCitiesSapCityCodeAndClusterByCityIdGet(cityId: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return CitiesApiFp(this.configuration).apiCitiesSapCityCodeAndClusterByCityIdGet(cityId, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Search cities based on criterias.
     * @param {SearchDataRequestModel} searchDataRequestModel The search parameters of the request.
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CitiesApi
     */
    public apiCitiesSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return CitiesApiFp(this.configuration).apiCitiesSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CitiesApi
     */
    public apiCitiesSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return CitiesApiFp(this.configuration).apiCitiesSelectListGet(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Updates an existing city in the databse.
     * @param {number} id The id of the city, being updated.
     * @param {CityRequestModel} cityRequestModel The new city data.
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CitiesApi
     */
    public apiCitiesUpdateByIdPut(id: number, cityRequestModel: CityRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return CitiesApiFp(this.configuration).apiCitiesUpdateByIdPut(id, cityRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * EquipmentApi - axios parameter creator
 * @export
 */
export const EquipmentApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {AcceptItemsForTransferRequestModel} acceptItemsForTransferRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentAcceptItemsPost: async (acceptItemsForTransferRequestModel: AcceptItemsForTransferRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'acceptItemsForTransferRequestModel' is not null or undefined
            assertParamExists('apiEquipmentAcceptItemsPost', 'acceptItemsForTransferRequestModel', acceptItemsForTransferRequestModel)
            const localVarPath = `/api/equipment/accept-items`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(acceptItemsForTransferRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {CancelItemsForTransferRequestModel} cancelItemsForTransferRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentCancelItemsPost: async (cancelItemsForTransferRequestModel: CancelItemsForTransferRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'cancelItemsForTransferRequestModel' is not null or undefined
            assertParamExists('apiEquipmentCancelItemsPost', 'cancelItemsForTransferRequestModel', cancelItemsForTransferRequestModel)
            const localVarPath = `/api/equipment/cancel-items`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(cancelItemsForTransferRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {DeliverItemsDataRequestModel} deliverItemsDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentDeliverItemsPost: async (deliverItemsDataRequestModel: DeliverItemsDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'deliverItemsDataRequestModel' is not null or undefined
            assertParamExists('apiEquipmentDeliverItemsPost', 'deliverItemsDataRequestModel', deliverItemsDataRequestModel)
            const localVarPath = `/api/equipment/deliver-items`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(deliverItemsDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentPostOfficesSelectListGet: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/equipment/post-offices-select-list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {RefuseItemsForTransferRequestModel} refuseItemsForTransferRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentRefuseItemsPost: async (refuseItemsForTransferRequestModel: RefuseItemsForTransferRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'refuseItemsForTransferRequestModel' is not null or undefined
            assertParamExists('apiEquipmentRefuseItemsPost', 'refuseItemsForTransferRequestModel', refuseItemsForTransferRequestModel)
            const localVarPath = `/api/equipment/refuse-items`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(refuseItemsForTransferRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentRemoveAllItemsPost: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/equipment/remove-all-items`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} itemId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentRemoveItemPost: async (itemId: number, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'itemId' is not null or undefined
            assertParamExists('apiEquipmentRemoveItemPost', 'itemId', itemId)
            const localVarPath = `/api/equipment/remove-item`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (itemId !== undefined) {
                localVarQueryParameter['itemId'] = itemId;
            }


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {Array<number>} requestBody 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentRemoveItemsPost: async (requestBody: Array<number>, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'requestBody' is not null or undefined
            assertParamExists('apiEquipmentRemoveItemsPost', 'requestBody', requestBody)
            const localVarPath = `/api/equipment/remove-items`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(requestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentReserveAllItemsPost: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/equipment/reserve-all-items`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {ReserveItemForTransferDataRequestModel} reserveItemForTransferDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentReserveItemPost: async (reserveItemForTransferDataRequestModel: ReserveItemForTransferDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'reserveItemForTransferDataRequestModel' is not null or undefined
            assertParamExists('apiEquipmentReserveItemPost', 'reserveItemForTransferDataRequestModel', reserveItemForTransferDataRequestModel)
            const localVarPath = `/api/equipment/reserve-item`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(reserveItemForTransferDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentReservedItemsCountGet: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/equipment/reserved-items-count`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentSearchAvailableEquipmentPost: async (searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'searchDataRequestModel' is not null or undefined
            assertParamExists('apiEquipmentSearchAvailableEquipmentPost', 'searchDataRequestModel', searchDataRequestModel)
            const localVarPath = `/api/equipment/search-available-equipment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(searchDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentSearchTransferDataPost: async (searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'searchDataRequestModel' is not null or undefined
            assertParamExists('apiEquipmentSearchTransferDataPost', 'searchDataRequestModel', searchDataRequestModel)
            const localVarPath = `/api/equipment/search-transfer-data`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(searchDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentSearchUserItemsToAcceptPost: async (searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'searchDataRequestModel' is not null or undefined
            assertParamExists('apiEquipmentSearchUserItemsToAcceptPost', 'searchDataRequestModel', searchDataRequestModel)
            const localVarPath = `/api/equipment/search-user-items-to-accept`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(searchDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentSearchUserItemsToCancelPost: async (searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'searchDataRequestModel' is not null or undefined
            assertParamExists('apiEquipmentSearchUserItemsToCancelPost', 'searchDataRequestModel', searchDataRequestModel)
            const localVarPath = `/api/equipment/search-user-items-to-cancel`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(searchDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * EquipmentApi - functional programming interface
 * @export
 */
export const EquipmentApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = EquipmentApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {AcceptItemsForTransferRequestModel} acceptItemsForTransferRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentAcceptItemsPost(acceptItemsForTransferRequestModel: AcceptItemsForTransferRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentAcceptItemsPost(acceptItemsForTransferRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentAcceptItemsPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {CancelItemsForTransferRequestModel} cancelItemsForTransferRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentCancelItemsPost(cancelItemsForTransferRequestModel: CancelItemsForTransferRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentCancelItemsPost(cancelItemsForTransferRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentCancelItemsPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {DeliverItemsDataRequestModel} deliverItemsDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentDeliverItemsPost(deliverItemsDataRequestModel: DeliverItemsDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentDeliverItemsPost(deliverItemsDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentDeliverItemsPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentPostOfficesSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<PostOfficesSelectListResponseModel>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentPostOfficesSelectListGet(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentPostOfficesSelectListGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {RefuseItemsForTransferRequestModel} refuseItemsForTransferRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentRefuseItemsPost(refuseItemsForTransferRequestModel: RefuseItemsForTransferRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentRefuseItemsPost(refuseItemsForTransferRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentRefuseItemsPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentRemoveAllItemsPost(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<number>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentRemoveAllItemsPost(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentRemoveAllItemsPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} itemId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentRemoveItemPost(itemId: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<RemoveItemFromTransferResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentRemoveItemPost(itemId, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentRemoveItemPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {Array<number>} requestBody 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentRemoveItemsPost(requestBody: Array<number>, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentRemoveItemsPost(requestBody, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentRemoveItemsPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentReserveAllItemsPost(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<number>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentReserveAllItemsPost(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentReserveAllItemsPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {ReserveItemForTransferDataRequestModel} reserveItemForTransferDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentReserveItemPost(reserveItemForTransferDataRequestModel: ReserveItemForTransferDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ReserveItemHistoryResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentReserveItemPost(reserveItemForTransferDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentReserveItemPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentReservedItemsCountGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<number>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentReservedItemsCountGet(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentReservedItemsCountGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentSearchAvailableEquipmentPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AvailableEquipmentDataResponseModelSearchResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentSearchAvailableEquipmentPost(searchDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentSearchAvailableEquipmentPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentSearchTransferDataPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ItemDataResponseModelSearchResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentSearchTransferDataPost(searchDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentSearchTransferDataPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentSearchUserItemsToAcceptPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserItemsToAcceptResponseModelSearchResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentSearchUserItemsToAcceptPost(searchDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentSearchUserItemsToAcceptPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentSearchUserItemsToCancelPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserItemsToCancelResponseModelSearchResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentSearchUserItemsToCancelPost(searchDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentApi.apiEquipmentSearchUserItemsToCancelPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * EquipmentApi - factory interface
 * @export
 */
export const EquipmentApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = EquipmentApiFp(configuration)
    return {
        /**
         * 
         * @param {AcceptItemsForTransferRequestModel} acceptItemsForTransferRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentAcceptItemsPost(acceptItemsForTransferRequestModel: AcceptItemsForTransferRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiEquipmentAcceptItemsPost(acceptItemsForTransferRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {CancelItemsForTransferRequestModel} cancelItemsForTransferRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentCancelItemsPost(cancelItemsForTransferRequestModel: CancelItemsForTransferRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiEquipmentCancelItemsPost(cancelItemsForTransferRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {DeliverItemsDataRequestModel} deliverItemsDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentDeliverItemsPost(deliverItemsDataRequestModel: DeliverItemsDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiEquipmentDeliverItemsPost(deliverItemsDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentPostOfficesSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<PostOfficesSelectListResponseModel>> {
            return localVarFp.apiEquipmentPostOfficesSelectListGet(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {RefuseItemsForTransferRequestModel} refuseItemsForTransferRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentRefuseItemsPost(refuseItemsForTransferRequestModel: RefuseItemsForTransferRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiEquipmentRefuseItemsPost(refuseItemsForTransferRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentRemoveAllItemsPost(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<number> {
            return localVarFp.apiEquipmentRemoveAllItemsPost(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} itemId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentRemoveItemPost(itemId: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<RemoveItemFromTransferResponseModel> {
            return localVarFp.apiEquipmentRemoveItemPost(itemId, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {Array<number>} requestBody 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentRemoveItemsPost(requestBody: Array<number>, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiEquipmentRemoveItemsPost(requestBody, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentReserveAllItemsPost(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<number> {
            return localVarFp.apiEquipmentReserveAllItemsPost(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {ReserveItemForTransferDataRequestModel} reserveItemForTransferDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentReserveItemPost(reserveItemForTransferDataRequestModel: ReserveItemForTransferDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<ReserveItemHistoryResponseModel> {
            return localVarFp.apiEquipmentReserveItemPost(reserveItemForTransferDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentReservedItemsCountGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<number> {
            return localVarFp.apiEquipmentReservedItemsCountGet(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentSearchAvailableEquipmentPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<AvailableEquipmentDataResponseModelSearchResponseModel> {
            return localVarFp.apiEquipmentSearchAvailableEquipmentPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentSearchTransferDataPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<ItemDataResponseModelSearchResponseModel> {
            return localVarFp.apiEquipmentSearchTransferDataPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentSearchUserItemsToAcceptPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<UserItemsToAcceptResponseModelSearchResponseModel> {
            return localVarFp.apiEquipmentSearchUserItemsToAcceptPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentSearchUserItemsToCancelPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<UserItemsToCancelResponseModelSearchResponseModel> {
            return localVarFp.apiEquipmentSearchUserItemsToCancelPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * EquipmentApi - object-oriented interface
 * @export
 * @class EquipmentApi
 * @extends {BaseAPI}
 */
export class EquipmentApi extends BaseAPI {
    /**
     * 
     * @param {AcceptItemsForTransferRequestModel} acceptItemsForTransferRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentAcceptItemsPost(acceptItemsForTransferRequestModel: AcceptItemsForTransferRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentAcceptItemsPost(acceptItemsForTransferRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {CancelItemsForTransferRequestModel} cancelItemsForTransferRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentCancelItemsPost(cancelItemsForTransferRequestModel: CancelItemsForTransferRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentCancelItemsPost(cancelItemsForTransferRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {DeliverItemsDataRequestModel} deliverItemsDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentDeliverItemsPost(deliverItemsDataRequestModel: DeliverItemsDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentDeliverItemsPost(deliverItemsDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentPostOfficesSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentPostOfficesSelectListGet(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {RefuseItemsForTransferRequestModel} refuseItemsForTransferRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentRefuseItemsPost(refuseItemsForTransferRequestModel: RefuseItemsForTransferRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentRefuseItemsPost(refuseItemsForTransferRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentRemoveAllItemsPost(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentRemoveAllItemsPost(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} itemId 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentRemoveItemPost(itemId: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentRemoveItemPost(itemId, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {Array<number>} requestBody 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentRemoveItemsPost(requestBody: Array<number>, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentRemoveItemsPost(requestBody, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentReserveAllItemsPost(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentReserveAllItemsPost(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {ReserveItemForTransferDataRequestModel} reserveItemForTransferDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentReserveItemPost(reserveItemForTransferDataRequestModel: ReserveItemForTransferDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentReserveItemPost(reserveItemForTransferDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentReservedItemsCountGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentReservedItemsCountGet(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {SearchDataRequestModel} searchDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentSearchAvailableEquipmentPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentSearchAvailableEquipmentPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {SearchDataRequestModel} searchDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentSearchTransferDataPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentSearchTransferDataPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {SearchDataRequestModel} searchDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentSearchUserItemsToAcceptPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentSearchUserItemsToAcceptPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {SearchDataRequestModel} searchDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentApi
     */
    public apiEquipmentSearchUserItemsToCancelPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentApiFp(this.configuration).apiEquipmentSearchUserItemsToCancelPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * EquipmentGroupApi - axios parameter creator
 * @export
 */
export const EquipmentGroupApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentGroupAllGet: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/equipment-group/all`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentGroupAllNamesGet: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/equipment-group/all/names`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentGroupByIdByIdGet: async (id: number, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('apiEquipmentGroupByIdByIdGet', 'id', id)
            const localVarPath = `/api/equipment-group/by-id/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} sapMaterialNum 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentGroupNameBySapMaterialNumGet: async (sapMaterialNum: string, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'sapMaterialNum' is not null or undefined
            assertParamExists('apiEquipmentGroupNameBySapMaterialNumGet', 'sapMaterialNum', sapMaterialNum)
            const localVarPath = `/api/equipment-group/name/{sapMaterialNum}`
                .replace(`{${"sapMaterialNum"}}`, encodeURIComponent(String(sapMaterialNum)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * EquipmentGroupApi - functional programming interface
 * @export
 */
export const EquipmentGroupApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = EquipmentGroupApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentGroupAllGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<EquipmentGroupResponseModel>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentGroupAllGet(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentGroupApi.apiEquipmentGroupAllGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentGroupAllNamesGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<string>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentGroupAllNamesGet(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentGroupApi.apiEquipmentGroupAllNamesGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentGroupByIdByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<EquipmentGroupResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentGroupByIdByIdGet(id, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentGroupApi.apiEquipmentGroupByIdByIdGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} sapMaterialNum 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentGroupNameBySapMaterialNumGet(sapMaterialNum: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<string>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentGroupNameBySapMaterialNumGet(sapMaterialNum, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentGroupApi.apiEquipmentGroupNameBySapMaterialNumGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * EquipmentGroupApi - factory interface
 * @export
 */
export const EquipmentGroupApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = EquipmentGroupApiFp(configuration)
    return {
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentGroupAllGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<EquipmentGroupResponseModel>> {
            return localVarFp.apiEquipmentGroupAllGet(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentGroupAllNamesGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<string>> {
            return localVarFp.apiEquipmentGroupAllNamesGet(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentGroupByIdByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<EquipmentGroupResponseModel> {
            return localVarFp.apiEquipmentGroupByIdByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} sapMaterialNum 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentGroupNameBySapMaterialNumGet(sapMaterialNum: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<string>> {
            return localVarFp.apiEquipmentGroupNameBySapMaterialNumGet(sapMaterialNum, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * EquipmentGroupApi - object-oriented interface
 * @export
 * @class EquipmentGroupApi
 * @extends {BaseAPI}
 */
export class EquipmentGroupApi extends BaseAPI {
    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentGroupApi
     */
    public apiEquipmentGroupAllGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentGroupApiFp(this.configuration).apiEquipmentGroupAllGet(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentGroupApi
     */
    public apiEquipmentGroupAllNamesGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentGroupApiFp(this.configuration).apiEquipmentGroupAllNamesGet(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentGroupApi
     */
    public apiEquipmentGroupByIdByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentGroupApiFp(this.configuration).apiEquipmentGroupByIdByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} sapMaterialNum 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentGroupApi
     */
    public apiEquipmentGroupNameBySapMaterialNumGet(sapMaterialNum: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentGroupApiFp(this.configuration).apiEquipmentGroupNameBySapMaterialNumGet(sapMaterialNum, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * EquipmentTypeApi - axios parameter creator
 * @export
 */
export const EquipmentTypeApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentTypeAllGet: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/equipment-type/all`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentTypeByIdByIdGet: async (id: number, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('apiEquipmentTypeByIdByIdGet', 'id', id)
            const localVarPath = `/api/equipment-type/by-id/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} sendMethod 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentTypeBySendMethodBySendMethodGet: async (sendMethod: string, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'sendMethod' is not null or undefined
            assertParamExists('apiEquipmentTypeBySendMethodBySendMethodGet', 'sendMethod', sendMethod)
            const localVarPath = `/api/equipment-type/by-send-method/{sendMethod}`
                .replace(`{${"sendMethod"}}`, encodeURIComponent(String(sendMethod)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {ConciseEquipmentTypesDataRequestModel} conciseEquipmentTypesDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentTypeConciseEquipmentTypesPost: async (conciseEquipmentTypesDataRequestModel: ConciseEquipmentTypesDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'conciseEquipmentTypesDataRequestModel' is not null or undefined
            assertParamExists('apiEquipmentTypeConciseEquipmentTypesPost', 'conciseEquipmentTypesDataRequestModel', conciseEquipmentTypesDataRequestModel)
            const localVarPath = `/api/equipment-type/concise-equipment-types`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(conciseEquipmentTypesDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentTypeLastMonthQuantitiesGet: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/equipment-type/last-month-quantities`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} sapMaterialNum 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet: async (sapMaterialNum: string, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'sapMaterialNum' is not null or undefined
            assertParamExists('apiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet', 'sapMaterialNum', sapMaterialNum)
            const localVarPath = `/api/equipment-type/type-id-by-sap-material-num/{sapMaterialNum}`
                .replace(`{${"sapMaterialNum"}}`, encodeURIComponent(String(sapMaterialNum)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * EquipmentTypeApi - functional programming interface
 * @export
 */
export const EquipmentTypeApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = EquipmentTypeApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentTypeAllGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<EquipmentTypeResponseModel>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentTypeAllGet(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentTypeApi.apiEquipmentTypeAllGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentTypeByIdByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<EquipmentTypeResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentTypeByIdByIdGet(id, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentTypeApi.apiEquipmentTypeByIdByIdGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} sendMethod 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentTypeBySendMethodBySendMethodGet(sendMethod: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<number>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentTypeBySendMethodBySendMethodGet(sendMethod, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentTypeApi.apiEquipmentTypeBySendMethodBySendMethodGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {ConciseEquipmentTypesDataRequestModel} conciseEquipmentTypesDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentTypeConciseEquipmentTypesPost(conciseEquipmentTypesDataRequestModel: ConciseEquipmentTypesDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<EquipmentTypeConciseDto>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentTypeConciseEquipmentTypesPost(conciseEquipmentTypesDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentTypeApi.apiEquipmentTypeConciseEquipmentTypesPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentTypeLastMonthQuantitiesGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<{ [key: string]: number; }>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentTypeLastMonthQuantitiesGet(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentTypeApi.apiEquipmentTypeLastMonthQuantitiesGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} sapMaterialNum 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet(sapMaterialNum: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<number>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet(sapMaterialNum, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EquipmentTypeApi.apiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * EquipmentTypeApi - factory interface
 * @export
 */
export const EquipmentTypeApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = EquipmentTypeApiFp(configuration)
    return {
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentTypeAllGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<EquipmentTypeResponseModel>> {
            return localVarFp.apiEquipmentTypeAllGet(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentTypeByIdByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<EquipmentTypeResponseModel> {
            return localVarFp.apiEquipmentTypeByIdByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} sendMethod 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentTypeBySendMethodBySendMethodGet(sendMethod: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<number> {
            return localVarFp.apiEquipmentTypeBySendMethodBySendMethodGet(sendMethod, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {ConciseEquipmentTypesDataRequestModel} conciseEquipmentTypesDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentTypeConciseEquipmentTypesPost(conciseEquipmentTypesDataRequestModel: ConciseEquipmentTypesDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<EquipmentTypeConciseDto>> {
            return localVarFp.apiEquipmentTypeConciseEquipmentTypesPost(conciseEquipmentTypesDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentTypeLastMonthQuantitiesGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<{ [key: string]: number; }> {
            return localVarFp.apiEquipmentTypeLastMonthQuantitiesGet(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} sapMaterialNum 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet(sapMaterialNum: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<number> {
            return localVarFp.apiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet(sapMaterialNum, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * EquipmentTypeApi - object-oriented interface
 * @export
 * @class EquipmentTypeApi
 * @extends {BaseAPI}
 */
export class EquipmentTypeApi extends BaseAPI {
    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentTypeApi
     */
    public apiEquipmentTypeAllGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentTypeApiFp(this.configuration).apiEquipmentTypeAllGet(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentTypeApi
     */
    public apiEquipmentTypeByIdByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentTypeApiFp(this.configuration).apiEquipmentTypeByIdByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} sendMethod 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentTypeApi
     */
    public apiEquipmentTypeBySendMethodBySendMethodGet(sendMethod: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentTypeApiFp(this.configuration).apiEquipmentTypeBySendMethodBySendMethodGet(sendMethod, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {ConciseEquipmentTypesDataRequestModel} conciseEquipmentTypesDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentTypeApi
     */
    public apiEquipmentTypeConciseEquipmentTypesPost(conciseEquipmentTypesDataRequestModel: ConciseEquipmentTypesDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentTypeApiFp(this.configuration).apiEquipmentTypeConciseEquipmentTypesPost(conciseEquipmentTypesDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentTypeApi
     */
    public apiEquipmentTypeLastMonthQuantitiesGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentTypeApiFp(this.configuration).apiEquipmentTypeLastMonthQuantitiesGet(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} sapMaterialNum 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EquipmentTypeApi
     */
    public apiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet(sapMaterialNum: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return EquipmentTypeApiFp(this.configuration).apiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet(sapMaterialNum, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * IncorrectEquipmentApi - axios parameter creator
 * @export
 */
export const IncorrectEquipmentApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} equipmentSerialNum 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet: async (equipmentSerialNum: string, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentSerialNum' is not null or undefined
            assertParamExists('apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet', 'equipmentSerialNum', equipmentSerialNum)
            const localVarPath = `/api/incorrect-equipment/check-serial-number/{equipmentSerialNum}`
                .replace(`{${"equipmentSerialNum"}}`, encodeURIComponent(String(equipmentSerialNum)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} serviceId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIncorrectEquipmentCheckServiceIdByServiceIdGet: async (serviceId: string, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'serviceId' is not null or undefined
            assertParamExists('apiIncorrectEquipmentCheckServiceIdByServiceIdGet', 'serviceId', serviceId)
            const localVarPath = `/api/incorrect-equipment/check-service-id/{serviceId}`
                .replace(`{${"serviceId"}}`, encodeURIComponent(String(serviceId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} equipmentSerialnumber 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet: async (equipmentSerialnumber: string, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentSerialnumber' is not null or undefined
            assertParamExists('apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet', 'equipmentSerialnumber', equipmentSerialnumber)
            const localVarPath = `/api/incorrect-equipment/get-histories/{equipmentSerialnumber}`
                .replace(`{${"equipmentSerialnumber"}}`, encodeURIComponent(String(equipmentSerialnumber)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} namePrefix 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet: async (namePrefix: string, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'namePrefix' is not null or undefined
            assertParamExists('apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet', 'namePrefix', namePrefix)
            const localVarPath = `/api/incorrect-equipment/get-user-display-names/{namePrefix}`
                .replace(`{${"namePrefix"}}`, encodeURIComponent(String(namePrefix)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {TransferCorrectionRequest} [transferCorrectionRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIncorrectEquipmentTransferCorrectionPost: async (inputRequestId?: string, inputTimestamp?: string, transferCorrectionRequest?: TransferCorrectionRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/incorrect-equipment/transfer-correction`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(transferCorrectionRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {DeleteTransferRequest} [deleteTransferRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIncorrectEquipmentTransferDeleteDelete: async (inputRequestId?: string, inputTimestamp?: string, deleteTransferRequest?: DeleteTransferRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/incorrect-equipment/transfer-delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(deleteTransferRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIncorrectEquipmentUsersWithOpcodesGet: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/incorrect-equipment/users-with-opcodes`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * IncorrectEquipmentApi - functional programming interface
 * @export
 */
export const IncorrectEquipmentApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = IncorrectEquipmentApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} equipmentSerialNum 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet(equipmentSerialNum: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BaseResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet(equipmentSerialNum, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['IncorrectEquipmentApi.apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} serviceId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIncorrectEquipmentCheckServiceIdByServiceIdGet(serviceId: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BaseResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiIncorrectEquipmentCheckServiceIdByServiceIdGet(serviceId, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['IncorrectEquipmentApi.apiIncorrectEquipmentCheckServiceIdByServiceIdGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} equipmentSerialnumber 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet(equipmentSerialnumber: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<HistoriesResponse>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet(equipmentSerialnumber, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['IncorrectEquipmentApi.apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} namePrefix 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet(namePrefix: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<UserDisplayNameResponseModel>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet(namePrefix, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['IncorrectEquipmentApi.apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {TransferCorrectionRequest} [transferCorrectionRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIncorrectEquipmentTransferCorrectionPost(inputRequestId?: string, inputTimestamp?: string, transferCorrectionRequest?: TransferCorrectionRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BaseResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiIncorrectEquipmentTransferCorrectionPost(inputRequestId, inputTimestamp, transferCorrectionRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['IncorrectEquipmentApi.apiIncorrectEquipmentTransferCorrectionPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {DeleteTransferRequest} [deleteTransferRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIncorrectEquipmentTransferDeleteDelete(inputRequestId?: string, inputTimestamp?: string, deleteTransferRequest?: DeleteTransferRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BaseResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiIncorrectEquipmentTransferDeleteDelete(inputRequestId, inputTimestamp, deleteTransferRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['IncorrectEquipmentApi.apiIncorrectEquipmentTransferDeleteDelete']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiIncorrectEquipmentUsersWithOpcodesGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<UsersWithOPCodeResponse>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiIncorrectEquipmentUsersWithOpcodesGet(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['IncorrectEquipmentApi.apiIncorrectEquipmentUsersWithOpcodesGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * IncorrectEquipmentApi - factory interface
 * @export
 */
export const IncorrectEquipmentApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = IncorrectEquipmentApiFp(configuration)
    return {
        /**
         * 
         * @param {string} equipmentSerialNum 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet(equipmentSerialNum: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<BaseResponseModel> {
            return localVarFp.apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet(equipmentSerialNum, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} serviceId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIncorrectEquipmentCheckServiceIdByServiceIdGet(serviceId: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<BaseResponseModel> {
            return localVarFp.apiIncorrectEquipmentCheckServiceIdByServiceIdGet(serviceId, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} equipmentSerialnumber 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet(equipmentSerialnumber: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<HistoriesResponse>> {
            return localVarFp.apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet(equipmentSerialnumber, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} namePrefix 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet(namePrefix: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<UserDisplayNameResponseModel>> {
            return localVarFp.apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet(namePrefix, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {TransferCorrectionRequest} [transferCorrectionRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIncorrectEquipmentTransferCorrectionPost(inputRequestId?: string, inputTimestamp?: string, transferCorrectionRequest?: TransferCorrectionRequest, options?: RawAxiosRequestConfig): AxiosPromise<BaseResponseModel> {
            return localVarFp.apiIncorrectEquipmentTransferCorrectionPost(inputRequestId, inputTimestamp, transferCorrectionRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {DeleteTransferRequest} [deleteTransferRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIncorrectEquipmentTransferDeleteDelete(inputRequestId?: string, inputTimestamp?: string, deleteTransferRequest?: DeleteTransferRequest, options?: RawAxiosRequestConfig): AxiosPromise<BaseResponseModel> {
            return localVarFp.apiIncorrectEquipmentTransferDeleteDelete(inputRequestId, inputTimestamp, deleteTransferRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiIncorrectEquipmentUsersWithOpcodesGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<UsersWithOPCodeResponse>> {
            return localVarFp.apiIncorrectEquipmentUsersWithOpcodesGet(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * IncorrectEquipmentApi - object-oriented interface
 * @export
 * @class IncorrectEquipmentApi
 * @extends {BaseAPI}
 */
export class IncorrectEquipmentApi extends BaseAPI {
    /**
     * 
     * @param {string} equipmentSerialNum 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof IncorrectEquipmentApi
     */
    public apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet(equipmentSerialNum: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return IncorrectEquipmentApiFp(this.configuration).apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet(equipmentSerialNum, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} serviceId 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof IncorrectEquipmentApi
     */
    public apiIncorrectEquipmentCheckServiceIdByServiceIdGet(serviceId: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return IncorrectEquipmentApiFp(this.configuration).apiIncorrectEquipmentCheckServiceIdByServiceIdGet(serviceId, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} equipmentSerialnumber 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof IncorrectEquipmentApi
     */
    public apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet(equipmentSerialnumber: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return IncorrectEquipmentApiFp(this.configuration).apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet(equipmentSerialnumber, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} namePrefix 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof IncorrectEquipmentApi
     */
    public apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet(namePrefix: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return IncorrectEquipmentApiFp(this.configuration).apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet(namePrefix, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {TransferCorrectionRequest} [transferCorrectionRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof IncorrectEquipmentApi
     */
    public apiIncorrectEquipmentTransferCorrectionPost(inputRequestId?: string, inputTimestamp?: string, transferCorrectionRequest?: TransferCorrectionRequest, options?: RawAxiosRequestConfig) {
        return IncorrectEquipmentApiFp(this.configuration).apiIncorrectEquipmentTransferCorrectionPost(inputRequestId, inputTimestamp, transferCorrectionRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {DeleteTransferRequest} [deleteTransferRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof IncorrectEquipmentApi
     */
    public apiIncorrectEquipmentTransferDeleteDelete(inputRequestId?: string, inputTimestamp?: string, deleteTransferRequest?: DeleteTransferRequest, options?: RawAxiosRequestConfig) {
        return IncorrectEquipmentApiFp(this.configuration).apiIncorrectEquipmentTransferDeleteDelete(inputRequestId, inputTimestamp, deleteTransferRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof IncorrectEquipmentApi
     */
    public apiIncorrectEquipmentUsersWithOpcodesGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return IncorrectEquipmentApiFp(this.configuration).apiIncorrectEquipmentUsersWithOpcodesGet(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * ProductsApi - axios parameter creator
 * @export
 */
export const ProductsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {ProductDataRequestModel} productDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiProductsAddPost: async (productDataRequestModel: ProductDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'productDataRequestModel' is not null or undefined
            assertParamExists('apiProductsAddPost', 'productDataRequestModel', productDataRequestModel)
            const localVarPath = `/api/products/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(productDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiProductsByIdGet: async (id: number, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('apiProductsByIdGet', 'id', id)
            const localVarPath = `/api/products/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiProductsSearchPost: async (searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'searchDataRequestModel' is not null or undefined
            assertParamExists('apiProductsSearchPost', 'searchDataRequestModel', searchDataRequestModel)
            const localVarPath = `/api/products/search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(searchDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {ProductDataRequestModel} productDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiProductsUpdatePut: async (productDataRequestModel: ProductDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'productDataRequestModel' is not null or undefined
            assertParamExists('apiProductsUpdatePut', 'productDataRequestModel', productDataRequestModel)
            const localVarPath = `/api/products/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(productDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ProductsApi - functional programming interface
 * @export
 */
export const ProductsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ProductsApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {ProductDataRequestModel} productDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiProductsAddPost(productDataRequestModel: ProductDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<number>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiProductsAddPost(productDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProductsApi.apiProductsAddPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiProductsByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SearchProductDataResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiProductsByIdGet(id, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProductsApi.apiProductsByIdGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiProductsSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SearchProductDataResponseModelSearchResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiProductsSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProductsApi.apiProductsSearchPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {ProductDataRequestModel} productDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiProductsUpdatePut(productDataRequestModel: ProductDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiProductsUpdatePut(productDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProductsApi.apiProductsUpdatePut']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ProductsApi - factory interface
 * @export
 */
export const ProductsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ProductsApiFp(configuration)
    return {
        /**
         * 
         * @param {ProductDataRequestModel} productDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiProductsAddPost(productDataRequestModel: ProductDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<number> {
            return localVarFp.apiProductsAddPost(productDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiProductsByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<SearchProductDataResponseModel> {
            return localVarFp.apiProductsByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiProductsSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<SearchProductDataResponseModelSearchResponseModel> {
            return localVarFp.apiProductsSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {ProductDataRequestModel} productDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiProductsUpdatePut(productDataRequestModel: ProductDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiProductsUpdatePut(productDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ProductsApi - object-oriented interface
 * @export
 * @class ProductsApi
 * @extends {BaseAPI}
 */
export class ProductsApi extends BaseAPI {
    /**
     * 
     * @param {ProductDataRequestModel} productDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProductsApi
     */
    public apiProductsAddPost(productDataRequestModel: ProductDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return ProductsApiFp(this.configuration).apiProductsAddPost(productDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProductsApi
     */
    public apiProductsByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return ProductsApiFp(this.configuration).apiProductsByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {SearchDataRequestModel} searchDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProductsApi
     */
    public apiProductsSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return ProductsApiFp(this.configuration).apiProductsSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {ProductDataRequestModel} productDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProductsApi
     */
    public apiProductsUpdatePut(productDataRequestModel: ProductDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return ProductsApiFp(this.configuration).apiProductsUpdatePut(productDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * QuantityCalculationObjectApi - axios parameter creator
 * @export
 */
export const QuantityCalculationObjectApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiQuantityCalculationObjectByIdGet: async (id: number, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('apiQuantityCalculationObjectByIdGet', 'id', id)
            const localVarPath = `/api/quantity-calculation-object/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiQuantityCalculationObjectSearchPost: async (searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'searchDataRequestModel' is not null or undefined
            assertParamExists('apiQuantityCalculationObjectSearchPost', 'searchDataRequestModel', searchDataRequestModel)
            const localVarPath = `/api/quantity-calculation-object/search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(searchDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {QuantityCalculationObjectRequestModel} [quantityCalculationObjectRequestModel] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiQuantityCalculationObjectUpdatePost: async (inputRequestId?: string, inputTimestamp?: string, quantityCalculationObjectRequestModel?: QuantityCalculationObjectRequestModel, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/quantity-calculation-object/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(quantityCalculationObjectRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * QuantityCalculationObjectApi - functional programming interface
 * @export
 */
export const QuantityCalculationObjectApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = QuantityCalculationObjectApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiQuantityCalculationObjectByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<QuantityCalculationObjectResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiQuantityCalculationObjectByIdGet(id, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['QuantityCalculationObjectApi.apiQuantityCalculationObjectByIdGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiQuantityCalculationObjectSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<QuantityCalculationObjectResponseModelSearchResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiQuantityCalculationObjectSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['QuantityCalculationObjectApi.apiQuantityCalculationObjectSearchPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {QuantityCalculationObjectRequestModel} [quantityCalculationObjectRequestModel] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiQuantityCalculationObjectUpdatePost(inputRequestId?: string, inputTimestamp?: string, quantityCalculationObjectRequestModel?: QuantityCalculationObjectRequestModel, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BaseResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiQuantityCalculationObjectUpdatePost(inputRequestId, inputTimestamp, quantityCalculationObjectRequestModel, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['QuantityCalculationObjectApi.apiQuantityCalculationObjectUpdatePost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * QuantityCalculationObjectApi - factory interface
 * @export
 */
export const QuantityCalculationObjectApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = QuantityCalculationObjectApiFp(configuration)
    return {
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiQuantityCalculationObjectByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<QuantityCalculationObjectResponseModel> {
            return localVarFp.apiQuantityCalculationObjectByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiQuantityCalculationObjectSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<QuantityCalculationObjectResponseModelSearchResponseModel> {
            return localVarFp.apiQuantityCalculationObjectSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {QuantityCalculationObjectRequestModel} [quantityCalculationObjectRequestModel] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiQuantityCalculationObjectUpdatePost(inputRequestId?: string, inputTimestamp?: string, quantityCalculationObjectRequestModel?: QuantityCalculationObjectRequestModel, options?: RawAxiosRequestConfig): AxiosPromise<BaseResponseModel> {
            return localVarFp.apiQuantityCalculationObjectUpdatePost(inputRequestId, inputTimestamp, quantityCalculationObjectRequestModel, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * QuantityCalculationObjectApi - object-oriented interface
 * @export
 * @class QuantityCalculationObjectApi
 * @extends {BaseAPI}
 */
export class QuantityCalculationObjectApi extends BaseAPI {
    /**
     * 
     * @param {number} id 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof QuantityCalculationObjectApi
     */
    public apiQuantityCalculationObjectByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return QuantityCalculationObjectApiFp(this.configuration).apiQuantityCalculationObjectByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {SearchDataRequestModel} searchDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof QuantityCalculationObjectApi
     */
    public apiQuantityCalculationObjectSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return QuantityCalculationObjectApiFp(this.configuration).apiQuantityCalculationObjectSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {QuantityCalculationObjectRequestModel} [quantityCalculationObjectRequestModel] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof QuantityCalculationObjectApi
     */
    public apiQuantityCalculationObjectUpdatePost(inputRequestId?: string, inputTimestamp?: string, quantityCalculationObjectRequestModel?: QuantityCalculationObjectRequestModel, options?: RawAxiosRequestConfig) {
        return QuantityCalculationObjectApiFp(this.configuration).apiQuantityCalculationObjectUpdatePost(inputRequestId, inputTimestamp, quantityCalculationObjectRequestModel, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * SchenkersApi - axios parameter creator
 * @export
 */
export const SchenkersApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {SchenkerRequestModel} schenkerRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchenkersAddPost: async (schenkerRequestModel: SchenkerRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'schenkerRequestModel' is not null or undefined
            assertParamExists('apiSchenkersAddPost', 'schenkerRequestModel', schenkerRequestModel)
            const localVarPath = `/api/schenkers/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(schenkerRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchenkersByIdGet: async (id: number, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('apiSchenkersByIdGet', 'id', id)
            const localVarPath = `/api/schenkers/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchenkersSearchPost: async (searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'searchDataRequestModel' is not null or undefined
            assertParamExists('apiSchenkersSearchPost', 'searchDataRequestModel', searchDataRequestModel)
            const localVarPath = `/api/schenkers/search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(searchDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchenkersSelectListGet: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/schenkers/select-list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {SchenkerRequestModel} schenkerRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchenkersUpdatePut: async (schenkerRequestModel: SchenkerRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'schenkerRequestModel' is not null or undefined
            assertParamExists('apiSchenkersUpdatePut', 'schenkerRequestModel', schenkerRequestModel)
            const localVarPath = `/api/schenkers/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(schenkerRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} schenkerId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchenkersUsersBySchenkerIdGet: async (schenkerId: number, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'schenkerId' is not null or undefined
            assertParamExists('apiSchenkersUsersBySchenkerIdGet', 'schenkerId', schenkerId)
            const localVarPath = `/api/schenkers/users/{schenkerId}`
                .replace(`{${"schenkerId"}}`, encodeURIComponent(String(schenkerId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SchenkersApi - functional programming interface
 * @export
 */
export const SchenkersApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = SchenkersApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {SchenkerRequestModel} schenkerRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchenkersAddPost(schenkerRequestModel: SchenkerRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<number>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiSchenkersAddPost(schenkerRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SchenkersApi.apiSchenkersAddPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchenkersByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SchenkerResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiSchenkersByIdGet(id, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SchenkersApi.apiSchenkersByIdGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchenkersSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SchenkerResponseModelSearchResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiSchenkersSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SchenkersApi.apiSchenkersSearchPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchenkersSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<SchenkerSelectItemResponseModel>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiSchenkersSelectListGet(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SchenkersApi.apiSchenkersSelectListGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {SchenkerRequestModel} schenkerRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchenkersUpdatePut(schenkerRequestModel: SchenkerRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiSchenkersUpdatePut(schenkerRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SchenkersApi.apiSchenkersUpdatePut']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} schenkerId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSchenkersUsersBySchenkerIdGet(schenkerId: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<UserDataConciseResponseModel>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiSchenkersUsersBySchenkerIdGet(schenkerId, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SchenkersApi.apiSchenkersUsersBySchenkerIdGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * SchenkersApi - factory interface
 * @export
 */
export const SchenkersApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = SchenkersApiFp(configuration)
    return {
        /**
         * 
         * @param {SchenkerRequestModel} schenkerRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchenkersAddPost(schenkerRequestModel: SchenkerRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<number> {
            return localVarFp.apiSchenkersAddPost(schenkerRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchenkersByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<SchenkerResponseModel> {
            return localVarFp.apiSchenkersByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchenkersSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<SchenkerResponseModelSearchResponseModel> {
            return localVarFp.apiSchenkersSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchenkersSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<SchenkerSelectItemResponseModel>> {
            return localVarFp.apiSchenkersSelectListGet(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {SchenkerRequestModel} schenkerRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchenkersUpdatePut(schenkerRequestModel: SchenkerRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiSchenkersUpdatePut(schenkerRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} schenkerId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSchenkersUsersBySchenkerIdGet(schenkerId: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<UserDataConciseResponseModel>> {
            return localVarFp.apiSchenkersUsersBySchenkerIdGet(schenkerId, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SchenkersApi - object-oriented interface
 * @export
 * @class SchenkersApi
 * @extends {BaseAPI}
 */
export class SchenkersApi extends BaseAPI {
    /**
     * 
     * @param {SchenkerRequestModel} schenkerRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SchenkersApi
     */
    public apiSchenkersAddPost(schenkerRequestModel: SchenkerRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return SchenkersApiFp(this.configuration).apiSchenkersAddPost(schenkerRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SchenkersApi
     */
    public apiSchenkersByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return SchenkersApiFp(this.configuration).apiSchenkersByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {SearchDataRequestModel} searchDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SchenkersApi
     */
    public apiSchenkersSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return SchenkersApiFp(this.configuration).apiSchenkersSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SchenkersApi
     */
    public apiSchenkersSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return SchenkersApiFp(this.configuration).apiSchenkersSelectListGet(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {SchenkerRequestModel} schenkerRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SchenkersApi
     */
    public apiSchenkersUpdatePut(schenkerRequestModel: SchenkerRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return SchenkersApiFp(this.configuration).apiSchenkersUpdatePut(schenkerRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} schenkerId 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SchenkersApi
     */
    public apiSchenkersUsersBySchenkerIdGet(schenkerId: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return SchenkersApiFp(this.configuration).apiSchenkersUsersBySchenkerIdGet(schenkerId, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * SubstitutionsApi - axios parameter creator
 * @export
 */
export const SubstitutionsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {SubstitutionDataRequestModel} substitutionDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSubstitutionsAddPost: async (substitutionDataRequestModel: SubstitutionDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'substitutionDataRequestModel' is not null or undefined
            assertParamExists('apiSubstitutionsAddPost', 'substitutionDataRequestModel', substitutionDataRequestModel)
            const localVarPath = `/api/substitutions/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(substitutionDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSubstitutionsByIdGet: async (id: number, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('apiSubstitutionsByIdGet', 'id', id)
            const localVarPath = `/api/substitutions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSubstitutionsDelete: async (id: number, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('apiSubstitutionsDelete', 'id', id)
            const localVarPath = `/api/substitutions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (id !== undefined) {
                localVarQueryParameter['id'] = id;
            }


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {boolean} [isCurrentUserMol] 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSubstitutionsSearchPost: async (searchDataRequestModel: SearchDataRequestModel, isCurrentUserMol?: boolean, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'searchDataRequestModel' is not null or undefined
            assertParamExists('apiSubstitutionsSearchPost', 'searchDataRequestModel', searchDataRequestModel)
            const localVarPath = `/api/substitutions/search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (isCurrentUserMol !== undefined) {
                localVarQueryParameter['isCurrentUserMol'] = isCurrentUserMol;
            }


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(searchDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {SubstitutionDataRequestModel} substitutionDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSubstitutionsUpdatePut: async (substitutionDataRequestModel: SubstitutionDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'substitutionDataRequestModel' is not null or undefined
            assertParamExists('apiSubstitutionsUpdatePut', 'substitutionDataRequestModel', substitutionDataRequestModel)
            const localVarPath = `/api/substitutions/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(substitutionDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SubstitutionsApi - functional programming interface
 * @export
 */
export const SubstitutionsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = SubstitutionsApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {SubstitutionDataRequestModel} substitutionDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSubstitutionsAddPost(substitutionDataRequestModel: SubstitutionDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<number>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiSubstitutionsAddPost(substitutionDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SubstitutionsApi.apiSubstitutionsAddPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSubstitutionsByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SubstitutionsResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiSubstitutionsByIdGet(id, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SubstitutionsApi.apiSubstitutionsByIdGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSubstitutionsDelete(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiSubstitutionsDelete(id, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SubstitutionsApi.apiSubstitutionsDelete']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {boolean} [isCurrentUserMol] 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSubstitutionsSearchPost(searchDataRequestModel: SearchDataRequestModel, isCurrentUserMol?: boolean, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SubstitutionsResponseModelSearchResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiSubstitutionsSearchPost(searchDataRequestModel, isCurrentUserMol, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SubstitutionsApi.apiSubstitutionsSearchPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {SubstitutionDataRequestModel} substitutionDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSubstitutionsUpdatePut(substitutionDataRequestModel: SubstitutionDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiSubstitutionsUpdatePut(substitutionDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SubstitutionsApi.apiSubstitutionsUpdatePut']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * SubstitutionsApi - factory interface
 * @export
 */
export const SubstitutionsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = SubstitutionsApiFp(configuration)
    return {
        /**
         * 
         * @param {SubstitutionDataRequestModel} substitutionDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSubstitutionsAddPost(substitutionDataRequestModel: SubstitutionDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<number> {
            return localVarFp.apiSubstitutionsAddPost(substitutionDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSubstitutionsByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<SubstitutionsResponseModel> {
            return localVarFp.apiSubstitutionsByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSubstitutionsDelete(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiSubstitutionsDelete(id, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {boolean} [isCurrentUserMol] 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSubstitutionsSearchPost(searchDataRequestModel: SearchDataRequestModel, isCurrentUserMol?: boolean, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<SubstitutionsResponseModelSearchResponseModel> {
            return localVarFp.apiSubstitutionsSearchPost(searchDataRequestModel, isCurrentUserMol, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {SubstitutionDataRequestModel} substitutionDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSubstitutionsUpdatePut(substitutionDataRequestModel: SubstitutionDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiSubstitutionsUpdatePut(substitutionDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SubstitutionsApi - object-oriented interface
 * @export
 * @class SubstitutionsApi
 * @extends {BaseAPI}
 */
export class SubstitutionsApi extends BaseAPI {
    /**
     * 
     * @param {SubstitutionDataRequestModel} substitutionDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubstitutionsApi
     */
    public apiSubstitutionsAddPost(substitutionDataRequestModel: SubstitutionDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return SubstitutionsApiFp(this.configuration).apiSubstitutionsAddPost(substitutionDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubstitutionsApi
     */
    public apiSubstitutionsByIdGet(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return SubstitutionsApiFp(this.configuration).apiSubstitutionsByIdGet(id, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubstitutionsApi
     */
    public apiSubstitutionsDelete(id: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return SubstitutionsApiFp(this.configuration).apiSubstitutionsDelete(id, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {SearchDataRequestModel} searchDataRequestModel 
     * @param {boolean} [isCurrentUserMol] 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubstitutionsApi
     */
    public apiSubstitutionsSearchPost(searchDataRequestModel: SearchDataRequestModel, isCurrentUserMol?: boolean, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return SubstitutionsApiFp(this.configuration).apiSubstitutionsSearchPost(searchDataRequestModel, isCurrentUserMol, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {SubstitutionDataRequestModel} substitutionDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubstitutionsApi
     */
    public apiSubstitutionsUpdatePut(substitutionDataRequestModel: SubstitutionDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return SubstitutionsApiFp(this.configuration).apiSubstitutionsUpdatePut(substitutionDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * UsersApi - axios parameter creator
 * @export
 */
export const UsersApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {Array<string>} requestBody 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersActivateUsersPut: async (requestBody: Array<string>, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'requestBody' is not null or undefined
            assertParamExists('apiUsersActivateUsersPut', 'requestBody', requestBody)
            const localVarPath = `/api/users/activate-users`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(requestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} userId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersAdAccountByUserIdGet: async (userId: string, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('apiUsersAdAccountByUserIdGet', 'userId', userId)
            const localVarPath = `/api/users/ad-account/{userId}`
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersAllMolsGet: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/users/all-mols`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {Array<string>} requestBody 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersBlockUsersPut: async (requestBody: Array<string>, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'requestBody' is not null or undefined
            assertParamExists('apiUsersBlockUsersPut', 'requestBody', requestBody)
            const localVarPath = `/api/users/block-users`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(requestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} userId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersByUserIdGet: async (userId: string, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('apiUsersByUserIdGet', 'userId', userId)
            const localVarPath = `/api/users/{userId}`
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} userId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersIsUserMolByUserIdGet: async (userId: string, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('apiUsersIsUserMolByUserIdGet', 'userId', userId)
            const localVarPath = `/api/users/is-user-mol/{userId}`
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {EditUserRequest} editUserRequest 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersPut: async (editUserRequest: EditUserRequest, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'editUserRequest' is not null or undefined
            assertParamExists('apiUsersPut', 'editUserRequest', editUserRequest)
            const localVarPath = `/api/users`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(editUserRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersSearchPost: async (searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'searchDataRequestModel' is not null or undefined
            assertParamExists('apiUsersSearchPost', 'searchDataRequestModel', searchDataRequestModel)
            const localVarPath = `/api/users/search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(searchDataRequestModel, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} username 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersUserByUsernameByUsernameGet: async (username: string, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'username' is not null or undefined
            assertParamExists('apiUsersUserByUsernameByUsernameGet', 'username', username)
            const localVarPath = `/api/users/user-by-username/{username}`
                .replace(`{${"username"}}`, encodeURIComponent(String(username)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersUsersSelectListGet: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/users/users-select-list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * UsersApi - functional programming interface
 * @export
 */
export const UsersApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = UsersApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {Array<string>} requestBody 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUsersActivateUsersPut(requestBody: Array<string>, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiUsersActivateUsersPut(requestBody, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.apiUsersActivateUsersPut']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} userId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUsersAdAccountByUserIdGet(userId: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiUsersAdAccountByUserIdGet(userId, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.apiUsersAdAccountByUserIdGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUsersAllMolsGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<UserDataConciseResponseModel>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiUsersAllMolsGet(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.apiUsersAllMolsGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {Array<string>} requestBody 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUsersBlockUsersPut(requestBody: Array<string>, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiUsersBlockUsersPut(requestBody, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.apiUsersBlockUsersPut']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} userId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUsersByUserIdGet(userId: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SearchUserDataResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiUsersByUserIdGet(userId, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.apiUsersByUserIdGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} userId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUsersIsUserMolByUserIdGet(userId: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<boolean>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiUsersIsUserMolByUserIdGet(userId, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.apiUsersIsUserMolByUserIdGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {EditUserRequest} editUserRequest 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUsersPut(editUserRequest: EditUserRequest, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiUsersPut(editUserRequest, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.apiUsersPut']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUsersSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SearchUserDataResponseModelSearchResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiUsersSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.apiUsersSearchPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} username 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUsersUserByUsernameByUsernameGet(username: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SearchUserDataResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiUsersUserByUsernameByUsernameGet(username, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.apiUsersUserByUsernameByUsernameGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUsersUsersSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UsersListResponseModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiUsersUsersSelectListGet(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.apiUsersUsersSelectListGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * UsersApi - factory interface
 * @export
 */
export const UsersApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = UsersApiFp(configuration)
    return {
        /**
         * 
         * @param {Array<string>} requestBody 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersActivateUsersPut(requestBody: Array<string>, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiUsersActivateUsersPut(requestBody, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} userId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersAdAccountByUserIdGet(userId: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.apiUsersAdAccountByUserIdGet(userId, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersAllMolsGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<UserDataConciseResponseModel>> {
            return localVarFp.apiUsersAllMolsGet(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {Array<string>} requestBody 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersBlockUsersPut(requestBody: Array<string>, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiUsersBlockUsersPut(requestBody, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} userId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersByUserIdGet(userId: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<SearchUserDataResponseModel> {
            return localVarFp.apiUsersByUserIdGet(userId, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} userId 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersIsUserMolByUserIdGet(userId: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<boolean> {
            return localVarFp.apiUsersIsUserMolByUserIdGet(userId, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {EditUserRequest} editUserRequest 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersPut(editUserRequest: EditUserRequest, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.apiUsersPut(editUserRequest, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {SearchDataRequestModel} searchDataRequestModel 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<SearchUserDataResponseModelSearchResponseModel> {
            return localVarFp.apiUsersSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} username 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersUserByUsernameByUsernameGet(username: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<SearchUserDataResponseModel> {
            return localVarFp.apiUsersUserByUsernameByUsernameGet(username, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUsersUsersSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<UsersListResponseModel> {
            return localVarFp.apiUsersUsersSelectListGet(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * UsersApi - object-oriented interface
 * @export
 * @class UsersApi
 * @extends {BaseAPI}
 */
export class UsersApi extends BaseAPI {
    /**
     * 
     * @param {Array<string>} requestBody 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public apiUsersActivateUsersPut(requestBody: Array<string>, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).apiUsersActivateUsersPut(requestBody, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} userId 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public apiUsersAdAccountByUserIdGet(userId: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).apiUsersAdAccountByUserIdGet(userId, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public apiUsersAllMolsGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).apiUsersAllMolsGet(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {Array<string>} requestBody 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public apiUsersBlockUsersPut(requestBody: Array<string>, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).apiUsersBlockUsersPut(requestBody, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} userId 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public apiUsersByUserIdGet(userId: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).apiUsersByUserIdGet(userId, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} userId 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public apiUsersIsUserMolByUserIdGet(userId: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).apiUsersIsUserMolByUserIdGet(userId, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {EditUserRequest} editUserRequest 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public apiUsersPut(editUserRequest: EditUserRequest, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).apiUsersPut(editUserRequest, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {SearchDataRequestModel} searchDataRequestModel 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public apiUsersSearchPost(searchDataRequestModel: SearchDataRequestModel, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).apiUsersSearchPost(searchDataRequestModel, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} username 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public apiUsersUserByUsernameByUsernameGet(username: string, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).apiUsersUserByUsernameByUsernameGet(username, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public apiUsersUsersSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).apiUsersUsersSelectListGet(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * WarehousesApi - axios parameter creator
 * @export
 */
export const WarehousesApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {number} [region] 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiWarehousesByRegionGet: async (region?: number, inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/warehouses/by-region`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (region !== undefined) {
                localVarQueryParameter['region'] = region;
            }


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiWarehousesSelectListGet: async (inputRequestId?: string, inputTimestamp?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/warehouses/select-list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (inputRequestId != null) {
                localVarHeaderParameter['Input-Request-Id'] = String(inputRequestId);
            }
            if (inputTimestamp != null) {
                localVarHeaderParameter['Input-Timestamp'] = typeof inputTimestamp === 'string'
                    ? inputTimestamp
                    : JSON.stringify(inputTimestamp);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * WarehousesApi - functional programming interface
 * @export
 */
export const WarehousesApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = WarehousesApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {number} [region] 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiWarehousesByRegionGet(region?: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<WarehousesResponseModel>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiWarehousesByRegionGet(region, inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['WarehousesApi.apiWarehousesByRegionGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiWarehousesSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<WarehousesResponseModel>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.apiWarehousesSelectListGet(inputRequestId, inputTimestamp, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['WarehousesApi.apiWarehousesSelectListGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * WarehousesApi - factory interface
 * @export
 */
export const WarehousesApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = WarehousesApiFp(configuration)
    return {
        /**
         * 
         * @param {number} [region] 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiWarehousesByRegionGet(region?: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<WarehousesResponseModel>> {
            return localVarFp.apiWarehousesByRegionGet(region, inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [inputRequestId] 
         * @param {string} [inputTimestamp] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiWarehousesSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig): AxiosPromise<Array<WarehousesResponseModel>> {
            return localVarFp.apiWarehousesSelectListGet(inputRequestId, inputTimestamp, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * WarehousesApi - object-oriented interface
 * @export
 * @class WarehousesApi
 * @extends {BaseAPI}
 */
export class WarehousesApi extends BaseAPI {
    /**
     * 
     * @param {number} [region] 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof WarehousesApi
     */
    public apiWarehousesByRegionGet(region?: number, inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return WarehousesApiFp(this.configuration).apiWarehousesByRegionGet(region, inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} [inputRequestId] 
     * @param {string} [inputTimestamp] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof WarehousesApi
     */
    public apiWarehousesSelectListGet(inputRequestId?: string, inputTimestamp?: string, options?: RawAxiosRequestConfig) {
        return WarehousesApiFp(this.configuration).apiWarehousesSelectListGet(inputRequestId, inputTimestamp, options).then((request) => request(this.axios, this.basePath));
    }
}



