# SchenkerSelectItemResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **string** |  | [optional] [default to undefined]
**opCode** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { SchenkerSelectItemResponseModel } from './api';

const instance: SchenkerSelectItemResponseModel = {
    id,
    opCode,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
