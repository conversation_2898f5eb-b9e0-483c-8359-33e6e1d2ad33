﻿using System;
using System.Collections.Generic;

namespace EtDb.ApiClients.EtDb.Models
{
    public partial class SnapshotDates
    {
        public SnapshotDates()
        {
            AvailableEquipmentByOpsnapshots = new HashSet<AvailableEquipmentByOpsnapshots>();
        }

        public int Id { get; set; }
        public DateTime Date { get; set; }

        public virtual ICollection<AvailableEquipmentByOpsnapshots> AvailableEquipmentByOpsnapshots { get; set; }
    }
}
