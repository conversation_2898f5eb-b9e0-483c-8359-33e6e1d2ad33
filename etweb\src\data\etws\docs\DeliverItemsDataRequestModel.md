# DeliverItemsDataRequestModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**selectedItems** | **Array&lt;number&gt;** |  | [default to undefined]
**toUserId** | **string** |  | [default to undefined]
**isSpecialUser** | **boolean** |  | [optional] [default to undefined]
**postOfficeId** | **number** |  | [optional] [default to undefined]
**waybillNum** | **string** |  | [optional] [default to undefined]
**waybillDate** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { DeliverItemsDataRequestModel } from './api';

const instance: DeliverItemsDataRequestModel = {
    selectedItems,
    toUserId,
    isSpecialUser,
    postOfficeId,
    waybillNum,
    waybillDate,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
