﻿using System;
using System.Collections.Generic;

namespace EtDb.ApiClients.EtDb.Models
{
    public partial class AvailableEquipmentByOpsnapshots
    {
        public int Id { get; set; }
        public int EquipmentQuantity { get; set; }
        public int Opid { get; set; }
        public int EquipmentTypeId { get; set; }
        public int SnapshotDateId { get; set; }

        public virtual EquipmentType EquipmentType { get; set; }
        public virtual Schenkers Op { get; set; }
        public virtual SnapshotDates SnapshotDate { get; set; }
    }
}
