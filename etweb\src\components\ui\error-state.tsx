import { AlertCircle, RefreshCw } from "lucide-react";
import { Button } from "./button";
import { cn } from "@/lib/utils";
import { useTranslation } from "react-i18next";

interface ErrorStateProps {
  message?: string;
  title?: string;
  onRetry?: () => void;
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function ErrorState({ 
  message,
  title,
  onRetry,
  size = "md",
  className 
}: ErrorStateProps) {
  const { t } = useTranslation();
  const defaultMessage = message || t("common.error.loadingData");
  
  const sizeClasses = {
    sm: "p-4",
    md: "p-8", 
    lg: "p-12"
  };

  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6"
  };

  return (
    <div className={cn("flex flex-col items-center justify-center text-center", sizeClasses[size], className)}>
      <div className="flex flex-col items-center gap-4">
        <div className="flex items-center gap-3 text-destructive">
          <AlertCircle className={cn(iconSizes[size])} />
          <div>
            {title && <h3 className="font-medium text-foreground mb-1">{title}</h3>}
            <span className="text-sm">{defaultMessage}</span>
          </div>
        </div>
        
        {onRetry && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onRetry}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            {t("common.error.retryButton")}
          </Button>
        )}
      </div>
    </div>
  );
}
