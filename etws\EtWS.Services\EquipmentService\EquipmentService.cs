﻿namespace EtWS.Services.EquipmentService
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.EquipmentModels;
    using EtWS.Services.NotificationsService;
    using EtWS.Services.UserManagerService;

    public class EquipmentService : IEquipmentService
    {
        private readonly Lazy<IETDB> etDb;
        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<IUserManagerService> userManagerService;
        private readonly Lazy<INotificationService> notificationService;

        public EquipmentService(Lazy<IETDB> etDb, Lazy<IMapper> mapper, Lazy<IUserManagerService> userManagerService, Lazy<INotificationService> notificationService)
        {
            this.etDb = etDb;
            this.mapper = mapper;
            this.userManagerService = userManagerService;
            this.notificationService = notificationService;
        }

        public async Task<SearchResponseModel<AvailableEquipmentDataResponseModel>> SearchAvailableEquipmentAsync(SearchDataRequestModel request, string userId)
        {
            var dbRequest = this.mapper.Value.Map<SearchRequestModelWithUserId>(request);
            dbRequest.UserId = userId;
            var data = await this.etDb.Value.ApiEquipmentSearchAvailableEquipmentPostAsync(body: dbRequest);
            return this.mapper.Value.Map<SearchResponseModel<AvailableEquipmentDataResponseModel>>(data);
        }

        public async Task<ReserveItemHistoryDataResponseModel> ReserveItemForTransferAsync(ReserveItemForTransferDataRequestModel request, string userId)
        {
            var dbRequest = this.mapper.Value.Map<ReserveItemForTransferRequestModel>(request);
            dbRequest.UserId = userId;
            var reservationEntry = await this.etDb.Value.ApiEquipmentReserveItemPostAsync(body: dbRequest);
            var response = this.mapper.Value.Map<ReserveItemHistoryDataResponseModel>(reservationEntry);
            response.ReservedItemsCount = await this.etDb.Value.ApiEquipmentReservedItemsCountByUserIdGetAsync(userId);
            return response;
        }

        public async Task<int> ReserveAllItemsForTransferAsync(string userId)
        {
            await this.etDb.Value.ApiEquipmentReserveAllItemsPostAsync(userId);
            return await this.etDb.Value.ApiEquipmentReservedItemsCountByUserIdGetAsync(userId);
        }

        public async Task<int> CountAllUserReservedItemsForTransfer(string userId)
        {
            return await this.etDb.Value.ApiEquipmentReservedItemsCountByUserIdGetAsync(userId);
        }

        public async Task<RemoveItemFromTransferResponseModel> RemoveItemFromTransferAsync(int itemId, string userId)
        {
            var dbRequest = new RemoveItemForTransferRequestModel
            {
                UserId = userId,
                ItemId = itemId,
            };
            var response = await this.etDb.Value.ApiEquipmentRemoveItemPostAsync(dbRequest);
            return new RemoveItemFromTransferResponseModel
            {
                RemovedEquipmentTypeName = response,
                ReservedItemsCount = await this.etDb.Value.ApiEquipmentReservedItemsCountByUserIdGetAsync(userId),
            };
        }

        public async Task RemoveItemsFromTransferAsync(IEnumerable<int> selectedItems, string userId)
        {
            var dbRequest = new RemoveItemsForTransferRequestModel
            {
                SelectedItems = selectedItems.ToList(),
                UserId = userId,
            };
            await this.etDb.Value.ApiEquipmentRemoveItemsPostAsync(dbRequest);
        }

        public async Task<int> RemoveAllItemsFromTransferAsync(string userId)
        {
            await this.etDb.Value.ApiEquipmentRemoveAllItemsPostAsync(userId);
            return await this.etDb.Value.ApiEquipmentReservedItemsCountByUserIdGetAsync(userId);
        }

        public async Task<SearchResponseModel<ItemDataResponseModel>> SearchTransferDataAsync(SearchDataRequestModel request, string userId)
        {
            var dbRequest = this.mapper.Value.Map<SearchRequestModelWithUserId>(request);
            dbRequest.UserId = userId;
            var data = await this.etDb.Value.ApiEquipmentSearchTransferDataPostAsync(body: dbRequest);
            return this.mapper.Value.Map<SearchResponseModel<ItemDataResponseModel>>(data);
        }

        public async Task<IEnumerable<PostOfficesSelectListResponseModel>> GetAllActivePostOfficesAsync()
        {
            return await this.etDb.Value.ApiEquipmentPostOfficesSelectListGetAsync();
        }

        public async Task DeliverItemsAsync(DeliverItemsDataRequestModel request, string userId)
        {
            if (request.SelectedItems.Count() == 0)
            {
                throw new ArgumentNullException("No items were provided for transfer!");
            }

            var fromUser = await this.userManagerService.Value.GetUserByUserIdAsync(userId);
            var toUser = await this.userManagerService.Value.GetUserByUserIdAsync(request.ToUserId);
            string fromOp = fromUser.SchenkerId != null ? fromUser.Op : null;
            string toOp = toUser.Opcode ?? (toUser.SchenkerId != null ? toUser.Op : null);

            var userReservedItems = await this.etDb.Value
                .ApiHistoryReservedItemsGetAsync(fromUser.Id);
            var itemsSerialNumbers = userReservedItems
                .Where(h => request.SelectedItems.Contains(h.ItemId))
                .Select(h => h.Item.EquipmentSerialNum)
                .ToList();

            var dbRequest = this.mapper.Value.Map<DeliverItemsRequestModel>(request);
            dbRequest.FromUserId = userId;
            await this.etDb.Value.ApiEquipmentDeliverItemsPostAsync(body: dbRequest);

            await this.userManagerService.Value.UpdateUserNotificationAsync(request.ToUserId);

            if (!string.IsNullOrWhiteSpace(fromOp) && !string.IsNullOrWhiteSpace(toOp) && !fromOp.Equals(toOp))
            {
                await this.notificationService.Value.SendEmailAsync(fromOp, toOp, itemsSerialNumbers);
            }
        }

        public async Task<SearchResponseModel<UserItemsToAcceptResponseModel>> SearchUserItemsToAcceptDataAsync(SearchDataRequestModel request, string userId)
        {
            var dbRequest = this.mapper.Value.Map<SearchRequestModelWithUserId>(request);
            dbRequest.UserId = userId;
            var data = await this.etDb.Value.ApiEquipmentSearchUserItemsToAcceptPostAsync(dbRequest);
            return this.mapper.Value.Map<SearchResponseModel<UserItemsToAcceptResponseModel>>(data);
        }

        public async Task<SearchResponseModel<UserItemsToCancelResponseModel>> SearchUserItemsToCancelDataAsync(SearchDataRequestModel request, string userId)
        {
            var dbRequest = this.mapper.Value.Map<SearchRequestModelWithUserId>(request);
            dbRequest.UserId = userId;
            var data = await this.etDb.Value.ApiEquipmentSearchUserItemsToCancelPostAsync(dbRequest);
            return this.mapper.Value.Map<SearchResponseModel<UserItemsToCancelResponseModel>>(data);
        }

        public async Task AcceptItemsAsync(AcceptItemsForTransferRequestModel request, string userId)
        {
            var dbRequest = this.mapper.Value.Map<AcceptItemsRequestModel>(request);
            dbRequest.UserId = userId;
            var usersPendingTransferedItemsTo = await this.etDb.Value.ApiEquipmentAcceptItemsPostAsync(dbRequest);

            await this.HandleNotificationsForTransfersAsync(usersPendingTransferedItemsTo, GlobalConstants.AcceptItemsActionName);
        }

        public async Task RefuseItemsAsync(RefuseItemsForTransferRequestModel request, string userId)
        {
            var dbRequest = this.mapper.Value.Map<RefuseItemsRequestModel>(request);
            dbRequest.UserId = userId;
            var usersPendingTransferedItemsTo = await this.etDb.Value.ApiEquipmentRefuseItemsPostAsync(dbRequest);

            await this.HandleNotificationsForTransfersAsync(usersPendingTransferedItemsTo, GlobalConstants.RefuseItemsActionName);
        }

        public async Task CancelItemsAsync(CancelItemsForTransferRequestModel request, string userId)
        {
            var dbRequest = this.mapper.Value.Map<CancelItemsRequestModel>(request);
            dbRequest.UserId = userId;
            var usersPendingTransferedItemsTo = await this.etDb.Value.ApiEquipmentCancelItemsPostAsync(dbRequest);

            await this.HandleNotificationsForTransfersAsync(usersPendingTransferedItemsTo, GlobalConstants.CancelItemsActionName);
        }

        private async Task HandleNotificationsForTransfersAsync(IDictionary<string, TransferedItemsModel> usersPendingTransferedItemsTo, string action)
        {
            foreach (var userIdKey in usersPendingTransferedItemsTo.Keys)
            {
                TransferedItemsModel transferedItemsModel = usersPendingTransferedItemsTo[userIdKey];
                await this.notificationService.Value.NotifyForAcceptanceOfTransferAsync(transferedItemsModel, action);
                if (usersPendingTransferedItemsTo[userIdKey].TotalCount == 0)
                {
                    await this.notificationService.Value.RemovePendingTransferNotificationAsync(userIdKey);
                }

                if (usersPendingTransferedItemsTo[userIdKey].ExpiredCount == 0)
                {
                   await this.notificationService.Value.RemoveNotificationForUnacceptedTransfersAsync(userIdKey, usersPendingTransferedItemsTo[userIdKey].ToUserUnacceptedTransferNotificationId);
                }
            }
        }
    }
}
