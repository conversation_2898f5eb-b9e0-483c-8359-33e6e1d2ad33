﻿namespace EtWS.Services.NotificationsService
{
    using EtWS.ApiClients.ETDB;
    using EtWs.ApiClients.NotificationsSender;
    using EtWS.Infrastructure.Models.Infrastructure;
    using Microsoft.Extensions.Options;

    public class NotificationService : INotificationService
    {
        private readonly Lazy<IETDB> etDb;
        private readonly Lazy<INotificationsSender> notificationsSender;

        public NotificationService(
            Lazy<IETDB> etDb,
            Lazy<INotificationsSender> notificationsSender)
        {
            this.etDb = etDb;
            this.notificationsSender = notificationsSender;
        }

        public async Task SendEmailAsync(string fromOp, string toOp, IEnumerable<string> equipmentSerialNumbers)
        {
            throw new NotImplementedException();
        }

        public async Task NotifyForAcceptanceOfTransferAsync(TransferedItemsModel transferedItemsModel, string actionName)
        {
            int indexOfMessage = 3;
            string serialNumbers = null;
            if (transferedItemsModel.SerialNumbers.Count <= 5)
            {
                indexOfMessage = transferedItemsModel.SerialNumbers.Count == 1 ? 1 : 2;
                serialNumbers = string.Join(", ", transferedItemsModel.SerialNumbers);
            }

            string userId = null;
            switch (actionName)
            {
                case GlobalConstants.AcceptItemsActionName: userId = transferedItemsModel.FromUserId; break;
                case GlobalConstants.RefuseItemsActionName: userId = transferedItemsModel.FromUserId; break;
                case GlobalConstants.CancelItemsActionName: userId = transferedItemsModel.ToUserId; break;
                default:
                    break;
            }

            var message = string.Format(AcceptanceOfItems.Messages[actionName][indexOfMessage], serialNumbers);
            var dbUpdateNotificationRequest = new UpdateUserNotificationRequestModel
            {
                UserId = userId,
                NotificationType = GlobalConstants.AcceptanceOfTransfer,
                Text = message,
            };
            await this.etDb.Value.ApiUsersUpdateUserNotificationPatchAsync(body: dbUpdateNotificationRequest);
        }

        public async Task RemovePendingTransferNotificationAsync(string userId)
        {
            var dbRemoveNotificationRequest = new RemoveUserNotificationRequestModel
            {
                UserId = userId,
                NotificationType = GlobalConstants.PendingTransfer,
            };
            await this.etDb.Value.ApiUsersRemoveUserNotificationPatchAsync(body: dbRemoveNotificationRequest);
        }

        public async Task RemoveNotificationForUnacceptedTransfersAsync(string userId, int notificationId)
        {
            var dbRemoveNotificationRequest = new RemoveUserNotificationRequestModel
            {
                UserId = userId,
                NotificationId = notificationId,
            };
            await this.etDb.Value.ApiUsersRemoveUserNotificationPatchAsync(body: dbRemoveNotificationRequest);
        }
    }
}
