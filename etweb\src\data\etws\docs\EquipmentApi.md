# EquipmentApi

All URIs are relative to *http://microit9app1.drcenter.btk.bg:26121/et-ws*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**apiEquipmentAcceptItemsPost**](#apiequipmentacceptitemspost) | **POST** /api/equipment/accept-items | |
|[**apiEquipmentCancelItemsPost**](#apiequipmentcancelitemspost) | **POST** /api/equipment/cancel-items | |
|[**apiEquipmentDeliverItemsPost**](#apiequipmentdeliveritemspost) | **POST** /api/equipment/deliver-items | |
|[**apiEquipmentPostOfficesSelectListGet**](#apiequipmentpostofficesselectlistget) | **GET** /api/equipment/post-offices-select-list | |
|[**apiEquipmentRefuseItemsPost**](#apiequipmentrefuseitemspost) | **POST** /api/equipment/refuse-items | |
|[**apiEquipmentRemoveAllItemsPost**](#apiequipmentremoveallitemspost) | **POST** /api/equipment/remove-all-items | |
|[**apiEquipmentRemoveItemPost**](#apiequipmentremoveitempost) | **POST** /api/equipment/remove-item | |
|[**apiEquipmentRemoveItemsPost**](#apiequipmentremoveitemspost) | **POST** /api/equipment/remove-items | |
|[**apiEquipmentReserveAllItemsPost**](#apiequipmentreserveallitemspost) | **POST** /api/equipment/reserve-all-items | |
|[**apiEquipmentReserveItemPost**](#apiequipmentreserveitempost) | **POST** /api/equipment/reserve-item | |
|[**apiEquipmentReservedItemsCountGet**](#apiequipmentreserveditemscountget) | **GET** /api/equipment/reserved-items-count | |
|[**apiEquipmentSearchAvailableEquipmentPost**](#apiequipmentsearchavailableequipmentpost) | **POST** /api/equipment/search-available-equipment | |
|[**apiEquipmentSearchTransferDataPost**](#apiequipmentsearchtransferdatapost) | **POST** /api/equipment/search-transfer-data | |
|[**apiEquipmentSearchUserItemsToAcceptPost**](#apiequipmentsearchuseritemstoacceptpost) | **POST** /api/equipment/search-user-items-to-accept | |
|[**apiEquipmentSearchUserItemsToCancelPost**](#apiequipmentsearchuseritemstocancelpost) | **POST** /api/equipment/search-user-items-to-cancel | |

# **apiEquipmentAcceptItemsPost**
> apiEquipmentAcceptItemsPost(acceptItemsForTransferRequestModel)


### Example

```typescript
import {
    EquipmentApi,
    Configuration,
    AcceptItemsForTransferRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let acceptItemsForTransferRequestModel: AcceptItemsForTransferRequestModel; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentAcceptItemsPost(
    acceptItemsForTransferRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **acceptItemsForTransferRequestModel** | **AcceptItemsForTransferRequestModel**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentCancelItemsPost**
> apiEquipmentCancelItemsPost(cancelItemsForTransferRequestModel)


### Example

```typescript
import {
    EquipmentApi,
    Configuration,
    CancelItemsForTransferRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let cancelItemsForTransferRequestModel: CancelItemsForTransferRequestModel; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentCancelItemsPost(
    cancelItemsForTransferRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **cancelItemsForTransferRequestModel** | **CancelItemsForTransferRequestModel**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentDeliverItemsPost**
> apiEquipmentDeliverItemsPost(deliverItemsDataRequestModel)


### Example

```typescript
import {
    EquipmentApi,
    Configuration,
    DeliverItemsDataRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let deliverItemsDataRequestModel: DeliverItemsDataRequestModel; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentDeliverItemsPost(
    deliverItemsDataRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **deliverItemsDataRequestModel** | **DeliverItemsDataRequestModel**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentPostOfficesSelectListGet**
> Array<PostOfficesSelectListResponseModel> apiEquipmentPostOfficesSelectListGet()


### Example

```typescript
import {
    EquipmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentPostOfficesSelectListGet(
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**Array<PostOfficesSelectListResponseModel>**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentRefuseItemsPost**
> apiEquipmentRefuseItemsPost(refuseItemsForTransferRequestModel)


### Example

```typescript
import {
    EquipmentApi,
    Configuration,
    RefuseItemsForTransferRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let refuseItemsForTransferRequestModel: RefuseItemsForTransferRequestModel; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentRefuseItemsPost(
    refuseItemsForTransferRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **refuseItemsForTransferRequestModel** | **RefuseItemsForTransferRequestModel**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentRemoveAllItemsPost**
> number apiEquipmentRemoveAllItemsPost()


### Example

```typescript
import {
    EquipmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentRemoveAllItemsPost(
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**number**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentRemoveItemPost**
> RemoveItemFromTransferResponseModel apiEquipmentRemoveItemPost()


### Example

```typescript
import {
    EquipmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let itemId: number; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentRemoveItemPost(
    itemId,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **itemId** | [**number**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**RemoveItemFromTransferResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentRemoveItemsPost**
> apiEquipmentRemoveItemsPost(requestBody)


### Example

```typescript
import {
    EquipmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let requestBody: Array<number>; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentRemoveItemsPost(
    requestBody,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **requestBody** | **Array<number>**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentReserveAllItemsPost**
> number apiEquipmentReserveAllItemsPost()


### Example

```typescript
import {
    EquipmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentReserveAllItemsPost(
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**number**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentReserveItemPost**
> ReserveItemHistoryResponseModel apiEquipmentReserveItemPost(reserveItemForTransferDataRequestModel)


### Example

```typescript
import {
    EquipmentApi,
    Configuration,
    ReserveItemForTransferDataRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let reserveItemForTransferDataRequestModel: ReserveItemForTransferDataRequestModel; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentReserveItemPost(
    reserveItemForTransferDataRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **reserveItemForTransferDataRequestModel** | **ReserveItemForTransferDataRequestModel**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**ReserveItemHistoryResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentReservedItemsCountGet**
> number apiEquipmentReservedItemsCountGet()


### Example

```typescript
import {
    EquipmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentReservedItemsCountGet(
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**number**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentSearchAvailableEquipmentPost**
> AvailableEquipmentDataResponseModelSearchResponseModel apiEquipmentSearchAvailableEquipmentPost(searchDataRequestModel)


### Example

```typescript
import {
    EquipmentApi,
    Configuration,
    SearchDataRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let searchDataRequestModel: SearchDataRequestModel; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentSearchAvailableEquipmentPost(
    searchDataRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **searchDataRequestModel** | **SearchDataRequestModel**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**AvailableEquipmentDataResponseModelSearchResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentSearchTransferDataPost**
> ItemDataResponseModelSearchResponseModel apiEquipmentSearchTransferDataPost(searchDataRequestModel)


### Example

```typescript
import {
    EquipmentApi,
    Configuration,
    SearchDataRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let searchDataRequestModel: SearchDataRequestModel; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentSearchTransferDataPost(
    searchDataRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **searchDataRequestModel** | **SearchDataRequestModel**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**ItemDataResponseModelSearchResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentSearchUserItemsToAcceptPost**
> UserItemsToAcceptResponseModelSearchResponseModel apiEquipmentSearchUserItemsToAcceptPost(searchDataRequestModel)


### Example

```typescript
import {
    EquipmentApi,
    Configuration,
    SearchDataRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let searchDataRequestModel: SearchDataRequestModel; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentSearchUserItemsToAcceptPost(
    searchDataRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **searchDataRequestModel** | **SearchDataRequestModel**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**UserItemsToAcceptResponseModelSearchResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentSearchUserItemsToCancelPost**
> UserItemsToCancelResponseModelSearchResponseModel apiEquipmentSearchUserItemsToCancelPost(searchDataRequestModel)


### Example

```typescript
import {
    EquipmentApi,
    Configuration,
    SearchDataRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentApi(configuration);

let searchDataRequestModel: SearchDataRequestModel; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentSearchUserItemsToCancelPost(
    searchDataRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **searchDataRequestModel** | **SearchDataRequestModel**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**UserItemsToCancelResponseModelSearchResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

