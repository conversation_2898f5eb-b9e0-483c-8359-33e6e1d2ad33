# SearchUserDataResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **string** |  | [optional] [default to undefined]
**eln** | **string** |  | [optional] [default to undefined]
**firstName** | **string** |  | [optional] [default to undefined]
**surname** | **string** |  | [optional] [default to undefined]
**familyName** | **string** |  | [optional] [default to undefined]
**fullName** | **string** |  | [optional] [default to undefined]
**op** | **string** |  | [optional] [default to undefined]
**regionId** | **number** |  | [optional] [default to undefined]
**region** | **string** |  | [optional] [default to undefined]
**cityId** | **number** |  | [optional] [default to undefined]
**city** | **string** |  | [optional] [default to undefined]
**clientNumber** | **string** |  | [optional] [default to undefined]
**phoneNumber** | **string** |  | [optional] [default to undefined]
**displayName** | **string** |  | [optional] [default to undefined]
**iptuName** | **string** |  | [optional] [default to undefined]
**position** | **string** |  | [optional] [default to undefined]
**adAccount** | **string** |  | [optional] [default to undefined]
**email** | **string** |  | [optional] [default to undefined]
**isMolOfOp** | **boolean** |  | [optional] [default to undefined]
**opcode** | **string** |  | [optional] [default to undefined]
**schenkerId** | **number** |  | [optional] [default to undefined]
**blocked** | **string** |  | [optional] [default to undefined]
**dataBlocked** | **string** |  | [optional] [default to undefined]
**blockedByUserId** | **string** |  | [optional] [default to undefined]
**hasPendingTransfer** | **boolean** |  | [optional] [default to undefined]

## Example

```typescript
import { SearchUserDataResponseModel } from './api';

const instance: SearchUserDataResponseModel = {
    id,
    eln,
    firstName,
    surname,
    familyName,
    fullName,
    op,
    regionId,
    region,
    cityId,
    city,
    clientNumber,
    phoneNumber,
    displayName,
    iptuName,
    position,
    adAccount,
    email,
    isMolOfOp,
    opcode,
    schenkerId,
    blocked,
    dataBlocked,
    blockedByUserId,
    hasPendingTransfer,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
