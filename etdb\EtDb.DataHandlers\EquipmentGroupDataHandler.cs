﻿namespace EtDb.DataHandlers
{
    using EtDb.ApiClients.EtDb.Context;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class EquipmentGroupDataHandler : BaseDataHandler, IEquipmentGroupDataHandler
    {
        private Lazy<IEquipmentTypeDataHandler> equipmentTypeDataHandler;

        public EquipmentGroupDataHandler(Lazy<EtDbContext> dbContext, Lazy<IEquipmentTypeDataHandler> equipmentTypeDataHandler)
            : base(dbContext)
        {
            this.equipmentTypeDataHandler = equipmentTypeDataHandler;
        }

        public IQueryable<EquipmentGroups> GetEquipmentGroups()
        {
            return dbContext.Value.EquipmentGroups;
        }

        public IQueryable<string> GetEquipmentGroupsNames()
        {
            return this.GetEquipmentGroups().Select(eg => eg.Name);
        }

        public async Task<string?> GetEquipmentGroupNameAsync(string sapMaterialNum)
        {
            return await this.equipmentTypeDataHandler.Value
                .GetEquipmentTypes()
                .Where(e => e.SapmaterialNum == sapMaterialNum)
                .Select(e => e.EquipmentGroup.Name)
                .FirstOrDefaultAsync();
        }

        public async Task<EquipmentGroups?> GetEquipmentGroupByIdAsync(int equipmentGroupId)
        {
            return await this.dbContext.Value.EquipmentGroups.FindAsync(equipmentGroupId);
        }
    }
}
