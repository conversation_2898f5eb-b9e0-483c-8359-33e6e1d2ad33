﻿namespace EtDb.Api.Controllers
{
    using System.Collections.Generic;

    using EtDb.Api.Infrastructure.Utils;
    using EtDb.Models.Responses;
    using EtDb.Services.Interfaces;

    public class WarehousesController : BaseApiController
    {
        private readonly Lazy<IWarehousesService> warehousesService;

        public WarehousesController(Lazy<IWarehousesService> warehousesService)
        {
            this.warehousesService = warehousesService;
        }

        [HttpGet("select-list")]
        [ProducesResponseType(typeof(IEnumerable<WarehousesResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<WarehousesResponseModel>>> GetWarehousesSelectListAsync()
        {
            try
            {
                var warehouses = await this.warehousesService.Value.GetWarehousesSelectListAsync();
                return this.Ok(warehouses);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("by-region")]
        [ProducesResponseType(typeof(IEnumerable<WarehousesResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<WarehousesResponseModel>>> GetWarehousesByRegionAsync([FromQuery] int? region)
        {
            try
            {
                var warehousesByRegion = await this.warehousesService.Value.GetWarehousesByRegionAsync(region);
                return this.Ok(warehousesByRegion);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
