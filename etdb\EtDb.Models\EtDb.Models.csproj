﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <CodeAnalysisRuleSet>../Rules.ruleset</CodeAnalysisRuleSet>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <LangVersion>Latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <Using Include="System.ComponentModel.DataAnnotations" />
    <Using Include="Net.Common" />
    <Using Include="EtDb.Infrastructure.Constants" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Net.Common" Version="0.2.20" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
    <PackageReference Include="SonarAnalyzer.CSharp" Version="8.35.0.42613">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EtDb.ApiClients\EtDb.ApiClients.csproj" />
    <ProjectReference Include="..\EtDb.DataHandlers\EtDb.DataHandlers.csproj" />
    <ProjectReference Include="..\EtDb.Infrastructure\EtDb.Infrastructure.csproj" />
  </ItemGroup>

</Project>
