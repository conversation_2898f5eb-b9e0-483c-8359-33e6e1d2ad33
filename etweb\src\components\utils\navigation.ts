export const navigation = [
  {
    name: "equipment",
    items: [
      { name: "availableEquipment", href: "/equipment/available" },
      { name: "dailyEquipment", href: "/equipment/daily" },
      { name: "transfer", href: "/equipment/transfer" },
      { name: "acceptance", href: "/equipment/accept" },
      { name: "cancellationOfTransfer", href: "/equipment/cancel" },
    ],
  },
  {
    name: "reports",
    items: [
      { name: "equipmentMovement", href: "/reports/equipment-movement" },
      { name: "criticalQuantitiesReport", href: "/reports/critical-quantities" },
      { name: "availableEquipment", href: "/reports/available-equipment" },
      { name: "availableEquipmentByOp", href: "/reports/available-equipment-by-op" },
      { name: "unusedEquipment", href: "/reports/unused-equipment" },
      { name: "equipmentTransfers", href: "/reports/equipment-transfers" },
      { name: "incorrectMovements", href: "/reports/incorrect-movements" },
      { name: "waitingForConfirmation", href: "/reports/waiting-for-confirmation" },
      { name: "itemsCount", href: "/reports/items-count" },
    ],
  },
  {
    name: "aggregations",
    items: [
      { name: "incorrectMovements", href: "/aggregations/incorrect-movements" },
      { name: "equipmentMovement", href: "/aggregations/equipment-movement" },
      { name: "unusedEquipment", href: "/aggregations/unused-equipment" },
    ],
  },
  {
    name: "administration",
    items: [
      { name: "products", href: "/administration/products" },
      { name: "users", href: "/administration/users" },
      { name: "cities", href: "/administration/cities" },
      { name: "schenkers", href: "/administration/schenkers" },
      { name: "criticalQuantitiesAdministration", href: "/administration/critical-quantities" },
      { name: "sapElementCodes", href: "/administration/sap-element-codes" },
      { name: "monthlyOrderQuantities", href: "/administration/monthly-order-quantities" },
      { name: "substitutions", href: "/administration/substitutions" },
      { name: "transferCorrection", href: "/transfer-correction" },
    ],
  },
  {
    name: "requests",
    items: [
      { name: "newRequest", href: "/transfers/new" },
      { name: "allRequests", href: "/transfers/all" },
      { name: "monthlyOrderQuantities", href: "/monthly-order-quantities/load" },
      { name: "monthlyOrderQuantitiesSum", href: "/monthly-order-quantities/load-sum" },
    ],
  },
];