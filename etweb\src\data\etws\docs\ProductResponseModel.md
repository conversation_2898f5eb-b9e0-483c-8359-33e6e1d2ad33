# ProductResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**description** | **string** |  | [optional] [default to undefined]
**name** | **string** |  | [optional] [default to undefined]
**partNum** | **string** |  | [optional] [default to undefined]
**xaClassName** | **string** |  | [optional] [default to undefined]
**relationshipList** | [**Array&lt;ProductRelationship&gt;**](ProductRelationship.md) |  | [optional] [default to undefined]

## Example

```typescript
import { ProductResponseModel } from './api';

const instance: ProductResponseModel = {
    description,
    name,
    partNum,
    xaClassName,
    relationshipList,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
