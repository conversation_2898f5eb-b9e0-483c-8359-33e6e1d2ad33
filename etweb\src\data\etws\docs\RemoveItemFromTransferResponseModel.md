# RemoveItemFromTransferResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**removedEquipmentTypeName** | **string** |  | [optional] [default to undefined]
**reservedItemsCount** | **number** |  | [optional] [default to undefined]

## Example

```typescript
import { RemoveItemFromTransferResponseModel } from './api';

const instance: RemoveItemFromTransferResponseModel = {
    removedEquipmentTypeName,
    reservedItemsCount,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
