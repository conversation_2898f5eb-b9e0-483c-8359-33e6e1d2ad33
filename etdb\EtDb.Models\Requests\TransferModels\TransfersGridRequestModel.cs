namespace EtDb.Models.Requests.TransferModels
{
    using ET.Database.Enums;

    public class TransfersGridRequestModel
    {
        public string OpCode { get; set; }

        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }

        public TransferStatus? Status { get; set; }

        public string Query { get; set; }

        public string SortBy { get; set; }

        public string Dir { get; set; }

        public int Page { get; set; }

        public int PageDataSize { get; set; }
    }
}
