# ProductDataRequestModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**name** | **string** |  | [default to undefined]
**sapMaterialNum** | **string** |  | [default to undefined]
**equipmentNameFirst** | **string** |  | [default to undefined]
**matFirst** | **string** |  | [default to undefined]
**minimumQuantity** | **number** |  | [default to undefined]
**sapRequestType** | **number** |  | [default to undefined]
**brProjectName** | **string** |  | [optional] [default to undefined]
**sapElementCode** | **string** |  | [optional] [default to undefined]
**equipmentGroupId** | **number** |  | [default to undefined]
**unitOfMeasure** | **number** |  | [default to undefined]
**boxCapacity** | **number** |  | [optional] [default to undefined]

## Example

```typescript
import { ProductDataRequestModel } from './api';

const instance: ProductDataRequestModel = {
    id,
    name,
    sapMaterialNum,
    equipmentNameFirst,
    matFirst,
    minimumQuantity,
    sapRequestType,
    brProjectName,
    sapElementCode,
    equipmentGroupId,
    unitOfMeasure,
    boxCapacity,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
