﻿namespace EtDb.DataHandlers
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using EtDb.ApiClients.EtDb.Context;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class SnapshotDateDataHandler : BaseDataHandler, ISnapshotDateDataHandler
    {
        public SnapshotDateDataHandler(Lazy<EtDbContext> dbContext)
            : base(dbContext)
        {
        }

        public IQueryable<SnapshotDates> GetAll()
        {
            return this.dbContext.Value.SnapshotDates;
        }

        public async Task<SnapshotDates> AddAsync(SnapshotDates snapshotDate)
        {
            await this.dbContext.Value.SnapshotDates.AddAsync(snapshotDate);
            return snapshotDate;
        }

        public async Task<SnapshotDates?> FindAvailableSnapshotForDateAsync(DateTime date)
        {
            var allSnapshots = this.GetAll();
            var foundSnapShot = await allSnapshots
                .Where(x => x.Date.Date == date.Date &&
                            x.AvailableEquipmentByOpsnapshots.Any()) // TODO: IsDeleted Field!
                .OrderByDescending(x => x.Date)
                .FirstOrDefaultAsync();

            return foundSnapShot;
        }

        public async Task<SnapshotDates?> FindLastAvailableSnapshotForDateAsync(DateTime date)
        {
            var allSnapshots = this.GetAll();
            var foundSnapShot = await allSnapshots
                .Where(x => x.Date <= date
                    && x.AvailableEquipmentByOpsnapshots.Count() > 0) // TODO: IsDeleted Field!
                .OrderByDescending(x => x.Date)
                .FirstOrDefaultAsync();

            return foundSnapShot;
        }

        public async Task<List<SnapshotDates>> FindAllSnapshotsWithDataBetweenDatesAsync(DateTime fromDate, DateTime toDate, bool includeLastAvailableBeforeFromDate = true)
        {
            var allSnapshotsWithData = await this.GetAll()
                .Where(x => x.AvailableEquipmentByOpsnapshots.Count() > 0) // TODO: IsDeleted Field!
                .Select(x => new SnapshotDates
                {
                    Date = x.Date,
                    Id = x.Id
                })
                .OrderByDescending(x => x.Date)
                .ToListAsync();

            var result = new List<SnapshotDates>();

            foreach (var snapshot in allSnapshotsWithData)
            {
                if (snapshot.Date <= toDate)
                {
                    if (fromDate <= snapshot.Date)
                    {
                        result.Add(snapshot);
                    }
                    else
                    {
                        if (includeLastAvailableBeforeFromDate)
                        {
                            result.Add(snapshot);
                        }

                        break;
                    }
                }
            }

            return result;
        }
    }
}
