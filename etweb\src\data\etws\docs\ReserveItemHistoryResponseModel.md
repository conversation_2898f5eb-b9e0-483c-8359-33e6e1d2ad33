# ReserveItemHistoryResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**itemId** | **string** |  | [optional] [default to undefined]
**itemEquipmentTypeName** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { ReserveItemHistoryResponseModel } from './api';

const instance: ReserveItemHistoryResponseModel = {
    itemId,
    itemEquipmentTypeName,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
