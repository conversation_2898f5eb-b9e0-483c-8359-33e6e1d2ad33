﻿namespace EtWS.Api.Infrastructure.Mapper
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.UserModels;

    public class ProductProfile : Profile
    {
        public ProductProfile()
        {
            this.CreateMap<AdministrateProductResponseModelSearchResponseModel, SearchResponseModel<SearchProductDataResponseModel>>()
                .ForMember(r => r.Count, opt => opt.MapFrom(c => c.Count));

            this.CreateMap<ProductDataRequestModel, AdministrateProductRequestModel>();

            this.CreateMap<AdministrateProductResponseModel, SearchProductDataResponseModel>();
        }
    }
}
