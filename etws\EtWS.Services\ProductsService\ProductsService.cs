﻿namespace EtWS.Services.ProductsService
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.UserModels;

    public class ProductsService : IProductsService
    {
        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<IETDB> etDb;

        public ProductsService(Lazy<IMapper> mapper, Lazy<IETDB> etDb)
        {
            this.mapper = mapper;
            this.etDb = etDb;
        }

        public async Task<SearchProductDataResponseModel> GetProductByIdAsync(int id)
        {
            var currentProduct = await this.etDb.Value.ApiProductGetAsync(id);
            return this.mapper.Value.Map<SearchProductDataResponseModel>(currentProduct);
        }

        public async Task<SearchResponseModel<SearchProductDataResponseModel>> GetProductsAsync(SearchDataRequestModel request)
        {
            var dbRequest = this.mapper.Value.Map<SearchRequestModel>(request);
            var filteredProducts = await this.etDb.Value.ApiProductSearchPostAsync(body: dbRequest);
            var productsResponse = this.mapper.Value.Map<SearchResponseModel<SearchProductDataResponseModel>>(filteredProducts);

            return productsResponse;
        }

        public async Task<int> AddProductAsync(ProductDataRequestModel request)
        {
            var dbRequest = this.mapper.Value.Map<AdministrateProductRequestModel>(request);
            return await this.etDb.Value.ApiProductInsertPostAsync(body: dbRequest);
        }

        public async Task UpdateProductAsync(ProductDataRequestModel request)
        {
            var dbRequest = this.mapper.Value.Map<AdministrateProductRequestModel>(request);
            await this.etDb.Value.ApiProductUpdatePutAsync(body: dbRequest);
        }
    }
}
