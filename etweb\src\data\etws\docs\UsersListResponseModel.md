# UsersListResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**usersSelectListResponseModels** | **{ [key: string]: Array&lt;UsersSelectListResponseModel&gt;; }** |  | [optional] [default to undefined]
**iptuListResponseModel** | **Array&lt;string&gt;** |  | [optional] [default to undefined]

## Example

```typescript
import { UsersListResponseModel } from './api';

const instance: UsersListResponseModel = {
    usersSelectListResponseModels,
    iptuListResponseModel,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
