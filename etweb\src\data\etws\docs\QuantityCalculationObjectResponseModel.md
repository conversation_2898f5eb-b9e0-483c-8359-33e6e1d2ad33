# QuantityCalculationObjectResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**name** | **string** |  | [optional] [default to undefined]
**criticalQuantity** | **number** |  | [optional] [default to undefined]
**isGroupWithSubstitutes** | **boolean** |  | [optional] [default to undefined]
**isManuallySet** | **boolean** |  | [optional] [default to undefined]
**toggleToAutomaticGeneration** | **boolean** |  | [optional] [default to undefined]
**lastUpdateDate** | **string** |  | [optional] [default to undefined]
**opCode** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { QuantityCalculationObjectResponseModel } from './api';

const instance: QuantityCalculationObjectResponseModel = {
    id,
    name,
    criticalQuantity,
    isGroupWithSubstitutes,
    isManuallySet,
    toggleToAutomaticGeneration,
    lastUpdateDate,
    opCode,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
