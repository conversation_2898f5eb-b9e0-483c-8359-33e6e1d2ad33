import axios from 'axios';
import toast from 'react-hot-toast';

// Create a flag to prevent multiple logout calls
let isLoggingOut = false;

// Track recent toast messages to prevent duplicates
const recentToasts = new Map<string, number>();
const TOAST_DEDUPE_WINDOW = 5000; // 5 seconds

const showUniqueToast = (message: string, type: 'error' | 'success' = 'error') => {
  const now = Date.now();
  const lastShown = recentToasts.get(message);

  // If this message was shown recently, don't show it again
  if (lastShown && (now - lastShown) < TOAST_DEDUPE_WINDOW) {
    return;
  }

  // Show the toast and record the timestamp
  recentToasts.set(message, now);

  // Clean up old entries to prevent memory leaks
  for (const [msg, timestamp] of recentToasts.entries()) {
    if (now - timestamp > TOAST_DEDUPE_WINDOW) {
      recentToasts.delete(msg);
    }
  }

  if (type === 'error') {
    toast.error(message);
  } else {
    toast.success(message);
  }
};

interface AuthAction {
  type: string;
  payload?: unknown;
}

type NavigateFunction = (to: string, options?: { replace?: boolean }) => void;

export const setupAxiosInterceptors = (
  dispatch: React.Dispatch<AuthAction>,
  navigate: NavigateFunction
) => {
  // Response interceptor to handle authentication errors
  const responseInterceptor = axios.interceptors.response.use(
    (response) => {
      // If we get a successful response, reset the logout flag
      if (isLoggingOut) {
        isLoggingOut = false;
      }
      return response;
    },
    (error) => {
      // Handle 401 Unauthorized responses (expired session/cookie)
      if (error.response?.status === 401 && !isLoggingOut) {
        isLoggingOut = true;
        showUniqueToast("Your session has expired. Please log in again.");

        // Check if user was previously logged in (has user data in localStorage)
        const userData = localStorage.getItem("user");
        const wasLoggedIn = userData && userData !== "null";

        // Clear user data and set appropriate state
        if (wasLoggedIn) {
          dispatch({ type: "SESSION_EXPIRED" });
        } else {
          dispatch({ type: "LOGOUT" });
        }

        // Clear localStorage
        localStorage.removeItem("user");

        // Redirect to login page with current URL as return parameter
        const currentPath = window.location.pathname + window.location.search;
        const loginUrl = `/login?url=${encodeURIComponent(currentPath)}`;

        // Use React Router navigation instead of window.location
        navigate(loginUrl, { replace: true });
      }
      else {
        showUniqueToast(error.response?.data?.message || "An error occurred. Please try again later.");
      }
      return Promise.reject(error);
    }
  );

  // Return cleanup function
  return () => {
    axios.interceptors.response.eject(responseInterceptor);
  };
};
