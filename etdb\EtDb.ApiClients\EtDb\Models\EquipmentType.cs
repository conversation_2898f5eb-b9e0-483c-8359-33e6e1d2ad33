﻿using System.Collections.Generic;

namespace EtDb.ApiClients.EtDb.Models
{
    public partial class EquipmentType
    {
        public EquipmentType()
        {
            AvailableEquipmentByOpsnapshots = new HashSet<AvailableEquipmentByOpsnapshots>();
            Item = new HashSet<Item>();
            QuantityCalculationObject = new HashSet<QuantityCalculationObject>();
            TransferListItems = new HashSet<TransferListItems>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string SapmaterialNum { get; set; }
        public int SerialNumberRequired { get; set; }
        public int EquipmentGroupId { get; set; }
        public int UnitOfMeasure { get; set; }
        public int SendMethod { get; set; }
        public string SapsupplyCode { get; set; }
        public int MinimumQuantity { get; set; }
        public bool IsTransferFromSapAllowed { get; set; }
        public int? BoxCapacity { get; set; }
        public int? LastMonthlyOrderQuantity { get; set; }
        public string BrprojectCode { get; set; }

        public virtual EquipmentGroups EquipmentGroup { get; set; }
        public virtual ICollection<AvailableEquipmentByOpsnapshots> AvailableEquipmentByOpsnapshots { get; set; }
        public virtual ICollection<Item> Item { get; set; }
        public virtual ICollection<QuantityCalculationObject> QuantityCalculationObject { get; set; }
        public virtual ICollection<TransferListItems> TransferListItems { get; set; }
    }
}
