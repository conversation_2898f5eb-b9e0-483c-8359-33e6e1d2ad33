# EquipmentTypeApi

All URIs are relative to *http://microit9app1.drcenter.btk.bg:26121/et-ws*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**apiEquipmentTypeAllGet**](#apiequipmenttypeallget) | **GET** /api/equipment-type/all | |
|[**apiEquipmentTypeByIdByIdGet**](#apiequipmenttypebyidbyidget) | **GET** /api/equipment-type/by-id/{id} | |
|[**apiEquipmentTypeBySendMethodBySendMethodGet**](#apiequipmenttypebysendmethodbysendmethodget) | **GET** /api/equipment-type/by-send-method/{sendMethod} | |
|[**apiEquipmentTypeConciseEquipmentTypesPost**](#apiequipmenttypeconciseequipmenttypespost) | **POST** /api/equipment-type/concise-equipment-types | |
|[**apiEquipmentTypeLastMonthQuantitiesGet**](#apiequipmenttypelastmonthquantitiesget) | **GET** /api/equipment-type/last-month-quantities | |
|[**apiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet**](#apiequipmenttypetypeidbysapmaterialnumbysapmaterialnumget) | **GET** /api/equipment-type/type-id-by-sap-material-num/{sapMaterialNum} | |

# **apiEquipmentTypeAllGet**
> Array<EquipmentTypeResponseModel> apiEquipmentTypeAllGet()


### Example

```typescript
import {
    EquipmentTypeApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentTypeApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentTypeAllGet(
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**Array<EquipmentTypeResponseModel>**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentTypeByIdByIdGet**
> EquipmentTypeResponseModel apiEquipmentTypeByIdByIdGet()


### Example

```typescript
import {
    EquipmentTypeApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentTypeApi(configuration);

let id: number; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentTypeByIdByIdGet(
    id,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**EquipmentTypeResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentTypeBySendMethodBySendMethodGet**
> number apiEquipmentTypeBySendMethodBySendMethodGet()


### Example

```typescript
import {
    EquipmentTypeApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentTypeApi(configuration);

let sendMethod: string; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentTypeBySendMethodBySendMethodGet(
    sendMethod,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **sendMethod** | [**string**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**number**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentTypeConciseEquipmentTypesPost**
> Array<EquipmentTypeConciseDto> apiEquipmentTypeConciseEquipmentTypesPost(conciseEquipmentTypesDataRequestModel)


### Example

```typescript
import {
    EquipmentTypeApi,
    Configuration,
    ConciseEquipmentTypesDataRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentTypeApi(configuration);

let conciseEquipmentTypesDataRequestModel: ConciseEquipmentTypesDataRequestModel; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentTypeConciseEquipmentTypesPost(
    conciseEquipmentTypesDataRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **conciseEquipmentTypesDataRequestModel** | **ConciseEquipmentTypesDataRequestModel**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**Array<EquipmentTypeConciseDto>**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentTypeLastMonthQuantitiesGet**
> { [key: string]: number; } apiEquipmentTypeLastMonthQuantitiesGet()


### Example

```typescript
import {
    EquipmentTypeApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentTypeApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentTypeLastMonthQuantitiesGet(
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**{ [key: string]: number; }**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet**
> number apiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet()


### Example

```typescript
import {
    EquipmentTypeApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EquipmentTypeApi(configuration);

let sapMaterialNum: string; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet(
    sapMaterialNum,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **sapMaterialNum** | [**string**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**number**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

