﻿using AutoMapper;
using EtDb.ApiClients.EtDb.Context;
using EtDb.ApiClients.EtDb.Models;
using EtDb.DataHandlers.Interfaces;
using EtDb.DataHandlers.Models;
using EtDb.Infrastructure.Enumerations;
using Microsoft.EntityFrameworkCore;

namespace EtDb.DataHandlers
{
    public class CitiesDataHandler : BaseDataHandler, ICitiesDataHandler
    {
        private readonly Lazy<IMapper> mapper;

        public CitiesDataHandler(Lazy<EtDbContext> dbContext, Lazy<IMapper> mapper)
            : base(dbContext)
        {
            this.mapper = mapper;
        }

        public async Task<CitiesDto> GetCityByIdAsync(int id)
        {
            var result = await this.dbContext.Value.Cities.FirstOrDefaultAsync(c => c.Id == id);

            if (result == null)
            {
                throw new ArgumentException($"No city has been found for id - {id}");
            }

            return this.mapper.Value.Map<CitiesDto>(result);
        }

        public async Task<FilteredDataModel<CitiesDto>> GetFilteredCitiesAsync(string sortBy, string sortDir, int pageNumber, int pageSize, string query)
        {
            var collection = this.dbContext.Value.Cities.AsQueryable();


            var mappedCollection = collection.Select(c => this.mapper.Value.Map<CitiesDto>(c));

            FilteredDataModel<CitiesDto> filteredGridData = await this.GetFilteredData(mappedCollection, sortBy, sortDir, pageNumber, pageSize, query);

            return filteredGridData;
        }

        public async Task<int> AddCityAsync(CitiesDto request)
        {
            bool existingCity = this.dbContext.Value.Cities.Any(c => c.Name == request.Name);
            if (existingCity)
            {
                throw new ArgumentException($"A city with the name '{request.Name}' already exists.");
            }

            var city = this.mapper.Value.Map<Cities>(request);
            city.CreatedOn = DateTime.UtcNow;
            city.CreatedBy = request.Username;

            await this.dbContext.Value.Cities.AddAsync(city);
            await this.dbContext.Value.SaveChangesAsync();

            return city.Id;
        }

        public async Task UpdateCityAsync(CitiesDto request)
        {
            var city = await this.dbContext.Value.Cities.FirstOrDefaultAsync(c => c.Id == request.Id);

            if (city == null)
            {
                throw new ArgumentException($"No city has been found for id - {request.Id}");
            }

            city.Name = request.Name;
            city.Region = request.Region;
            city.SapcityCode = request.SAPCityCode;
            city.Cluster = request.Cluster;
            city.ModifiedOn = DateTime.UtcNow;
            city.ModifiedBy = request.Username;

            this.dbContext.Value.Cities.Update(city);
            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task BlockCitiesAsync(List<int> ids)
        {
            List<Cities> cities = await CheckCitiesAsync(ids);
            foreach (var city in cities)
            {
                city.IsDeleted = true;
                city.DeletedOn = DateTime.UtcNow;
                city.ModifiedOn = DateTime.UtcNow;
            }
            this.dbContext.Value.Cities.UpdateRange(cities);
            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task ActivateCitiesAsync(List<int> ids)
        {
            var cities = await CheckCitiesAsync(ids);

            foreach (var city in cities)
            {
                city.IsDeleted = false;
                city.DeletedOn = null;
                city.ModifiedOn = DateTime.UtcNow;
            }
            this.dbContext.Value.Cities.UpdateRange(cities);
            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task<string> GetCityRegionByIdAsync(int id)
        {
            var result = await this.dbContext.Value.Cities.FirstOrDefaultAsync(c => c.Id == id);

            if (result == null)
            {
                throw new ArgumentException($"No city has been found for id - {id}");
            }

            return ((Regions)result.Region).ToString();
        }

        private async Task<List<Cities>> CheckCitiesAsync(List<int> ids)
        {
            var cities = await this.dbContext.Value.Cities.Where(c => ids.Contains(c.Id)).ToListAsync();

            var missingIds = ids.Except(cities.Select(c => c.Id)).ToList();
            if (missingIds.Any())
            {
                throw new ArgumentException($"The following city IDs were not found: {string.Join(", ", missingIds)}");
            }

            if (cities == null || !cities.Any())
            {
                throw new ArgumentException($"No cities have been found for the provided ids.");
            }

            return cities;
        }
    }
}
