# CityResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**name** | **string** |  | [optional] [default to undefined]
**region** | **number** |  | [optional] [default to undefined]
**isDeleted** | **boolean** |  | [optional] [default to undefined]
**sapCityCode** | **string** |  | [optional] [default to undefined]
**cluster** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { CityResponseModel } from './api';

const instance: CityResponseModel = {
    id,
    name,
    region,
    isDeleted,
    sapCityCode,
    cluster,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
