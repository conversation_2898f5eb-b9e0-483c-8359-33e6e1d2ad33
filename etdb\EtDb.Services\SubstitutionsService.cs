﻿namespace EtDb.Services
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Interfaces;
    using EtDb.Models.Requests;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.UserModels;
    using EtDb.Services.Interfaces;

    public class SubstitutionsService : ISubstitutionsService
    {
        private readonly Lazy<ISubstitutionsDataHandler> substitutionsDataHandler;
        private readonly Lazy<ISchenkerDataHandler> schenkerDataHandler;
        private readonly Lazy<IUserDataHandler> userDataHandler;
        private readonly Lazy<IMapper> mapper;

        public SubstitutionsService(Lazy<ISubstitutionsDataHandler> substitutionsD<PERSON><PERSON><PERSON><PERSON>, Lazy<ISchenkerDataHandler> schenkerDataHandler, Lazy<IUserDataHandler> userDataHandler, Lazy<IMapper> mapper)
        {
            this.substitutionsDataHandler = substitutionsDataHandler;
            this.schenkerDataHandler = schenkerDataHandler;
            this.userDataHandler = userDataHandler;
            this.mapper = mapper;
        }

        public async Task<SearchResponseModel<SubstitutionsResponseModel>> GetSubstitutions(SearchRequestModel request, bool isCurrentUserMol = false, FilterSubstitutionsRequestModel model = null)
        {
            string opCode = null;
            IQueryable<Substitutions> substitutions = null;

            if (model != null)
            {
                if (model.OpId != null)
                {
                    var schenker = await this.schenkerDataHandler.Value.GetSchenkerById(model.OpId.Value);
                    opCode = schenker.Opcode;
                }

                substitutions = await this.substitutionsDataHandler.Value.GetFilteredSubstitutions(opCode, model.FromDateFilter, model.ToDateFilter);
            }
            else
            {
                if (isCurrentUserMol)
                {
                    var user = await this.userDataHandler.Value.GetUserByIdAsync(model.UserId);
                    opCode = user.Opcode;
                }

                var substitutionsAll = await this.substitutionsDataHandler.Value.GetSubstitutions();

                substitutions = opCode != null
                    ? substitutionsAll.Where(s => s.Opcode == opCode)
                    : substitutionsAll;
            }

            var filteredSubstitutions = await this.substitutionsDataHandler.Value.GetFilteredSubstitutions(
                request.SortBy,
                request.SortDir,
                request.PageNumber,
                request.PageSize,
                request.Query,
                substitutions);

            var response = this.mapper.Value.Map<SearchResponseModel<SubstitutionsResponseModel>>(filteredSubstitutions);

            return response;
        }

        public async Task AddSubstitution(SubstitutionsRequestModel model)
        {
            int overlappingSubstitutionsCount = await this.GetOverlappingSubstitutionsCount(model.ForUserId, model.FromDate, model.ToDate);

            if (overlappingSubstitutionsCount > 0)
            {
                throw new InvalidOperationException("You cannot hace overlapping substitutions for the same user");
            }

            var forUser = await this.userDataHandler.Value.GetUserByIdAsync(model.ForUserId);
            var substitution = this.mapper.Value.Map<Substitutions>(model);
            substitution.Opcode = forUser.Opcode;
            substitution.ToDate = model.ToDate.AddDays(1).AddSeconds(-1);

            if (model.FromDate.Date <= DateTime.Today)
            {
                substitution.IsActive = true;
            }

            await this.substitutionsDataHandler.Value.AddSubstitution(substitution);
        }

        public async Task<SubstitutionsResponseModel> GetSubstitutionById(int id)
        {
            var substitution = await this.substitutionsDataHandler.Value.GetSubstitutionById(id);
            var response = new SubstitutionsResponseModel();

            if (substitution != null)
            {
                response = this.mapper.Value.Map<SubstitutionsResponseModel>(substitution);
            }

            return response;
        }

        public async Task EditSubstitution(int id, SubstitutionsRequestModel model)
        {
            var substitution = await this.substitutionsDataHandler.Value.GetSubstitutionById(id);
            substitution.ToDate = new DateTime(model.ToDate.Year, model.ToDate.Month, model.ToDate.Day, 23, 59, 59);
            substitution.IsActive = substitution.FromDate <= DateTime.Today && substitution.ToDate >= DateTime.Today;

            int overlappingSubstitutionsCount = await this.GetOverlappingSubstitutionsCount(substitution.ForUserId, substitution.FromDate, substitution.ToDate, substitution.Id);

            if (overlappingSubstitutionsCount > 0)
            {
                throw new InvalidOperationException("You cannot hace overlapping substitutions for the same user");
            }

            await this.substitutionsDataHandler.Value.EditSubstitution(substitution);
        }

        public async Task DeleteSubstitution(int id)
        {
            await this.substitutionsDataHandler.Value.DeleteSubstitution(id);
        }

        public async Task<IEnumerable<UserConciseResponseModel>> GetSubstituteUsers(string forUserId)
        {
            var user = await this.userDataHandler.Value.GetUserByIdAsync(forUserId);
            int? schenkerId = user?.SchenkerId;
            var schenkerUsers = this.userDataHandler.Value.GetAll().Where(u => u.SchenkerId == schenkerId);

            return this.mapper.Value.Map<IEnumerable<UserConciseResponseModel>>(schenkerUsers);
        }

        public async Task ActivateSubstitution(DateTime activationDate)
        {
            await this.ActivateSubstitution(activationDate);
        }

        public async Task DeactivateSubstitution(DateTime deactivationDate)
        {
            await this.DeactivateSubstitution(deactivationDate);
        }

        public async Task<SubstitutionsResponseModel> GetActiveSubstitutionsBySubstituteUserId(string substituteUserId)
        {
            var activeSubstitutions = await this.substitutionsDataHandler.Value.GetActiveSubstitutionBySubstituteUserAd(substituteUserId);

            return this.mapper.Value.Map<SubstitutionsResponseModel>(activeSubstitutions);
        }

        public async Task UpdateForUserIdWhenMolChanges(EditUsersRequestModel model)
        {
            var substitutions = await this.substitutionsDataHandler.Value.UpdateForUserIdWhenMolChanges(model.OP, model.Id);
        }

        private async Task<int> GetOverlappingSubstitutionsCount(string forUserId, DateTime fromDate, DateTime toDate, int substitutionId = 0)
        {
            var overlappingSubstitutions = await this.substitutionsDataHandler.Value.GetSubstitutions();
            return overlappingSubstitutions
                .Where(s => s.Id != substitutionId)
                .Where(s => s.ForUserId == forUserId)
                .Count(s =>
                    (s.FromDate <= fromDate && s.ToDate >= fromDate) ||
                    (s.FromDate <= toDate && s.ToDate >= toDate) ||
                    (s.FromDate <= fromDate && s.ToDate >= toDate) ||
                    (s.FromDate >= fromDate && s.ToDate <= toDate));
        }
    }
}
