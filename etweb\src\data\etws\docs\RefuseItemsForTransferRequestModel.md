# RefuseItemsForTransferRequestModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**selectedItems** | **Array&lt;number&gt;** |  | [optional] [default to undefined]
**refuseReason** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { RefuseItemsForTransferRequestModel } from './api';

const instance: RefuseItemsForTransferRequestModel = {
    selectedItems,
    refuseReason,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
