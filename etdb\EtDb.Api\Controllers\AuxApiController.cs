﻿namespace EtDb.Api.Controllers
{
    using EtDb.Api.Infrastructure.Utils;
    using EtDb.Models.Requests;
    using EtDb.Models.Responses;
    using EtDb.Services.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class AuxApiController : BaseApiController
    {
        private readonly Lazy<IAuxApiService> auxApiService;

        public AuxApiController(Lazy<IAuxApiService> auxApiService)
        {
            this.auxApiService = auxApiService;
        }

        [HttpPost]
        [Route("insert-item")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> InsertItemAsync([Required] InsertItemRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var id = await this.auxApiService.Value.InsertItemAsync(request);
                    return this.Ok(id);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (DbUpdateException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPut]
        [Route("update-item")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> UpdateItemAsync([Required] int equipmentId, [Required] UpdateItemRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    await this.auxApiService.Value.UpdateItemAsync(equipmentId, request);
                    return this.Ok(new BaseResponseModel { Success = true, Message = "Item updated successfully." });
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (ArgumentException arg)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPost]
        [Route("insert-history")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> InsertHistoryAsync([Required] InsertHistoryRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var id = await this.auxApiService.Value.InsertHistoryAsync(request);
                    return this.Ok(id);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (DbUpdateException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPut]
        [Route("update-history")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> UpdateHistoryAsync([Required] int equipmentId)
        {
            try
            {
                await this.auxApiService.Value.UpdateHistoryAsync(equipmentId);
                return this.Ok(new BaseResponseModel { Success = true, Message = "Histories updated successfully." });
            }
            catch (ArgumentException arg)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpPost]
        [Route("insert-available-equipment")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> InsertAvailableEquipmentAsync([Required] InsertAvailableEquipmentRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var id = await this.auxApiService.Value.InsertAvailableEquipmentAsync(request);
                    return this.Ok(id);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (DbUpdateException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPut]
        [Route("update-available-equipment")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> UpdateAvailableEquipmentAsync([Required] int equipmentId, [Required] UpdateAvailableEquipmentRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    await this.auxApiService.Value.UpdateAvailableEquipmentAsync(equipmentId, request);
                    return this.Ok(new BaseResponseModel { Success = true, Message = "Available Equipment updated successfully." });
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (ArgumentException arg)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpPost("insert-status-history-by-equipmentId")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> InsertStatusHistoryAsync([Required] int equipmentId)
        {
            try
            {
                await this.auxApiService.Value.InsertStatusHistoryAsync(equipmentId);
                return this.Ok(new BaseResponseModel { Success = true, Message = "Status Histories inserted successfully." });
            }
            catch (ArgumentException arg)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPost]
        [Route("insert-status-history")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> InsertStatusHistoryAsync([Required] InsertStatusHistoryRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var id = await this.auxApiService.Value.InsertStatusHistoryAsync(request);
                    return this.Ok(id);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (DbUpdateException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPost]
        [Route("insert-notification")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> InsertNotificationAsync([Required] string userId)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var id = await this.auxApiService.Value.InsertNotificationAsync(userId);
                    return this.Ok(id);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (DbUpdateException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPut]
        [Route("update-notification")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> UpdateNotificationAsync([Required] string userId)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    await this.auxApiService.Value.UpdateNotificationAsync(userId);
                    return this.Ok(new BaseResponseModel { Success = true, Message = "Available Notifications updated successfully." });
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (ArgumentException arg)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }
    }
}
