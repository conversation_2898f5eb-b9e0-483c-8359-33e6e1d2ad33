# CityRequestModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** |  | [default to undefined]
**region** | **number** |  | [default to undefined]
**sapCityCode** | **string** |  | [optional] [default to undefined]
**cluster** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { CityRequestModel } from './api';

const instance: CityRequestModel = {
    name,
    region,
    sapCityCode,
    cluster,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
