﻿namespace EtDb.DataHandlers
{
    using EtDb.ApiClients.EtDb.Context;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Interfaces;

    public class AvailableEquipmentByOPSnapShotDataHandler : BaseDataHandler, IAvailableEquipmentByOPSnapShotDataHandler
    {
        public AvailableEquipmentByOPSnapShotDataHandler(Lazy<EtDbContext> dbContext)
            : base(dbContext)
        {
        }

        public IQueryable<AvailableEquipmentByOpsnapshots> GetAll()
        {
            return this.dbContext.Value.AvailableEquipmentByOpsnapshots;
        }
    }
}
