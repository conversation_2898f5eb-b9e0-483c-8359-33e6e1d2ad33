﻿using EtDb.ApiClients.EtDb.Models;
using EtDb.DataHandlers.Models;

namespace EtDb.DataHandlers.Interfaces
{
    public interface ISubstitutionsDataHandler
    {
        Task<IQueryable<Substitutions>> GetFilteredSubstitutions(string opCode, DateTime fromDate, DateTime toDate);

        Task<IQueryable<Substitutions>> GetSubstitutions();

        Task<FilteredDataModel<Substitutions>> GetFilteredSubstitutions(string sortBy, string sortDir, int pageNumber, int pageSize, string query, IQueryable<Substitutions> substitutions);

        Task AddSubstitution(Substitutions substitution);

        Task<Substitutions?> GetSubstitutionById(int id);

        Task EditSubstitution(Substitutions substitution);

        Task DeleteSubstitution(int id);

        Task ActivateSubstitution(DateTime activationDate);

        Task DeactivateSubstitution(DateTime deactivationDate);

        Task<Substitutions?> GetActiveSubstitutionBySubstituteUserAd(string username);

        Task<IQueryable<Substitutions>> UpdateForUserIdWhenMolChanges(string OP, string id);
    }
}
