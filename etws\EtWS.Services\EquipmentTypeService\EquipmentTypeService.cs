﻿namespace EtWS.Services.EquipmentTypeService
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;

    public class EquipmentTypeService : IEquipmentTypeService
    {
        private readonly Lazy<IETDB> etDb;
        private readonly Lazy<IMapper> mapper;

        public EquipmentTypeService(Lazy<IETDB> etDb, Lazy<IMapper> mapper)
        {
            this.etDb = etDb;
            this.mapper = mapper;
        }

        public async Task<EquipmentTypeResponseModel> GetEquipmentTypeByIdAsync(int id)
        {
            return await this.etDb.Value.ApiEquipmentTypeByIdByIdGetAsync(id);
        }

        public async Task<IEnumerable<EquipmentTypeResponseModel>> GetEquipmentTypesAsync()
        {
            return await this.etDb.Value.ApiEquipmentTypeAllGetAsync();
        }

        public async Task<int> GetEquipmentTypeIdAsync(string sapMaterialNum)
        {
            return await this.etDb.Value.ApiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGetAsync(sapMaterialNum);
        }

        public async Task<IEnumerable<EquipmentTypeResponseModel>> GetEquipmentTypesAsync(string sendMethod)
        {
            return await this.etDb.Value.ApiEquipmentTypeBySendMethodBySendMethodGetAsync(sendMethod);
        }

        public async Task<IEnumerable<EquipmentTypeConciseDto>> GetConciseEquipmentTypesAsync(ConciseEquipmentTypesDataRequestModel request)
        {
            var dbRequest = this.mapper.Value.Map<ConciseEquipmentTypesRequestModel>(request);
            return await this.etDb.Value.ApiEquipmentTypeConciseEquipmentTypesPostAsync(dbRequest);
        }

        public async Task<IDictionary<string, int>> GetEquipmentTypesLastMonthQuantitiesAsync()
        {
            return await this.etDb.Value.ApiEquipmentTypeLastMonthQuantitiesGetAsync();
        }
    }
}
