﻿namespace EtWS.Models.Requests
{
    using EtWS.Infrastructure.Enumerations;

    public class EditUserRequest
    {
        [Required]
        public string Id { get; set; }

        public string ELN { get; set; }

        public string FirstName { get; set; }

        public string Surname { get; set; }

        public string FamilyName { get; set; }

        public string Status { get; set; }

        [Required]
        public int SchenkerId { get; set; }

        public string OP { get; set; }

        public string Type { get; set; }

        public string UserType { get; set; }

        public string ClientNumber { get; set; }

        public string City { get; set; }

        [Required]
        public int CityId { get; set; }

        public string Region { get; set; }

        public string IPTUName { get; set; }

        public string PhoneNumber { get; set; }

        public string StatusClass { get; set; }

        public string UserStatuses { get; set; }
    }
}
