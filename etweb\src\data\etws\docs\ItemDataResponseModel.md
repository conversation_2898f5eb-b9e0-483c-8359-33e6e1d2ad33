# ItemDataResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**icmIdSgwId** | **string** |  | [optional] [default to undefined]
**equipmentName** | **string** |  | [optional] [default to undefined]
**equipmentSerialNum** | **string** |  | [optional] [default to undefined]
**sapserialNum** | **string** |  | [optional] [default to undefined]
**sapmaterialNum** | **string** |  | [optional] [default to undefined]
**equipmentTypeId** | **number** |  | [optional] [default to undefined]
**equipmentMaterialGroup** | **string** |  | [optional] [default to undefined]
**typeOfUsage** | **number** |  | [optional] [default to undefined]
**equipmentValidity** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { ItemDataResponseModel } from './api';

const instance: ItemDataResponseModel = {
    id,
    icmIdSgwId,
    equipmentName,
    equipmentSerialNum,
    sapserialNum,
    sapmaterialNum,
    equipmentTypeId,
    equipmentMaterialGroup,
    typeOfUsage,
    equipmentValidity,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
