﻿namespace EtWS.Services.CriticalQuantitiesService
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;

    public interface ICriticalQuantitiesService : IService
    {
        Task<QuantityCalculationObjectResponseModelSearchResponseModel> SearchQuantityCalculationObjectsAsync(SearchDataRequestModel request);

        Task<QuantityCalculationObjectResponseModel> GetQuantityCalculationObjectByIdAsync(int id);

        Task UpdateQuantityCalculationObjectAsync(QuantityCalculationObjectRequestModel request);
    }
}
