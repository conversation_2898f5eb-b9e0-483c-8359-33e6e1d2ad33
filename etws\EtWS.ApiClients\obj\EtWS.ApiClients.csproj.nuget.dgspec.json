{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\EtWS.ApiClients.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\EtWS.ApiClients.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\EtWS.ApiClients.csproj", "projectName": "EtWS.ApiClients", "projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\EtWS.ApiClients.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"D:\\LocalNugetPackages": {}, "http://10.25.57.20/nuget/nuget": {}, "http://nxrm.btk.bg:8081/repository/nuget-group/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Net.Common": {"target": "Package", "version": "[0.2.20, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}