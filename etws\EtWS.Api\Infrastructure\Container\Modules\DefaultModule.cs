﻿namespace EtWS.Api.Infrastructure.Container.Modules
{
    using Autofac;
    using EtWS.Services;
    using Net.Autorest.Intercepception;

    public class DefaultModule : Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            //Conventional bindings
            builder.RegisterAssemblyTypes(typeof(IService).Assembly)
                .Where(x => x.IsClass && x.GetInterfaces().Any(y => y == typeof(IService)))
                .AsImplementedInterfaces()
                .InstancePerLifetimeScope();

            builder.RegisterType<RequestIdAndTimeStampInterceptor>();
            builder.RegisterType<EnsureSuccessStatusCodeInterceptor>();
        }
    }
}
