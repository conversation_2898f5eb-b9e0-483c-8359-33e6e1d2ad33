﻿namespace EtWS.Api.Infrastructure.Extensions
{
    using System.Reflection;
    using System.Text.Json.Serialization;

    using EtWs.ApiClients.EsbWs;
    using EtWs.ApiClients.NotificationsSender;
    using EtWS.ApiClients.ETDB;
    using EtWS.Infrastructure.Models.Infrastructure;
    using Hellang.Middleware.ProblemDetails;
    using Microsoft.AspNetCore.Authentication.Cookies;
    using Net.Ams.Extensions;
    using Net.Common.Mvc.Extensions;
    using Net.HealthChecks;
    using Net.Logging.Extensions;
    using Net.Middlewares.RequestId;
    using Net.Middlewares.Username;
    using Net.Swashbuckle;
    using Net.Tracing;
    using Steeltoe.Connector.Redis;
    using Steeltoe.Discovery.Eureka;

    public static class WebServiceConfigurationExtensions
    {
        public static IServiceCollection AddWebService(
            this IServiceCollection services,
            IConfiguration configuration)
            => services
                .Configure<ApiBehaviorOptions>(options => options.SuppressModelStateInvalidFilter = true)
                .AddAutoMapper(Assembly.GetExecutingAssembly())
                .AddCors()
                .AddCookieAuthentication()
                .AddDateTimeProvider()
                .AddDistributedRedisCache(configuration, addSteeltoeHealthChecks: true)
                .AddHealthChecksAndUI(configuration)
                .AddHttpClients(configuration)
                .AddJaegerTracing(options => options.ServiceName = WebServiceHelpers.GetApiName(configuration))
                .AddLogging(configuration)
                .AddMvcComponents()
                .AddOptions(configuration)
                .AddProblemDetails(WebServiceHelpers.SetDefaultProblemDetailsOptions)
                .AddRequestId("EQT")
                .AddResponseCaching()
                .AddSwagger(configuration)
                .AddUserManager(configuration)
                .AddSingleton<IHealthCheckHandler, ScopedEurekaHealthCheckHandler>()
                .AddUsername();

        public static IApplicationBuilder UseWebService(
            this IApplicationBuilder app,
            IConfiguration configuration)
            => app
                .UseInnerRequestResponseLogging()
                .UseProblemDetails()
                .UseResponseCaching()
                .UseAllHealthChecksEndpoints()
                .UseRequestId(proxyRequestId: true)
                .UseRouting()
                .UseUsername()
                .UseCors(x => WebServiceHelpers.AllowAnyOriginPolicy(x))
                .UseAuthentication()
                .UseAuthorization()
                .UseEndpoints(endpoints => endpoints.MapControllers())
                .UseSwagger(configuration);

        public static IServiceCollection AddMvcComponents(this IServiceCollection services)
        {
            services
                .AddMvc(WebServiceHelpers.SetDefaultMvcOptions)
                .AddControllersAsServices()
                .AddJsonOptions(options => options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter()));

            return services;
        }

        public static IServiceCollection AddHealthChecksAndUI(this IServiceCollection serviceCollection, IConfiguration configuration)
        {
            var redisConnectionString = configuration.GetConnectionString<RedisConnectionInfo>();

            serviceCollection
                .AddHealthChecks()
                .AddRedis(redisConnectionString: redisConnectionString);

            return serviceCollection;
        }

        public static IServiceCollection AddHttpClients(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddEurekaHttpClient<IETDB, ETDB>(configuration, nameof(ETDB));
            services.AddEurekaHttpClient<IEsbWs, EsbWs>(configuration, nameof(EsbWs));
            services.AddEurekaHttpClient<INotificationsSender, NotificationsSender>(configuration, nameof(NotificationsSender));
            return services;
        }

        public static IServiceCollection AddOptions(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddOptions();
            services.Configure<AppSettings>(configuration);

            services
                .Configure<Dictionary<string, string>>(
                    Constants.EndpointsConfigKey, configuration.GetSection(Constants.EndpointsConfigKey));

            return services;
        }

        public static IServiceCollection AddCookieAuthentication(this IServiceCollection services)
        {
            services
                 .AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
                 .AddCookie(CookieAuthenticationDefaults.AuthenticationScheme, options =>
                 {
                     options.ExpireTimeSpan = TimeSpan.FromDays(7); // Cookie expiration time
                     options.Cookie.MaxAge = options.ExpireTimeSpan;
                     options.SlidingExpiration = true; // Reset expiration time after each request
                     options.Cookie.HttpOnly = true;  // Make cookie HttpOnly
                     options.Cookie.SecurePolicy = CookieSecurePolicy.None;  // Only send cookie over HTTPS if request is secure; make always for prod
                     options.Cookie.SameSite = SameSiteMode.Strict;
                     options.Cookie.IsEssential = true;
                     options.Cookie.Name = "EtUserCookie";
                     options.Cookie.Path = "/";
                     options.Events = new CookieAuthenticationEvents
                     {
                         OnRedirectToLogin = context =>
                         {
                             context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                             return Task.CompletedTask;
                         },
                     };
                 });
            return services;
        }
    }
}
