# UserDisplayNameResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **string** |  | [optional] [default to undefined]
**displayName** | **string** |  | [optional] [default to undefined]
**iptuName** | **string** |  | [optional] [default to undefined]
**eln** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { UserDisplayNameResponseModel } from './api';

const instance: UserDisplayNameResponseModel = {
    id,
    displayName,
    iptuName,
    eln,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
