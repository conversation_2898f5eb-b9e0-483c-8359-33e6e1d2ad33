// <auto-generated/>
global using global::AutoMapper;
global using global::EtWS.Infrastructure.Constants;
global using global::EtWS.Infrastructure.Constants.ServicesConstants;
global using global::Microsoft.AspNetCore.Builder;
global using global::Microsoft.AspNetCore.Hosting;
global using global::Microsoft.AspNetCore.Http;
global using global::Microsoft.AspNetCore.Mvc;
global using global::Microsoft.AspNetCore.Routing;
global using global::Microsoft.Extensions.Configuration;
global using global::Microsoft.Extensions.DependencyInjection;
global using global::Microsoft.Extensions.Hosting;
global using global::Microsoft.Extensions.Logging;
global using global::Net.Common;
global using global::Net.Common.Mvc;
global using global::System;
global using global::System.Collections.Generic;
global using global::System.ComponentModel.DataAnnotations;
global using global::System.IO;
global using global::System.Linq;
global using global::System.Net.Http;
global using global::System.Net.Http.Json;
global using global::System.Threading;
global using global::System.Threading.Tasks;
