{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Services.Tests\\EtWS.Services.Tests.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\EtWS.ApiClients.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\EtWS.ApiClients.csproj", "projectName": "EtWS.ApiClients", "projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\EtWS.ApiClients.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"D:\\LocalNugetPackages": {}, "http://10.25.57.20/nuget/nuget": {}, "http://nxrm.btk.bg:8081/repository/nuget-group/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Net.Common": {"target": "Package", "version": "[0.2.20, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Api\\EtWS.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Api\\EtWS.Api.csproj", "projectName": "EtWS.Api", "projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Api\\EtWS.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Api\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"D:\\LocalNugetPackages": {}, "http://10.25.57.20/nuget/nuget": {}, "http://nxrm.btk.bg:8081/repository/nuget-group/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Services\\EtWS.Services.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Services\\EtWS.Services.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AspNetCore.HealthChecks.Redis": {"target": "Package", "version": "[6.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[11.0.0, )"}, "Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[7.2.0, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[6.0.25, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[6.0.2, )"}, "Net.Ams": {"target": "Package", "version": "[0.2.20, )"}, "Net.Autorest": {"target": "Package", "version": "[0.2.20, )"}, "Net.Common.Mvc": {"target": "Package", "version": "[0.2.20, )"}, "Net.HealthChecks": {"target": "Package", "version": "[0.2.20, )"}, "Net.Middlewares": {"target": "Package", "version": "[0.2.20, )"}, "Net.Steeltoe": {"target": "Package", "version": "[0.2.20, )"}, "Net.Swashbuckle": {"target": "Package", "version": "[0.2.20, )"}, "Net.Tracing": {"target": "Package", "version": "[0.2.20, )"}, "SonarAnalyzer.CSharp": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[8.35.0.42613, )"}, "Steeltoe.Connector.ConnectorCore": {"target": "Package", "version": "[3.1.3, )"}, "Steeltoe.Extensions.Configuration.ConfigServerCore": {"target": "Package", "version": "[3.1.3, )"}, "StyleCop.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[1.1.118, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Infrastructure\\EtWS.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Infrastructure\\EtWS.Infrastructure.csproj", "projectName": "EtWS.Infrastructure", "projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Infrastructure\\EtWS.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"D:\\LocalNugetPackages": {}, "http://10.25.57.20/nuget/nuget": {}, "http://nxrm.btk.bg:8081/repository/nuget-group/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"SonarAnalyzer.CSharp": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[8.35.0.42613, )"}, "StyleCop.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[1.1.118, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Models\\EtWS.Models.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Models\\EtWS.Models.csproj", "projectName": "EtWS.Models", "projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Models\\EtWS.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"D:\\LocalNugetPackages": {}, "http://10.25.57.20/nuget/nuget": {}, "http://nxrm.btk.bg:8081/repository/nuget-group/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\EtWS.ApiClients.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\EtWS.ApiClients.csproj"}, "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Infrastructure\\EtWS.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Infrastructure\\EtWS.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Net.Common": {"target": "Package", "version": "[0.2.20, )"}, "SonarAnalyzer.CSharp": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[8.35.0.42613, )"}, "StyleCop.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[1.1.118, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Services.Tests\\EtWS.Services.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Services.Tests\\EtWS.Services.Tests.csproj", "projectName": "EtWS.Services.Tests", "projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Services.Tests\\EtWS.Services.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Services.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"D:\\LocalNugetPackages": {}, "http://10.25.57.20/nuget/nuget": {}, "http://nxrm.btk.bg:8081/repository/nuget-group/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\EtWS.ApiClients.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\EtWS.ApiClients.csproj"}, "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Api\\EtWS.Api.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Api\\EtWS.Api.csproj"}, "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Services\\EtWS.Services.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Services\\EtWS.Services.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AutoFixture.Xunit2": {"target": "Package", "version": "[4.17.0, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[11.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.0.0, )"}, "Moq": {"target": "Package", "version": "[4.16.1, )"}, "xunit": {"target": "Package", "version": "[2.4.1, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.4.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Services\\EtWS.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Services\\EtWS.Services.csproj", "projectName": "EtWS.Services", "projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Services\\EtWS.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"D:\\LocalNugetPackages": {}, "http://10.25.57.20/nuget/nuget": {}, "http://nxrm.btk.bg:8081/repository/nuget-group/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\EtWS.ApiClients.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.ApiClients\\EtWS.ApiClients.csproj"}, "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Infrastructure\\EtWS.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Infrastructure\\EtWS.Infrastructure.csproj"}, "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Models\\EtWS.Models.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\ET-v2\\etws\\EtWS.Models\\EtWS.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[11.0.0, )"}, "Microsoft.Extensions.Caching.Abstractions": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[6.0.0, )"}, "Net.Ams": {"target": "Package", "version": "[0.2.20, )"}, "Net.Common.Mvc": {"target": "Package", "version": "[0.2.20, )"}, "StyleCop.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[1.1.118, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}