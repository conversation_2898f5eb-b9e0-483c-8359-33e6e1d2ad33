﻿namespace EtDb.ApiClients.EtDb.Enums
{
    using System.ComponentModel.DataAnnotations;

    public enum OperationType
    {
        [Display(Name = "Добавен от САП")]
        AddFromSAP = 0,

        [Display(Name = "Отписан от САП")]
        WriteOffFromSAP = 1,

        [Display(Name = "Добавен от Provisioning")]
        AddFromProvisioning = 2,

        [Display(Name = "Отписан от Provisioning")]
        WriteOffFromProvisioning = 3,

        [Display(Name = "Трансфер между техници")]
        TransferBetweenTechnicians = 4,

        [Display(Name = "Трансфер от ОП до ОП")]
        TransferOPtoOP = 5,
    }
}
