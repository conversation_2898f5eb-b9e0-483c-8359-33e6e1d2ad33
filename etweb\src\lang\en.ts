const en = {
  translation: {    // Common translations
    common: {
      loading: "Loading...",
      error: {
        loadingData: "An error occurred while loading data",
        occurred: "An error occurred",
        retryButton: "Try again"
      }
    },
    
    equipment: "Equipment",
    reports: "Reports",
    aggregations: "Aggregations",
    administration: "Administration",
    transferCorrection: "Transfer Correction",
    requests: "Requests",
    
    // Equipment submenu
    availableEquipment: "Available Equipment",
    dailyEquipment: "Daily Equipment",
    transfer: "Transfer",
    acceptance: "Acceptance",
    cancellationOfTransfer: "Cancellation of Transfer",
    
    // Reports submenu
    equipmentMovement: "Equipment Movement",
    criticalQuantitiesReport: "Critical Quantities Report",
    availableEquipmentByOp: "Available Equipment by OP",
    unusedEquipment: "Unused Equipment",
    equipmentTransfers: "Equipment Transfers",
    incorrectMovements: "Incorrect Movements",
    waitingForConfirmation: "Waiting for Confirmation",
    itemsCount: "Items Count",
    
    // Administration submenu
    products: "Products",
    users: "Users",
    cities: "Cities",
    schenkers: "Schenkers",
    criticalQuantitiesAdministration: "Critical Quantities Administration",
    sapElementCodes: "SAP Element Codes",
    monthlyOrderQuantities: "Monthly Order Quantities",
    substitutions: "Substitutions",

    // Users page
    manageSystemUsers: "Manage system users and their permissions",
    searchUsers: "Search users...",
    noUsersFound: "No users found",
    errorLoadingUsers: "Error loading users",
    activate: "Activate",
    block: "Block",
    active: "Active",
    blocked: "Blocked",
    edit: "Edit",
    eln: "ELN",
    fullName: "Full Name",
    op: "OP",
    region: "Region",
    city: "City",
    clientNumber: "Client Number",
    status: "Status",
    displayName: "Display Name",
    iptuName: "IPTU Name",
    adAccount: "AD Account",
    schenker: "Schenker",
    selectSchenker: "Select schenker...",
    selectStatus: "Select status...",
    editUser: "Edit User",
    editUserDescription: "Make changes to user information here.",
    save: "Save",
    saving: "Saving...",
    cancel: "Cancel",
    showingResults: "Showing {{start}}-{{end}} of {{total}} results",
    pageOfPages: "Page {{current}} of {{total}}",
    filters: "Filters",
    columnFilters: "Column Filters",
    clearAll: "Clear All",
    firstName: "First Name",
    surname: "Surname",
    enterEln: "Enter ELN...",
    enterFirstName: "Enter first name...",
    enterSurname: "Enter surname...",
    sortBy: "Sort By",
    sortDirection: "Sort Direction",
    ascending: "Ascending (A-Z)",
    descending: "Descending (Z-A)",
    familyName: "Family Name",
    enterFamilyName: "Enter family name...",
    enterClientNumber: "Enter client number...",
    position: "Position",
    email: "Email",
    opcode: "OP Code",
    schenkerId: "Schenker ID",
    dataBlocked: "Data Blocked",
    blockedByUserId: "Blocked By User ID",
    hasPendingTransfer: "Has Pending Transfer",
    select: "Select",
    enter: "Enter",

    // Search operators
    equals: "Equals",
    contains: "Contains",
    endsWith: "Ends With",
    startsWith: "Starts With",

    // Pagination
    noResults: "No results found",
    itemsPerPage: "Items per page",
    firstPage: "First page",
    previousPage: "Previous page",
    nextPage: "Next page",
    lastPage: "Last page",
    
    // Requests submenu
    newRequest: "New Request",
    allRequests: "All Requests",
    monthlyOrderQuantitiesSum: "Monthly Order Quantities Sum",    welcome: "Welcome to",
    ET: "E.T.",
    profile: "Profile",
    settings: "Settings",
    signOut: "Sign Out",
    signIn: "Sign In",
    transferTo: "Transfer to",
    serviceId: "Equipment Serial Number",
    enterSN: "Please enter the serial number.",
    selectAnOption: "Select an option",
    technician: "Technician",
    client: "Client",
    centralWarehouse: "Central Warehouse",
    SAPVirtualWarehouse: "SAP Virtual Warehouse",
    enterFirstAndLastName: "Please enter the first and last name of the technician.",
    firstAndLastName: "First and last name",
    enterTheService: "Please enter the service number (e.g., TV.123, LN.123).",    selectATechnicianFromTheList: "Select a technician from the list:",
    selectTechnician: "Select a technician",
    removeTransfer: "Remove transfer by date",
    selectRemovalDate: "Select Removal Date",
    pleaseSelectADate: "Please Select a Date",
    serviceIdError: "The serial number can only contain Latin letters and numbers.",
    transferAll: "Transfer All",
    transferCorrectionFromExel: "Transfer Correction from Excel",
    reviewDataFromExcel: "Review of the data from the Excel file:",
    // New keys
    serialNumber: "Serial Number",
    validating: "Validating...",
    serialNumberValidated: "Serial number is valid",
    serialNumberError: "The serial number can only contain Latin letters and numbers.",
    serialNumberRequired: "Serial number is required",
    transferToRequired: "Please select a transfer destination",
    technicianNameRequired: "Technician name is required",
    serviceIdRequired: "Service ID is required",
    opIdRequired: "Operation is required",
    removalDateRequired: "Removal date is required",
    selectOperation: "Select an operation",
    operationId: "Operation ID",
    loading: "Loading...",
    submit: "Submit",
    processing: "Processing...",
    transferSuccessful: "Transfer completed successfully",    transferFailed: "Transfer failed. Please try again.",
    transferToCentralWarehouse: "The equipment will be transferred to the central warehouse.",
    transferSAPVirtualWarehouse: "The equipment will be transferred to the SAP virtual warehouse.",
    searchTechnicians: "Search technicians",
    technicianName: "Technician Name",
    deliveryShopRequired: "Delivery shop is required",
    deleteIdRequired: "Removal date selection is required",
    deleteItemIdRequired: "Item ID is required",
    pleaseFix: "Please fix the following errors:",
    noTechniciansFound: "No technicians found",    serviceIdValidated: "Service ID is valid",
    invalidServiceId: "Invalid service ID",
    validationError: "Validation error occurred",
    errorOccurred: "An error occurred",
    serialNumberTooShort: "Serial number must be at least 3 characters long",
    serialNumberTooLong: "Serial number cannot exceed 50 characters",
    serialNumberValidationFailed: "Serial number validation failed",
    typeAtLeast2Characters: "Type at least 2 characters to search",
    searching: "Searching...",
    searchError: "Search failed. Please try again.",
    tryDifferentSearch: "Try a different search term",
    selectedTechnician: "Selected technician",    change: "Change",
    clearSelection: "Clear selection",
    invalidServiceIdFormat: "Invalid service ID format. Use: TV.123, LN.123, or LN-P.123",
    serviceIdValidationFailed: "Service ID validation failed",
    serviceIdFormatInfo: "Format: TV.123, LN.123, LN-P.123",
    validatingServiceId: "Validating service ID...",    serviceIdHelpText: "Enter a valid service ID in the format shown above",
    loginErrorStatus: "The Username or Password is incorrect.",
    
    // HomePage
    getStarted: "Get Started",
    learnMore: "Learn More",
    keyFeatures: "Key Features",
    fastProcessing: "Fast Processing",
    fastProcessingDesc: "Lightning-fast equipment tracking and real-time updates",
    premiumQuality: "Premium Quality", 
    premiumQualityDesc: "Enterprise-grade reliability and security standards",
    teamCollaboration: "Team Collaboration",
    teamCollaborationDesc: "Seamless collaboration tools for your entire team",
    readyToStart: "Ready to Get Started?",
    readyToStartDesc: "Join thousands of companies already using our equipment tracking system",
    startYourJourney: "Start Your Journey",
    advancedEMS: "Advanced Equipment Tracking and Management System",

    // UserEditForm translations
    userEditForm: {
      title: "Edit User",
      description: "Edit user information",
      fields: {
        eln: "ELN",
        firstName: "First Name",
        surname: "Surname",
        familyName: "Family Name",
        op: "OP",
        city: "City",
        region: "Region",
        molStatus: "MOL at OP",
        clientNumber: "Client Number",
        phoneNumber: "Phone Number"
      },
      placeholders: {
        selectOp: "Select OP",
        selectCity: "Select city",
        selectStatus: "Select status"
      },
      options: {
        yes: "Yes",
        no: "No"
      },
      buttons: {
        cancel: "Cancel",
        save: "Save",
        saving: "Saving..."
      }
    },
  },
};

export default en;
