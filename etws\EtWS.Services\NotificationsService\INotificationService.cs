﻿namespace EtWS.Services.NotificationsService
{
    using EtWS.ApiClients.ETDB;

    public interface INotificationService : IService
    {
        Task SendEmailAsync(string fromOp, string toOp, IEnumerable<string> equipmentSerialNumbers);

        Task NotifyForAcceptanceOfTransferAsync(TransferedItemsModel transferedItemsModel, string actionName);

        Task RemovePendingTransferNotificationAsync(string userId);

        Task RemoveNotificationForUnacceptedTransfersAsync(string userId, int notificationId);
    }
}
