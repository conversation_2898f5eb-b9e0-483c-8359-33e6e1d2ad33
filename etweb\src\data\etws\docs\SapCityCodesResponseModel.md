# SapCityCodesResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**sapCityCode** | **string** |  | [optional] [default to undefined]
**cluster** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { SapCityCodesResponseModel } from './api';

const instance: SapCityCodesResponseModel = {
    sapCityCode,
    cluster,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
