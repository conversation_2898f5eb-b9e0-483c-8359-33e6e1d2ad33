﻿namespace EtDb.Services
{
    using EtDb.ApiClients.EtDb.Context;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using EtDb.Models.Requests;
    using EtDb.Models.Requests.QuantityCalculationObjectModels;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.QuantityCalculationObjectModels;
    using EtDb.Services.Interfaces;

    public class CriticalQuantitiesService : ICriticalQuantitiesService
    {
        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<IQuantityCalculationObjectDataHandler> quantityCalculationObjectDataHandler;
        private readonly Lazy<EtDbContext> etDbContext;

        public CriticalQuantitiesService(Lazy<IMapper> mapper, Lazy<IQuantityCalculationObjectDataHandler> quantityCalculationObjectDataHandler, Lazy<EtDbContext> etDbContext)
        {
            this.mapper = mapper;
            this.quantityCalculationObjectDataHandler = quantityCalculationObjectDataHandler;
            this.etDbContext = etDbContext;
        }

        public async Task<SearchResponseModel<QuantityCalculationObjectResponseModel>> SearchQuantityCalculationObjectsAsync(SearchRequestModel request)
        {
            var data = await this.quantityCalculationObjectDataHandler.Value.GetFilteredQuantityCalculationObjectsAsync(
                request.SortBy, request.SortDir, request.PageNumber, request.PageSize, request.Query);

            return this.mapper.Value.Map<FilteredDataModel<QuantityCalculationObject>, SearchResponseModel<QuantityCalculationObjectResponseModel>>(data);
        }

        public async Task<QuantityCalculationObjectResponseModel> GetQuantityCalculationObjectByIdAsync(int id)
        {
            var quantityCalculationObject = await this.quantityCalculationObjectDataHandler.Value.GetByIdAsync(id);

            var responseModel = this.mapper.Value.Map<QuantityCalculationObjectResponseModel>(quantityCalculationObject);
            return responseModel;
        }

        public async Task UpdateQuantityCalculationObjectAsync(QuantityCalculationObjectRequestModel request)
        {
            var entity = await this.quantityCalculationObjectDataHandler.Value.GetByIdAsync(request.Id);

            if (entity == null)
            {
                throw new ArgumentException($"Quantity calculation object with ID {request.Id} not exist.");
            }

            entity.CriticalQuantity = request.CriticalQuantity;
            entity.IsManuallySet = !request.ToggleToAutomaticGeneration;
            entity.LastUpdateDate = DateTime.Now;

            this.quantityCalculationObjectDataHandler.Value.Update(entity);

            await this.etDbContext.Value.SaveChangesAsync();
        }
    }
}
