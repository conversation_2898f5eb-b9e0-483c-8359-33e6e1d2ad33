﻿namespace ET.DataHandlers.Models
{
    using System;

    using ET.Database.Enums;
    using EtDb.Infrastructure.Enumerations;

    public class TransferListItemWithDate
    {
        public string EquipmentTypeSAPMaterialNumber { get; set; }

        public int EquipmentTypeId { get; set; }

        public int Quantity { get; set; }

        public DateTime DeliveryDate { get; set; }

        public TransferStatus Status { get; set; }

        public SendMethod SendMethod { get; set; }
    }
}
