﻿namespace EtWS.Api.Infrastructure.Mapper
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;

    public class SubstitutionProfile : Profile
    {
        public SubstitutionProfile()
        {

            this.CreateMap<SubstitutionsResponseModelSearchResponseModel, SearchResponseModel<SubstitutionsResponseModel>>()
                .ForMember(r => r.Count, opt => opt.MapFrom(c => c.Count));

            this.CreateMap<SubstitutionDataRequestModel, SubstitutionsRequestModel>();
        }
    }
}
