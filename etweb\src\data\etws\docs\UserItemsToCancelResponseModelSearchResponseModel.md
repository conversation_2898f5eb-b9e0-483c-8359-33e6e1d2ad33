# UserItemsToCancelResponseModelSearchResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**count** | **number** |  | [optional] [default to undefined]
**dataCollection** | [**Array&lt;UserItemsToCancelResponseModel&gt;**](UserItemsToCancelResponseModel.md) |  | [optional] [default to undefined]

## Example

```typescript
import { UserItemsToCancelResponseModelSearchResponseModel } from './api';

const instance: UserItemsToCancelResponseModelSearchResponseModel = {
    count,
    dataCollection,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
