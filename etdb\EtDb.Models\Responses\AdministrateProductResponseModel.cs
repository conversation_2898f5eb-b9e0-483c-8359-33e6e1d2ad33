﻿namespace EtDb.Models.Responses
{
    using EtDb.Infrastructure.Enumerations;

    public class AdministrateProductResponseModel
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public string SAPMaterialNum { get; set; }

        public string EquipmentNameFirst { get; set; }

        public string MatFirst { get; set; }

        public SerialNumberRequiredStatus SerialNumbersRequiredStatus { get; set; }

        public int MinimumQuantity { get; set; }

        public int SapRequestType { get; set; }

        public string BRProjectName { get; set; }

        public string SapElementCode { get; set; }

        public int EquipmentGroupId { get; set; }

        public int UnitOfMeasure { get; set; }

        public int? BoxCapacity { get; set; }
    }
}
