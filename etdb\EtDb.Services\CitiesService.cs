﻿namespace EtDb.Services
{
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using EtDb.Models.Requests;
    using EtDb.Models.Responses;
    using EtDb.Services.Interfaces;

    public class CitiesService : ICitiesService
    {
        private readonly Lazy<ICitiesDataHandler> citiesDataHandler;
        private readonly Lazy<IMapper> mapper;

        public CitiesService(Lazy<ICitiesDataHandler> citiesDataHandler, Lazy<IMapper> mapper)
        {
            this.citiesDataHandler = citiesDataHandler;
            this.mapper = mapper;
        }

        public async Task<CitiesResponseModel> GetCityAsync(int id)
        {
            var city = await this.citiesDataHandler.Value.GetCityByIdAsync(id);

            return this.mapper.Value.Map<CitiesResponseModel>(city);
        }

        public async Task<SearchResponseModel<CitiesResponseModel>> GetCitiesAsync(SearchRequestModel request)
        {
            var filteredCities = await this.citiesDataHandler.Value.GetFilteredCitiesAsync(request.SortBy, request.SortDir, request.PageNumber, request.PageSize, request.Query);

            var citiesResponse = this.mapper.Value.Map<SearchResponseModel<CitiesResponseModel>>(filteredCities);

            return citiesResponse;
        }

        public async Task<int> AddCityAsync(CitiesRequestModel request)
        {
            var cityDto = this.mapper.Value.Map<CitiesDto>(request);

            return await this.citiesDataHandler.Value.AddCityAsync(cityDto);
        }

        public async Task UpdateCityAsync(int id, CitiesRequestModel request)
        {
            var cityDto = this.mapper.Value.Map<CitiesDto>(request);
            cityDto.Id = id;

            await this.citiesDataHandler.Value.UpdateCityAsync(cityDto);
        }

        public async Task BlockCitiesAsync(List<int> cityIds)
        {
            await this.citiesDataHandler.Value.BlockCitiesAsync(cityIds);
        }

        public async Task ActivateCitiesAsync(List<int> cityIds)
        {
            await this.citiesDataHandler.Value.ActivateCitiesAsync(cityIds);
        }

        public async Task<string> GetCityRegionByIdAsync(int cityId)
        {
            return await this.citiesDataHandler.Value.GetCityRegionByIdAsync(cityId);
        }
    }
}
