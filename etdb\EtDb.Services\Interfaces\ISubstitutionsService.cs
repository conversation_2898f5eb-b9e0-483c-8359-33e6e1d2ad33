﻿namespace EtDb.Services.Interfaces
{
    using EtDb.Models.Requests;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.UserModels;

    public interface ISubstitutionsService : IService
    {
        Task<SearchResponseModel<SubstitutionsResponseModel>> GetSubstitutions(SearchRequestModel request, bool isCurrentUserMol = false, FilterSubstitutionsRequestModel model = null);

        Task AddSubstitution(SubstitutionsRequestModel model);

        Task<SubstitutionsResponseModel> GetSubstitutionById(int id);

        Task EditSubstitution(int id, SubstitutionsRequestModel model);

        Task DeleteSubstitution(int id);

        Task<IEnumerable<UserConciseResponseModel>> GetSubstituteUsers(string forUserId);

        Task<SubstitutionsResponseModel> GetActiveSubstitutionsBySubstituteUserId(string substituteUserId);

        Task UpdateForUserIdWhenMolChanges(EditUsersRequestModel model);
    }
}
