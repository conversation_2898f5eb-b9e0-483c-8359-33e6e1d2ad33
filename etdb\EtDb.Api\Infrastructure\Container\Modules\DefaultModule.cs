﻿namespace EtDb.Api.Infrastructure.Container.Modules
{
    using Autofac;
    using EtDb.DataHandlers;
    using EtDb.Services;
    using Net.Autorest.Intercepception;

    public class DefaultModule : Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            //Conventional bindings
            builder.RegisterAssemblyTypes(typeof(IService).Assembly)
                .Where(x => x.IsClass && x.GetInterfaces().Any(y => y == typeof(IService)))
                .AsImplementedInterfaces()
                .InstancePerLifetimeScope();

            builder.RegisterAssemblyTypes(typeof(BaseDataHandler).Assembly)
                .Where(x => x.IsClass && x.BaseType == typeof(BaseDataHandler))
                .AsImplementedInterfaces()
                .InstancePerLifetimeScope();

            builder.RegisterType<RequestIdAndTimeStampInterceptor>();
            builder.RegisterType<EnsureSuccessStatusCodeInterceptor>();
        }
    }
}
