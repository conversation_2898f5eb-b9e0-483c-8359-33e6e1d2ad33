﻿namespace EtDb.ApiClients.EtDb.Models
{
    using System;

    public partial class History
    {
        private const string IntDeliveryType = "INT";

        public static History CreateEntry(Item item, string fromUserId, int deliveryItemQty)
        {
            return new History
            {
                Item = item,
                ItemTypeOfUsage = item.TypeOfUsage,
                ItemValidity = item.EquipmentValidity,
                DeliveryType = IntDeliveryType,
                FromUserId = fromUserId,
                DeliveryItemQty = deliveryItemQty,
                InsertDate = DateTime.Now,
                OperationType = (int)Enums.OperationType.TransferBetweenTechnicians,
                DocStatus = (int)Enums.DocStatus.Reserved,
                AutoGenerated = 0
            };
        }
    }
}
