# UsersWithOPCodeResponse


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **string** |  | [optional] [default to undefined]
**displayName** | **string** |  | [optional] [default to undefined]
**iptuName** | **string** |  | [optional] [default to undefined]
**opcode** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { UsersWithOPCodeResponse } from './api';

const instance: UsersWithOPCodeResponse = {
    id,
    displayName,
    iptuName,
    opcode,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
