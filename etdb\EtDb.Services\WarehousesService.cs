﻿namespace EtDb.Services
{
    using EtDb.DataHandlers.Interfaces;
    using EtDb.Models.Responses;
    using EtDb.Services.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class WarehousesService : IWarehousesService
    {
        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<IWarehouseDataHandler> warehousesDataHandler;

        public WarehousesService(Lazy<IMapper> mapper, Lazy<IWarehouseDataHandler> warehousesDataHandler)
        {
            this.mapper = mapper;
            this.warehousesDataHandler = warehousesDataHandler;
        }

        public async Task<IEnumerable<WarehousesResponseModel>> GetWarehousesSelectListAsync()
        {
            var data = await this.warehousesDataHandler.Value.GetAllWarehouses().ToListAsync();
            return this.mapper.Value.Map<IEnumerable<WarehousesResponseModel>>(data);
        }

        public async Task<IEnumerable<WarehousesResponseModel>> GetWarehousesByRegionAsync(int? region)
        {
            var data = await this.warehousesDataHandler.Value.GetWarehousesByRegionAsync(region);
            return this.mapper.Value.Map<IEnumerable<WarehousesResponseModel>>(data);
        }
    }
}
