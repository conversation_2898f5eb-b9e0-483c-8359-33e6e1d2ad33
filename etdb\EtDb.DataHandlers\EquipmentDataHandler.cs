﻿namespace EtDb.DataHandlers
{
    using EtDb.ApiClients.EtDb.Context;
    using EtDb.ApiClients.EtDb.Enums;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Enums;
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using Microsoft.EntityFrameworkCore;

    public class EquipmentDataHandler : BaseDataHandler, IEquipmentDataHandler
    {
        private const string SourceSystemBatchInt = "INT";
        private Lazy<IUserDataHandler> userDataHandler;
        private Lazy<IHistoryDataHandler> historyDataHandler;

        public EquipmentDataHandler(Lazy<EtDbContext> dbContext, Lazy<IUserDataHandler> userDataHandler, Lazy<IHistoryDataHandler> historyDataHandler)
            : base(dbContext)
        {
            this.userDataHandler = userDataHandler;
            this.historyDataHandler = historyDataHandler;
        }

        public IQueryable<Item> GetAllItems()
        {
            return dbContext.Value.Items;
        }

        public IQueryable<SapmatMapp> GetSAPMatMapp()
        {
            return dbContext.Value.SapmatMapp;
        }

        public async Task<List<SapmatMapp>> GetSAPMatMappAsync()
        {
            return await this.dbContext.Value.SapmatMapp.ToListAsync();
        }

        public IQueryable<AvailableEquipment> GetAllAvailableItems()
        {
            return this.dbContext.Value.AvailableEquipments;
        }

        public IQueryable<AvailableEquipment> GetNonZeroQuantityAvailableItems()
        {
            return this.GetAllAvailableItems()
                .Include(i => i.User)
                .Include(i => i.Item)
                .Where(i => i.ItemQuantity > 0);
        }

        public IQueryable<AvailableEquipment> GetUserAllAvailableItems(string userId)
        {
            return this.GetNonZeroQuantityAvailableItems()
                .Where(i => i.UserId == userId);
        }

        public async Task<AvailableEquipment?> GetUserAvailableItemAsync(string userId, int itemId)
        {
            return await this.GetAllAvailableItems()
                .Where(i => i.User.Id == userId)
                .SingleOrDefaultAsync(i => i.ItemId == itemId);
        }

        public async Task UpdateFromUserAvailableItemAsync(string fromUserId, int itemId, int deliveryItemQty)
        {
            var fromUserAvailableItem = await this.GetUserAvailableItemAsync(fromUserId, itemId);

            if (fromUserAvailableItem == null)
            {
                return;
            }

            if (fromUserAvailableItem.ItemQuantity != 1)
            {
                throw new Exception("The quantity to decrease is not equal to 1");
            }

            fromUserAvailableItem.ItemQuantity -= deliveryItemQty;

            this.dbContext.Value.AvailableEquipments.Update(fromUserAvailableItem);
        }

        public async Task UpdateToUserAvailableItemAsync(string toUserId, int itemId, int deliveryItemQty)
        {
            var toUserAvailableItem = await this.GetUserAvailableItemAsync(toUserId, itemId);

            if (toUserAvailableItem == null)
            {
                toUserAvailableItem = new AvailableEquipment()
                {
                    ItemId = itemId,
                    UserId = toUserId,
                    ItemQuantity = deliveryItemQty,
                };
                await this.dbContext.Value.AvailableEquipments.AddAsync(toUserAvailableItem);
            }
            else if (toUserAvailableItem.ItemQuantity != 0)
            {
                throw new Exception("The quantity to increase is not equal to 0");
            }
            else
            {
                toUserAvailableItem.ItemQuantity += deliveryItemQty;
                this.dbContext.Value.AvailableEquipments.Update(toUserAvailableItem);
            }
        }

        public async Task<IQueryable<AvailableEquipment>> GetAllUserAvailableItemsWithReservedForTransferAsync(string userId)
        {
            return await Task.FromResult(this.GetUserAllAvailableItems(userId)
                .Include(i => i.Item.Histories)
                .Include(i => i.Item.EquipmentType)
                .Where(i => !i.Item.Histories.Any(ih => ih.FromUserId == userId && ih.DocStatus == (int)DocStatus.WaitingForConfirmation)));
        }

        public async Task<IQueryable<AvailableEquipment>> GetAllUserReservedItemsForTransferAsync(string userId)
        {
            return await Task.FromResult(this.GetUserAllAvailableItems(userId)
                .Include(x => x.Item.Histories)
                .Where(i => i.Item.Histories
                    .Any(ih => ih.FromUserId == userId && ih.DocStatus == (int)DocStatus.Reserved)));
        }

        public async Task<FilteredDataModel<AvailableEquipment>> GetFilteredUserAvailableItemsAsync(string userId, string sortBy, string sortDir, int pageNumber, int pageSize, string query)
        {
            IQueryable<AvailableEquipment> userAvailableItems = await this.GetAllUserAvailableItemsWithReservedForTransferAsync(userId);

            var filteredUserAvailableItems = await this.GetFilteredData(userAvailableItems, sortBy, sortDir, pageNumber, pageSize, query);

            return filteredUserAvailableItems;
        }

        public async Task<FilteredDataModel<AvailableEquipment>> GetFilteredUserDailyItemsAsync(string userId, string sortBy, string sortDir, int pageNumber, int pageSize, string query)
        {
            IQueryable<AvailableEquipment> userAvailableItems = this.GetUserAllAvailableItems(userId).Where(x => x.IsDaily);

            var filteredUserAvailableItems = await this.GetFilteredData(userAvailableItems, sortBy, sortDir, pageNumber, pageSize, query);

            return filteredUserAvailableItems;
        }

        public async Task<FilteredDataModel<AvailableEquipment>> GetFilteredUserReservedItemsForTransferAsync(string userId, string sortBy, string sortDir, int pageNumber, int pageSize, string query)
        {
            IQueryable<AvailableEquipment> userReservedItems = await this.GetAllUserReservedItemsForTransferAsync(userId);

            var fiilteredUserReservedItemsForTransfer = await this.GetFilteredData(userReservedItems, sortBy, sortDir, pageNumber, pageSize, query);

            return fiilteredUserReservedItemsForTransfer;
        }

        public async Task<History> ReserveItemAsync(int itemId, string userId, int quantity)
        {
            // IsolationLevel.ReadCommitted
            await using var transaction = await this.dbContext.Value.Database.BeginTransactionAsync();
            AvailableEquipment? userAvailableItem = this.GetUserAllAvailableItems(userId).FirstOrDefault(i => i.Item.Id == itemId);

            History reservationEntry = await this.CreateReservationEntryAsync(userAvailableItem.Item, userId, quantity);

            await this.dbContext.Value.SaveChangesAsync();

            if (this.CheckIfItemIsAddedForTransferTwice(userAvailableItem.ItemId, userId))
            {
                transaction.Rollback();
                throw new ArgumentException("Item has already been added for transfer!");
            }

            transaction.Commit();
            return reservationEntry;
        }

        public async Task<History> ReserveItemAsync(string itemSerialNumber, string userId, int quantity)
        {
            // IsolationLevel.ReadCommitted
            await using var transaction = await this.dbContext.Value.Database.BeginTransactionAsync();
            var userAvailableItem = this.GetUserAllAvailableItems(userId).FirstOrDefault(i => i.Item.EquipmentSerialNum.ToLower() == itemSerialNumber.ToLower());
            if (userAvailableItem == null)
            {
                throw new ArgumentException("Incorrect serial number entered!");
            }

            History reservationEntry = await this.CreateReservationEntryAsync(userAvailableItem.Item, userId,userAvailableItem.ItemQuantity);

            this.dbContext.Value.SaveChanges();

            if (this.CheckIfItemIsAddedForTransferTwice(userAvailableItem.ItemId, userId))
            {
                transaction.Rollback();
                throw new ArgumentException("Item has already been added for transfer!");
            }

            transaction.Commit();
            return reservationEntry;
        }

        public async Task ReserveAllItems(string userId)
        {
            IQueryable<AvailableEquipment> userAvailableItems = await this.GetAllUserAvailableItemsWithReservedForTransferAsync(userId);
            IQueryable<AvailableEquipment> userAvailableForTransferItems = userAvailableItems.Where(i =>
                i.Item.Histories.All(ih => ih.DocStatus != (int)DocStatus.Reserved));
            if (!userAvailableForTransferItems.Any())
            {
                throw new ArgumentException("All items have already been added!");
            }

            // TODO: do we need autodetect changes to be deactivated ???
            this.dbContext.Value.ChangeTracker.AutoDetectChangesEnabled = false;
            foreach (var item in userAvailableForTransferItems.ToList())
            {
                await this.CreateReservationEntryAsync(item.Item, userId, item.ItemQuantity);
            }

            this.dbContext.Value.ChangeTracker.AutoDetectChangesEnabled = true;
            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task<string> RemoveItemAsync(int itemId, string userId)
        {
            var reservationEntry = await this.historyDataHandler.Value.GetHistoryFromUserByItemId(userId, itemId).Include(x => x.Item).ThenInclude(x => x.EquipmentType).FirstOrDefaultAsync(h => h.DocStatus == (int)DocStatus.Reserved);
            if (reservationEntry == null)
            {
                throw new ArgumentNullException(string.Format("Reservation entry for this item is not available!"));
            }

            string itemName = reservationEntry.Item.EquipmentType.Name;
            await this.historyDataHandler.Value.RemoveHistoryEntryAsync(reservationEntry);

            return itemName;
        }

        public async Task RemoveItemsAsync(IEnumerable<int> selectedItems, string userId)
        {
            foreach (var itemId in selectedItems)
            {
                await this.RemoveItemAsync(itemId, userId);
            }
        }

        public async Task RemoveAllItemsAsync(string userId)
        {
            IQueryable<History> reservationEntries = this.historyDataHandler.Value.GetHistoryResrvedItemsByFromUserId(userId);
            if (!reservationEntries.Any())
            {
                throw new ArgumentException("All items have already been removed!");
            }

            await this.historyDataHandler.Value.RemoveHistoryEntriesAsync(reservationEntries);
        }

        public async Task DeliverItemsAsync(IEnumerable<int> selectedItems, string fromUserId, string toUserId, bool isSpecialUser, int? postOfficeId, string waybillNum, DateTime? waybillDate)
        {
            IList<History> reservationEntries =
                this.historyDataHandler.Value.GetHistoryResrvedItemsByFromUserId(fromUserId).Where(h => selectedItems.Contains(h.ItemId)).ToList();
            string currentDeliveryNum = await this.GetIntDeliveryNumberAsync();

            this.dbContext.Value.ChangeTracker.AutoDetectChangesEnabled = false;
            foreach (var historyEntry in reservationEntries)
            {
                if (isSpecialUser)
                {
                    this.historyDataHandler.Value.UpdateHistoryDelieverItems(historyEntry, DocStatus.Approved, toUserId, currentDeliveryNum, postOfficeId, waybillNum, waybillDate);
                    await this.historyDataHandler.Value.CreateStatusHistoryEntryAsync(historyEntry.Id, fromUserId, DocStatus.Reserved, DocStatus.Approved, SourceSystemBatchInt);
                    await this.UpdateFromUserAvailableItemAsync(historyEntry.FromUserId, historyEntry.ItemId, historyEntry.DeliveryItemQty);
                    await this.UpdateToUserAvailableItemAsync(historyEntry.ToUserId, historyEntry.ItemId, historyEntry.DeliveryItemQty);
                }
                else
                {
                    this.historyDataHandler.Value.UpdateHistoryDelieverItems(historyEntry, DocStatus.WaitingForConfirmation, toUserId, currentDeliveryNum);
                    await this.historyDataHandler.Value.CreateStatusHistoryEntryAsync(historyEntry.Id, fromUserId, DocStatus.Reserved, DocStatus.WaitingForConfirmation, SourceSystemBatchInt);
                }
            }

            await this.userDataHandler.Value.UpdateRecipientHistoryAsync(fromUserId, toUserId);

            this.dbContext.Value.ChangeTracker.AutoDetectChangesEnabled = true;
            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task<FilteredDataModel<History>> GetFilteredUserItemsToAcceptAsync(string userId, string sortBy, string sortDir, int pageNumber, int pageSize, string query)
        {
            IQueryable<History> userItemsToAccept = this.historyDataHandler.Value.GetHistoryItemsToAccept(userId);

            var filteredUserItemsToAccept = await this.GetFilteredData(userItemsToAccept, sortBy, sortDir, pageNumber, pageSize, query);

            return filteredUserItemsToAccept;
        }

        public async Task<FilteredDataModel<History>> GetFilteredUserTransferedItemsAsync(string userId, string sortBy, string sortDir, int pageNumber, int pageSize, string query)
        {
            IQueryable<History> userItemsToAccept = this.historyDataHandler.Value.GetHistoryTransferedItems(userId);

            var filteredUserTransferedItems = await this.GetFilteredData(userItemsToAccept, sortBy, sortDir, pageNumber, pageSize, query);

            return filteredUserTransferedItems;
        }

        public async Task<IDictionary<string, TransferedItemsModel>> AcceptItemsAsync(IEnumerable<int> selectedItems, RefuseReasonsList? refuseReason, string userId)
        {
            var itemsHandler = this.AcceptItemsHandlerAsync;

            IDictionary<string, TransferedItemsModel> acceptItems = await this.UpdateTransferedItemsAsync(selectedItems, refuseReason, userId, itemsHandler);

            return acceptItems;
        }

        public async Task<IDictionary<string, TransferedItemsModel>> RefuseItemsAsync(IEnumerable<int> selectedItems, RefuseReasonsList? refuseReason, string userId)
        {
            var itemsHandler = this.RefuseItemsHandlerAsync;

            IDictionary<string, TransferedItemsModel> refuseItems = await this.UpdateTransferedItemsAsync(selectedItems, refuseReason, userId, itemsHandler);

            return refuseItems;
        }

        public async Task<IDictionary<string, TransferedItemsModel>> CancelItemsAsync(IEnumerable<int> selectedItems, RefuseReasonsList? refuseReason, string userId)
        {
            var itemsHandler = this.CancelItemsHandlerAsync;

            IDictionary<string, TransferedItemsModel> cancelItems = await this.UpdateTransferedItemsAsync(selectedItems, refuseReason, userId, itemsHandler);

            return cancelItems;
        }

        public async Task<string> GetIntDeliveryNumberAsync()
        {
            IntDeliveryNumber newUniqueRecord = new IntDeliveryNumber
            {
                Guid = Guid.NewGuid()
            };
            await this.dbContext.Value.IntDeliveryNumbers.AddAsync(newUniqueRecord);
            await this.dbContext.Value.SaveChangesAsync();

            long deliveryNumber = newUniqueRecord.UniqueId;
            this.dbContext.Value.IntDeliveryNumbers.Remove(newUniqueRecord);
            await this.dbContext.Value.SaveChangesAsync();

            return string.Format("{0:0000000000}", deliveryNumber);
        }

        public IQueryable<PostOffice> GetAllActivePostOffices()
        {
            IQueryable<PostOffice> allActivePostOffices = this.dbContext.Value.PostOffices.Where(p => p.DeleteFl == 0);

            return allActivePostOffices;
        }

        public async Task<SnapshotDates> GetLatestSnapshotDateAsync() => await this.dbContext.Value.SnapshotDates.OrderByDescending(s => s.Id).FirstAsync();

        public async Task<IEnumerable<AvailableEquipmentByOpsnapshots>> GetAvailableEquipmentAsync(int snapshotDateId, int? opId)
        {
            IQueryable<AvailableEquipmentByOpsnapshots> availableEquipment = this.dbContext.Value.AvailableEquipmentByOpsnapshots
                .Include(a => a.EquipmentType.EquipmentGroup)
                .Include(a => a.Op)
                .Where(a => a.SnapshotDateId == snapshotDateId)
                .Where(a => a.EquipmentType.SerialNumberRequired == 1);

            if (opId.HasValue)
            {
                availableEquipment = availableEquipment.Where(a => a.Opid == opId.Value);
            }

            availableEquipment = availableEquipment.OrderBy(a => a.Op.Opcode);

            return await availableEquipment.ToListAsync();
        }

        public async Task<IDictionary<int, int>> GetAllAvailableValidItemsGroupedByEquipmentTypeAsync()
        {
            Dictionary<int, int> data = await this.dbContext.Value.AvailableEquipments
                .Include(a => a.Item.Histories)
                .Include(a => a.Item.EquipmentType)
                .Include(a => a.User.Schenker)
                .Where(a => a.ItemQuantity == 1)
                .Where(a => a.User.Schenker.IncludedInMonthlyCalculations ?? false)
                .Where(a => a.User.Eln != "99999")
                .GroupBy(a => a.Item.EquipmentTypeId)
                .Select(g => new
                {
                    EquipmentTypeId = g.Key,
                    ValidItemsCount = g.Select(a => a.Item.Histories.OrderByDescending(h => h.Id).FirstOrDefault())
                        .Count(h => h.ItemValidity == "Y")
                })
                .ToDictionaryAsync(x => x.EquipmentTypeId, x => x.ValidItemsCount);

            List<int> equipmentTypeIds = await this.dbContext.Value.EquipmentTypes
                .Where(e => e.SerialNumberRequired == 1)
                .Select(e => e.Id)
                .ToListAsync();

            foreach (int equipmentTypeId in equipmentTypeIds)
            {
                if (!data.ContainsKey(equipmentTypeId))
                {
                    data.Add(equipmentTypeId, 0);
                }
            }

            return data;
        }

        public async Task UpdateAvailableEquipmentsAsync(IList<AvailableEquipment> availableEquipments)
        {
            foreach (AvailableEquipment availableEquipment in availableEquipments)
            {
                this.dbContext.Value.AvailableEquipments.Update(availableEquipment);
            }

            await this.dbContext.Value.SaveChangesAsync();
        }

        private async Task<History> CreateReservationEntryAsync(Item userItem, string userId, int quantity)
        {
            if (userItem == null)
            {
                throw new ArgumentException("The item is not available!");
            }

            if (this.CheckIfItemIsAddedForTransfer(userItem, userId))
            {
                throw new ArgumentException("The item has already been added for transfer!");
            }

            if (this.CheckIfItemIsWaitingForConfirmation(userItem, userId))
            {
                throw new ArgumentException("The item has been submitted and is awaiting confirmation!");
            }

            var reservationEntry = History.CreateEntry(userItem, userId, quantity);
            await this.dbContext.Value.Histories.AddAsync(reservationEntry);

            return reservationEntry;
        }

        private bool CheckIfItemIsAddedForTransfer(Item item, string userId)
        {
            bool isItemAddedForTransfer = item.Histories
                                            .Any(ih => ih.FromUserId == userId && ih.DocStatus == (int)DocStatus.Reserved);

            return isItemAddedForTransfer;
        }

        private bool CheckIfItemIsAddedForTransferTwice(int itemId, string userId)
        {
            var historyEntriesForCurrentItem = this.historyDataHandler.Value.GetHistoryResrvedItemsByFromUserId(userId).Where(h => h.ItemId == itemId);
            if (historyEntriesForCurrentItem.Count() > 1)
            {
                return true;
            }

            return false;
        }

        private bool CheckIfItemIsWaitingForConfirmation(Item item, string userId)
        {
            bool isItemWaitingForConfirmation = item.Histories
                .Any(ih => ih.FromUserId == userId && ih.DocStatus == (int)DocStatus.WaitingForConfirmation);

            return isItemWaitingForConfirmation;
        }

        private async Task AcceptItemsHandlerAsync(History historyEntry, RefuseReasonsList? refuseReason, string userId)
        {
            this.historyDataHandler.Value.UpdateHistoryAcceptItems(historyEntry, DocStatus.Approved, refuseReason);
            this.historyDataHandler.Value.UpdateAdditionDate(historyEntry);
            await this.UpdateFromUserAvailableItemAsync(historyEntry.FromUserId, historyEntry.ItemId, historyEntry.DeliveryItemQty);
            await this.UpdateToUserAvailableItemAsync(historyEntry.ToUserId, historyEntry.ItemId, historyEntry.DeliveryItemQty);
            await this.historyDataHandler.Value.CreateStatusHistoryEntryAsync(historyEntry.Id, userId, DocStatus.WaitingForConfirmation, DocStatus.Approved, SourceSystemBatchInt);
        }

        private async Task RefuseItemsHandlerAsync(History historyEntry, RefuseReasonsList? refuseReason, string userId)
        {
            this.historyDataHandler.Value.UpdateHistoryAcceptItems(historyEntry, DocStatus.Refused, refuseReason);
            await this.historyDataHandler.Value.CreateStatusHistoryEntryAsync(historyEntry.Id, userId, DocStatus.WaitingForConfirmation, DocStatus.Refused, SourceSystemBatchInt);
        }

        private async Task CancelItemsHandlerAsync(History historyEntry, RefuseReasonsList? refuseReason, string userId)
        {
            this.historyDataHandler.Value.UpdateHistoryAcceptItems(historyEntry, DocStatus.Cancelled, refuseReason);
            await this.historyDataHandler.Value.CreateStatusHistoryEntryAsync(historyEntry.Id, userId, DocStatus.WaitingForConfirmation, DocStatus.Cancelled, SourceSystemBatchInt);
        }

        private async Task<IDictionary<string, TransferedItemsModel>> UpdateTransferedItemsAsync(
            IEnumerable<int> selectedItems,
            RefuseReasonsList? refuseReason,
            string userId,
            Func<History, RefuseReasonsList?, string, Task> itemsHandler)
        {
            try
            {
                IDictionary<string, TransferedItemsModel> usersPendingTransferedItemsTo = new Dictionary<string, TransferedItemsModel>();

                List<History> selectedHistoryEntries = await this.historyDataHandler.Value.GetAll()
                    .Include(h => h.Item.EquipmentType)
                    .Include(h => h.ToUser)
                    .Where(h => selectedItems.Contains(h.Id))
                    .ToListAsync();

                this.dbContext.Value.ChangeTracker.AutoDetectChangesEnabled = false;

                const int NextItemsCount = 200;
                List<History> items = selectedHistoryEntries.Take(NextItemsCount).ToList();
                int numberOfItemsToSkip = items.Count;

                while (items.Count > 0)
                {
                    foreach (var historyEntry in items)
                    {
                        string toUserId = historyEntry.ToUserId;
                        string fromUserId = historyEntry.FromUserId;
                        string itemSerialNumber = historyEntry.Item.EquipmentSerialNum;

                        if (historyEntry.DocStatus == (int)DocStatus.WaitingForConfirmation)
                        {
                            await itemsHandler(historyEntry, refuseReason, userId);
                        }
                        else
                        {
                            throw new ArgumentException($"{historyEntry.Item.EquipmentType.Name} with CH:{itemSerialNumber} has already been transferred or declined!");
                        }

                        if (!usersPendingTransferedItemsTo.ContainsKey(toUserId))
                        {
                            TransferedItemsModel model = new TransferedItemsModel(historyEntry.ToUser, fromUserId, itemSerialNumber);
                            usersPendingTransferedItemsTo.Add(toUserId, model);
                        }
                        else
                        {
                            usersPendingTransferedItemsTo[toUserId].SerialNumbers.Add(itemSerialNumber);
                        }
                    }

                    await this.dbContext.Value.SaveChangesAsync();

                    items = selectedHistoryEntries
                        .Skip(numberOfItemsToSkip)
                        .Take(NextItemsCount)
                        .ToList();

                    numberOfItemsToSkip += items.Count;
                }

                var pendingTransferedItems = await this.historyDataHandler.Value.GetUsersPendingTransferedItemsAsync(usersPendingTransferedItemsTo);

                return pendingTransferedItems;
            }
            finally
            {
                this.dbContext.Value.ChangeTracker.AutoDetectChangesEnabled = true;
            }
        }
    }
}
