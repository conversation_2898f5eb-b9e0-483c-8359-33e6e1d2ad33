﻿using System.Linq;
using AutoMapper;
using EtDb.ApiClients.EtDb.Context;
using EtDb.ApiClients.EtDb.Models;
using EtDb.DataHandlers.Interfaces;
using EtDb.DataHandlers.Models;
using Microsoft.EntityFrameworkCore;

namespace EtDb.DataHandlers
{
    public class SubstitutionsDataHandler : BaseDataHandler, ISubstitutionsDataHandler
    {
        private readonly Lazy<IMapper> mapper;

        public SubstitutionsDataHandler(Lazy<EtDbContext> dbContext, Lazy<IMapper> mapper)
            : base(dbContext)
        {
            this.mapper = mapper;
        }

        public async Task<IQueryable<Substitutions>> GetFilteredSubstitutions(string opCode, DateTime fromDate, DateTime toDate)
        {
            var substitutions = this.dbContext.Value.Substitutions.AsQueryable();

            if (opCode != null)
            {
                substitutions = substitutions.Where(s => s.Opcode == opCode);
            }

            if (fromDate != default(DateTime))
            {
                substitutions = substitutions.Where(s => s.FromDate >= fromDate);
            }

            if (toDate != default(DateTime))
            {
                substitutions = substitutions.Where(s => s.ToDate <= toDate);
            }

            return substitutions;
        }

        public async Task<IQueryable<Substitutions>> GetSubstitutions()
            => this.dbContext.Value.Substitutions.Include(u => u.ForUser).Include(u => u.SubstituteUser);

        public async Task<Substitutions?> GetSubstitutionById(int id)
            => await this.dbContext.Value.Substitutions.Include(u => u.ForUser).Include(u => u.SubstituteUser).FirstOrDefaultAsync(s => s.Id == id);

        public async Task<FilteredDataModel<Substitutions>> GetFilteredSubstitutions(string sortBy, string sortDir, int pageNumber, int pageSize, string query, IQueryable<Substitutions> substitutions)
            => await this.GetFilteredData(
                substitutions,
                sortBy,
                sortDir,
                pageNumber,
                pageSize,
                query);

        public async Task AddSubstitution(Substitutions substitution)
        {
            if (substitution == null)
            {
                throw new ArgumentNullException(nameof(substitution));
            }
            await this.dbContext.Value.Substitutions.AddAsync(substitution);
            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task EditSubstitution(Substitutions substitution)
        {
            if (substitution == null)
            {
                throw new ArgumentNullException(nameof(substitution));
            }
            this.dbContext.Value.Substitutions.Update(substitution);
            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task DeleteSubstitution(int id)
        {
            var substitution = await this.GetSubstitutionById(id);
            if (substitution != null)
            {
                this.dbContext.Value.Substitutions.Remove(substitution);
                await this.dbContext.Value.SaveChangesAsync();
            }
        }

        public async Task ActivateSubstitution(DateTime activationDate)
        {
            var substitutions = await this.dbContext.Value.Substitutions
                .Where(s => s.IsActive == false && s.FromDate <= activationDate)
                .ToListAsync();

            foreach (var sub in substitutions)
            {
                sub.IsActive = true;
            }

            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task DeactivateSubstitution(DateTime deactivationDate)
        {
            var substitutions = await this.dbContext.Value.Substitutions
                .Where(s => s.IsActive && s.ToDate < deactivationDate)
                .ToListAsync();

            foreach (var sub in substitutions)
            {
                sub.IsActive = false;
            }

            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task<IQueryable<Substitutions>> UpdateForUserIdWhenMolChanges(string OP, string id)
        {
            var substitutions = this.dbContext.Value.Substitutions
                .Where(s => s.Opcode == OP)
                .Where(s => s.IsActive || s.FromDate > DateTime.Now)
                .AsQueryable();

            foreach (var substitution in substitutions)
            {
                substitution.ForUserId = id;
            }

            await this.dbContext.Value.SaveChangesAsync();

            return substitutions;
        }

        public async Task<Substitutions?> GetActiveSubstitutionBySubstituteUserAd(string username)
        {
            var activeSubstitutions = await this.dbContext.Value.Substitutions
                .Include(u => u.ForUser)
                .Include(u => u.SubstituteUser)
                .SingleOrDefaultAsync(s => s.IsActive && s.ForUser.Adaccount == username);

            return activeSubstitutions ?? throw new InvalidOperationException($"No active substitution found for user: {username}");
        }
    }
}
