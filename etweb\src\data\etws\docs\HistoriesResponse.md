# HistoriesResponse


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**insertDate** | **string** |  | [optional] [default to undefined]
**itemId** | **number** |  | [optional] [default to undefined]

## Example

```typescript
import { HistoriesResponse } from './api';

const instance: HistoriesResponse = {
    id,
    insertDate,
    itemId,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
