﻿namespace EtDb.Api.Infrastructure.Mapper
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Models;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.QuantityCalculationObjectModels;

    public class QuantityCalculationObjectProfile : Profile
    {
        public QuantityCalculationObjectProfile()
        {
            this.CreateMap<QuantityCalculationObject, QuantityCalculationObjectResponseModel>()
                .ForMember(dest => dest.OPCode, opt => opt.MapFrom(src => src.Op.Opcode));

            this.CreateMap<FilteredDataModel<QuantityCalculationObject>, SearchResponseModel<QuantityCalculationObjectResponseModel>>()
                .ForMember(m => m.DataCollection, opts => opts.MapFrom(x => x.DataRows.ToList()))
                .ForMember(m => m.Count, opts => opts.MapFrom(x => x.AllDataRowsCount));
        }
    }
}
