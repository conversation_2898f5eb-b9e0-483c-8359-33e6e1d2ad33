import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useGetTechnicianName } from "../../data/query";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Check, ChevronsUpDown, Loader2, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";

const Technician = () => {
  const { t } = useTranslation();
  const [query, setQuery] = useState("");
  const [selectedTechnician, setSelectedTechnician] = useState("");
  const [open, setOpen] = useState(false);
  const {
    register,
    setValue,
    formState: { errors },
  } = useFormContext();
  const { data: technicianOptions, isLoading } = useGetTechnicianName(query);

  const filteredTechnicians = technicianOptions || [];
  const hasError = !!errors.nameOfTechnician;
  const errorMessage = errors.nameOfTechnician?.message?.toString();

  function handleSelect(value: string) {
    setSelectedTechnician(value);
    setValue("nameOfTechnician", value, { shouldValidate: true });
    setOpen(false);
  }

  const selectedTechnicianData = filteredTechnicians.find(
    (tech) => tech.displayName === selectedTechnician
  );

  return (
    <div className="space-y-2">
      <Label htmlFor="nameOfTechnician">{t("enterFirstAndLastName")}</Label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between",
              hasError && "border-destructive",
              !selectedTechnician && "text-muted-foreground"
            )}
          >
            {selectedTechnicianData
              ? `${selectedTechnicianData.displayName} - ${selectedTechnicianData.iptuName}`
              : t("searchTechnicians")}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0">
          <Command>
            <CommandInput
              placeholder={t("searchTechnicians")}
              value={query}
              onValueChange={setQuery}
            />
            <CommandList>
              {isLoading && (
                <div className="flex items-center justify-center py-6">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span className="text-sm text-muted-foreground">
                    {t("loading")}
                  </span>
                </div>
              )}
              {!isLoading && filteredTechnicians.length === 0 && (
                <CommandEmpty>{t("noTechniciansFound")}</CommandEmpty>
              )}
              {!isLoading && filteredTechnicians.length > 0 && (
                <CommandGroup>
                  {filteredTechnicians.map((tech, idx) => (
                    <CommandItem
                      key={idx}
                      value={tech.displayName || ""}
                      onSelect={() => handleSelect(tech.displayName || "")}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          selectedTechnician === tech.displayName
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                      {tech.displayName} - {tech.iptuName} - {tech.eln}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      <input
        type="hidden"
        {...register("nameOfTechnician")}
        value={selectedTechnician}
      />

      {hasError && (
        <div className="flex items-center text-sm text-destructive">
          <AlertCircle className="h-4 w-4 mr-1.5" />
          {t(errorMessage || "")}
        </div>
      )}
    </div>
  );
};

export default Technician;
