﻿using System;

namespace EtDb.ApiClients.EtDb.Models
{
    public partial class StatusHistory
    {
        public int Id { get; set; }
        public int HistoryId { get; set; }
        public int DocStatusOld { get; set; }
        public int DocStatusNew { get; set; }
        public string SourceSystem { get; set; }
        public DateTime? DateInsert { get; set; } = DateTime.UtcNow;
        public string UserId { get; set; }
        public virtual History History { get; set; }
        public virtual User User { get; set; }
    }
}
