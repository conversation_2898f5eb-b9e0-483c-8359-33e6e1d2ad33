import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useGetOpQuery } from "../../data/query";
import { useEffect } from "react";
import { Label } from "@/components/ui/label";
import { XCircle } from "lucide-react";

const Op = () => {
  const { data: opOptions, isLoading, error } = useGetOpQuery();
  const { t } = useTranslation();
  const {
    register,
    watch,
    setValue,
    formState: { errors },
  } = useFormContext();

  const hasError = !!errors.opId;
  const errorMessage = errors.opId?.message?.toString();

  const userId = watch("userId");
  const selectedOption = opOptions?.find((option) => option.id === userId);

  useEffect(() => {
    if (selectedOption?.opcode) {
      setValue("deliveryShop", selectedOption.opcode);
    }
  }, [selectedOption, setValue]);

  return (
    <div className="space-y-2">
      <Label htmlFor="opId">{t("selectATechnicianFromTheList")}</Label>
      <div>
        {isLoading ? (
          <div className="flex items-center space-x-2 py-3">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent"></div>
            <span className="text-sm text-gray-500">{t("loading")}</span>
          </div>
        ) : error ? (
          <div className="flex items-center rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-sm text-destructive">
            <XCircle className="mr-2 h-5 w-5" />
            <span>{error.message}</span>
          </div>
        ) : (
          <select
            id="userId"
            {...register("userId")}
            className="w-full rounded-lg border px-4 py-3 text-sm outline-none transition-all focus:ring-2"
          >
            <option value="">{t("selectOperation")}</option>
            {opOptions &&
              Object.entries(
                opOptions.reduce((groups, option) => {
                  const key = option.iptuName || "Other";
                  if (!groups[key]) {
                    groups[key] = [];
                  }
                  groups[key].push(option);
                  return groups;
                }, {} as Record<string, typeof opOptions>)
              ).map(([iptuName, options]) => (
                <optgroup key={iptuName} label={iptuName}>
                  {options.map((option) => (
                    <option key={option.id} value={option.id || ""}>
                      {option.displayName} - {option.opcode}
                    </option>
                  ))}
                </optgroup>
              ))}
          </select>
        )}
        {hasError && !isLoading && !error && (
          <div className="mt-1.5 flex items-center text-sm text-destructive">
            <XCircle className="mr-1.5 h-4 w-4" />
            <p>{t(errorMessage || "")}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Op;
