import React from "react";
import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { Label } from "@/components/ui/label";
import { XCircle } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface TransferToInputProps {
  label: string;
  name: string;
}

const TransferToInput: React.FC<TransferToInputProps> = ({ label, name }) => {
  const { t } = useTranslation();

  const {
    register,
    formState: { errors },
    setValue,
    watch,
  } = useFormContext();
  const transferOptions = [
    { value: "technician", label: t("technician") },
    { value: "client", label: t("client") },
    { value: "centralWarehouse", label: t("centralWarehouse") },
    { value: "SAPVirtualWarehouse", label: t("SAPVirtualWarehouse") },
    { value: "op", label: t("op") },
    { value: "remove", label: t("removeTransfer") },
  ];

  const hasError = !!errors[name];
  const errorMessage = errors[name]?.message?.toString();
  const currentValue = watch(name);

  const handleValueChange = (value: string) => {
    setValue(name, value, { shouldValidate: true });
  };

  return (
    <div className="space-y-2">
      <Label htmlFor={name}>{label}</Label>
      <Select value={currentValue || undefined} onValueChange={handleValueChange}>
        <SelectTrigger
          className={`w-full ${hasError ? "border-destructive focus-visible:ring-destructive/20" : ""}`}
          aria-invalid={hasError}
        >
          <SelectValue placeholder={`-- ${t("selectAnOption")} --`} />
        </SelectTrigger>
        <SelectContent>
          {transferOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      <input
        type="hidden"
        {...register(name)}
        value={currentValue || ""}
      />
      
      {hasError && (
        <div className="mt-1.5 flex items-center text-sm text-destructive">
          <XCircle className="mr-1.5 h-4 w-4" />
          {t(errorMessage || "")}
        </div>
      )}
    </div>
  );
};

export default TransferToInput;
