﻿namespace EtDb.Api.Infrastructure.Mapper
{
    using EtDb.ApiClients.EtDb.Enums;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Models;
    using EtDb.Models.Requests.EquipmentModels;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.EquipmentModels;
    using EtDb.Models.Responses.UserModels;

    public class EquipmentProfile : Profile
    {
        public EquipmentProfile()
        {
            this.CreateMap<AvailableEquipment, AvailableEquipmentResponseModel>()
                .ForMember(m => m.IsReserved, opts => opts.MapFrom((src, _, _, resolutionContext) =>
                {
                    var userAdAcc = resolutionContext.Items[AutomapperConstants.UserAdAccount]?.ToString().ToLower();
                    return src.Item.Histories
                        .Any(h => h.DocStatus == (int)DocStatus.Reserved
                            && h.FromUser.Adaccount.ToLower() == userAdAcc);
                }))
                .ForMember(m => m.IsWaitingForConfirmation, opts => opts.MapFrom((src, _, _, resolutionContext) =>
                {
                    var userAdAcc = resolutionContext.Items[AutomapperConstants.UserAdAccount]?.ToString().ToLower();
                    return src.Item.Histories
                        .Any(h => h.DocStatus == (int)DocStatus.WaitingForConfirmation &&
                            h.FromUser.Adaccount.ToLower() == userAdAcc);
                }))
                .ForPath(m => m.Item.Id, opts => opts.MapFrom(x => x.Item.Id))
                .ForPath(m => m.Item.IcmIdSgwId, opts => opts.MapFrom(x => x.Item.IcmIdSgwId))
                .ForPath(m => m.Item.EquipmentName, opts => opts.MapFrom(x => x.Item.EquipmentName))
                .ForPath(m => m.Item.EquipmentSerialNum, opts => opts.MapFrom(x => x.Item.EquipmentSerialNum.Trim()))
                .ForPath(m => m.Item.SapserialNum, opts => opts.MapFrom(x => x.Item.SapserialNum))
                .ForPath(m => m.Item.SapmaterialNum, opts => opts.MapFrom(x => x.Item.SapmaterialNum))
                .ForPath(m => m.Item.EquipmentTypeId, opts => opts.MapFrom(x => x.Item.EquipmentTypeId))
                .ForPath(m => m.Item.EquipmentMaterialGroup, opts => opts.MapFrom(x => x.Item.EquipmentMaterialGroup))
                .ForPath(m => m.Item.TypeOfUsage, opts => opts.MapFrom(x => x.Item.TypeOfUsage))
                .ForPath(m => m.Item.EquipmentValidity, opts => opts.MapFrom(x => x.Item.EquipmentValidity));

            this.CreateMap<FilteredDataModel<AvailableEquipment>, SearchResponseModel<AvailableEquipmentResponseModel>>()
                .ForMember(m => m.DataCollection, opts => opts.MapFrom(x => x.DataRows.ToList()))
                .ForMember(m => m.Count, opts => opts.MapFrom(x => x.AllDataRowsCount));

            this.CreateMap<History, ReserveItemHistoryResponseModel>()
                .ForPath(m => m.ItemEquipmentTypeName, opts => opts.MapFrom(x => x.Item.EquipmentType.Name));

            this.CreateMap<AvailableEquipmentRequestModel, AvailableEquipment>();

            this.CreateMap<AvailableEquipment, ItemResponseModel>()
                .ForPath(m => m.Id, opts => opts.MapFrom(x => x.Item.Id))
                .ForPath(m => m.IcmIdSgwId, opts => opts.MapFrom(x => x.Item.IcmIdSgwId))
                .ForPath(m => m.EquipmentName, opts => opts.MapFrom(x => x.Item.EquipmentName))
                .ForPath(m => m.EquipmentSerialNum, opts => opts.MapFrom(x => x.Item.EquipmentSerialNum.Trim()))
                .ForPath(m => m.SapserialNum, opts => opts.MapFrom(x => x.Item.SapserialNum))
                .ForPath(m => m.SapmaterialNum, opts => opts.MapFrom(x => x.Item.SapmaterialNum))
                .ForPath(m => m.EquipmentTypeId, opts => opts.MapFrom(x => x.Item.EquipmentTypeId))
                .ForPath(m => m.EquipmentMaterialGroup, opts => opts.MapFrom(x => x.Item.EquipmentMaterialGroup))
                .ForPath(m => m.TypeOfUsage, opts => opts.MapFrom(x => x.Item.TypeOfUsage))
                .ForPath(m => m.EquipmentValidity, opts => opts.MapFrom(x => x.Item.EquipmentValidity));

            this.CreateMap<FilteredDataModel<AvailableEquipment>, SearchResponseModel<ItemResponseModel>>()
                .ForMember(m => m.DataCollection, opts => opts.MapFrom(x => x.DataRows.ToList()))
                .ForMember(m => m.Count, opts => opts.MapFrom(x => x.AllDataRowsCount));

            this.CreateMap<PostOffice, PostOfficesSelectListResponseModel>();

            this.CreateMap<FilteredDataModel<History>, SearchResponseModel<UserItemsToAcceptResponseModel>>()
                .ForMember(m => m.DataCollection, opts => opts.MapFrom(x => x.DataRows.ToList()))
                .ForMember(m => m.Count, opts => opts.MapFrom(x => x.AllDataRowsCount));

            this.CreateMap<History, UserItemsToAcceptResponseModel>()
                .ForMember(m => m.EquipmentName, opts => opts.MapFrom(h => h.Item.EquipmentName))
                .ForMember(m => m.EquipmentValidity, opt => opt.MapFrom(h => h.Item.EquipmentValidity))
                .ForMember(m => m.TypeOfUsage, opt => opt.MapFrom(h => h.Item.TypeOfUsage))
                .ForMember(m => m.FromUserId, opt => opt.MapFrom(h => h.FromUser.Id))
                .ForMember(m => m.FromUserFullName, opt => opt.MapFrom(h => h.FromUser != null ? h.FromUser.FullName : string.Empty))
                .ForMember(m => m.EquipmentSerialNum, opt => opt.MapFrom(h => h.Item.EquipmentSerialNum));

            this.CreateMap<FilteredDataModel<History>, SearchResponseModel<UserItemsToCancelResponseModel>>()
                .ForMember(m => m.DataCollection, opts => opts.MapFrom(x => x.DataRows.ToList()))
                .ForMember(m => m.Count, opts => opts.MapFrom(x => x.AllDataRowsCount));

            this.CreateMap<History, UserItemsToCancelResponseModel>()
                .ForMember(m => m.EquipmentName, opts => opts.MapFrom(h => h.Item.EquipmentName))
                .ForMember(m => m.ToUserId, opt => opt.MapFrom(h => h.ToUser.Id))
                .ForMember(m => m.ToUserFullName, opt => opt.MapFrom(h => h.ToUser != null ? h.ToUser.FullName : string.Empty))
                .ForMember(m => m.EquipmentSerialNum, opt => opt.MapFrom(h => h.Item.EquipmentSerialNum));
        }
    }
}
