﻿namespace EtWS.Api.Controllers
{
    using EtWS.Api.Infrastructure.Utils;
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Services.SubstitutionsService;

    public class SubstitutionsController : BaseApiController
    {
        private readonly Lazy<ISubstitutionsService> substitutionsService;

        public SubstitutionsController(Lazy<ISubstitutionsService> substitutionsService)
        {
            this.substitutionsService = substitutionsService;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(typeof(SubstitutionsResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SubstitutionsResponseModel>> GetAsync([Required, FromRoute] int id)
        {
            try
            {
                var substitution = await this.substitutionsService.Value.GetSubstitutionByIdAsync(id);
                return this.Ok(substitution);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("search")]
        [ProducesResponseType(typeof(SearchResponseModel<SubstitutionsResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<SubstitutionsResponseModel>>> GetSubstitutionsAsync([Required] SearchDataRequestModel request, bool isCurrentUserMol)
        {
            try
            {
                var substitutions = await this.substitutionsService.Value.GetSubstitutionsAsync(request, isCurrentUserMol);
                return this.Ok(substitutions);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("add")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> AddSubstitutionAsync([Required] SubstitutionDataRequestModel request)
        {
            try
            {
                await this.substitutionsService.Value.AddSubstitutionAsync(request);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPut("update")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateSubstitutionAsync([Required, FromBody] SubstitutionDataRequestModel request)
        {
            try
            {
                await this.substitutionsService.Value.UpdateSubstitutionAsync(request);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpDelete]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> DeleteSubstitutionAsync([Required] int id)
        {
            try
            {
                await this.substitutionsService.Value.DeleteSubstitutionAsync(id);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
