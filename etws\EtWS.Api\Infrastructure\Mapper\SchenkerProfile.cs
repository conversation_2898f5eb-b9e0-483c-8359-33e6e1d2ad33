﻿namespace EtWS.Api.Infrastructure.Mapper
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.SchenkerModels;

    public class SchenkerProfile : Profile
    {
        public SchenkerProfile()
        {
            this.CreateMap<SchenkersResponseModel, SchenkerResponseModel>()
                .ReverseMap();

            this.CreateMap<SchenkerOpCodeResponseModel, SchenkerSelectItemResponseModel>();

            this.CreateMap<SchenkersResponseModelSearchResponseModel, SearchResponseModel<SchenkerResponseModel>>()
                .ForMember(r => r.Count, opt => opt.MapFrom(c => c.Count));

            this.CreateMap<SchenkerRequestModel, SchenkersRequestModel>();
        }
    }
}
