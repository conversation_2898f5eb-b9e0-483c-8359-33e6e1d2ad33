# ItemDataResponseModelSearchResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**count** | **number** |  | [optional] [default to undefined]
**dataCollection** | [**Array&lt;ItemDataResponseModel&gt;**](ItemDataResponseModel.md) |  | [optional] [default to undefined]

## Example

```typescript
import { ItemDataResponseModelSearchResponseModel } from './api';

const instance: ItemDataResponseModelSearchResponseModel = {
    count,
    dataCollection,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
