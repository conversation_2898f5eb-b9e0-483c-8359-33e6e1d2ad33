# EquipmentTypeResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**name** | **string** |  | [optional] [default to undefined]
**sapmaterialNum** | **string** |  | [optional] [default to undefined]
**serialNumberRequired** | **boolean** |  | [optional] [default to undefined]
**equipmentGroupId** | **number** |  | [optional] [default to undefined]
**unitOfMeasure** | **number** |  | [optional] [default to undefined]
**sendMethod** | **string** |  | [optional] [default to undefined]
**sapsupplyCode** | **string** |  | [optional] [default to undefined]
**minimumQuantity** | **number** |  | [optional] [default to undefined]
**isTransferFromSapAllowed** | **boolean** |  | [optional] [default to undefined]
**boxCapacity** | **number** |  | [optional] [default to undefined]
**lastMonthlyOrderQuantity** | **number** |  | [optional] [default to undefined]
**brprojectCode** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { EquipmentTypeResponseModel } from './api';

const instance: EquipmentTypeResponseModel = {
    id,
    name,
    sapmaterialNum,
    serialNumberRequired,
    equipmentGroupId,
    unitOfMeasure,
    sendMethod,
    sapsupplyCode,
    minimumQuantity,
    isTransferFromSapAllowed,
    boxCapacity,
    lastMonthlyOrderQuantity,
    brprojectCode,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
