﻿namespace EtDb.DataHandlers.Interfaces
{
    using EtDb.ApiClients.EtDb.Enums;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Models;
    using EtDb.Infrastructure.Enumerations;
    using Net.Ams.Models;

    public interface IUserDataHandler
    {
        IQueryable<User> GetAll();

        Task<User?> GetUserByIdAsync(string id);

        Task<User?> GetUserByIdAsync(string id, params string[] includeProperties);

        Task<User?> GetUserByADAccountAsync(string adAccount);

        Task<User?> GetMolOfOpAsync(string opCode);

        IQueryable<User> GetActiveUsers();

        Task AddUserAsync(ApplicationUserModel cacheUser);

        Task AddUsersAsync(IList<ApplicationUserModel> usersToAdd);

        Task<FilteredDataModel<User>> GetFilteredUsersAsync(string sortBy, string sortDir, int pageNumber, int pageSize, string query, IEnumerable<string> usersEln);

        Task EditUsersStatusAsync(IEnumerable<string> selectedUsers, UserStatus newStatus, string currentUserId);

        Task UpdateUserAsync(ApplicationUserModel cacheUser);

        Task UpdateAllUsersAsync(IDictionary<string, ApplicationUserModel> cacheUsers);

        IQueryable<User> GetUserMostFrequentTransfersTo(string userId);

        Task<Notification> UpdateUserNotificationAsync(string userId, NotificationType notificationType, string? text = null);

        Task RemoveUserNotificationAsync(string userId, NotificationType notificationType);

        Task RemoveUserNotificationAsync(string userId, int notificationId);

        Task<RecipientHistory?> GetRecipientHistoryAsync(string fromUserId, string toUserId);

        Task UpdateRecipientHistoryAsync(string fromUserId, string toUserId);

        Task<FilteredDataModel<User>> GetFilteredUsersForEditAsync(string sortBy, string sortDir, int pageNumber, int pageSize, string query, IEnumerable<string> usersEln);

        Task<User> UpdateUserAsync(User user);

        Task UpdateDbUsersWithCacheUserDataAsync(IEnumerable<User> users);
    }
}
