﻿namespace EtDb.Models.Requests
{
    public class InsertItemRequestModel
    {
        public string IcmIdSgwId { get; set; }

        public string SourceSystem { get; set; }

        public DateTime? ModifyDate { get; set; } = DateTime.UtcNow;

        public string EquipmentName { get; set; }

        public string EquipmentSerialNum { get; set; }

        public string SapserialNum { get; set; }

        [Required]
        public string SapmaterialNum { get; set; }

        public int EquipmentTypeId { get; set; }

        public string EquipmentMaterialGroup { get; set; }

        public int TypeOfUsage { get; set; }

        public string EquipmentValidity { get; set; }

        public DateTime? AdditionDate { get; set; } = DateTime.UtcNow;
    }
}
