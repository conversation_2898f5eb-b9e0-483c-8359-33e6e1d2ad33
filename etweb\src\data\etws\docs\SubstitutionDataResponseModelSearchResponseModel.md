# SubstitutionDataResponseModelSearchResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**count** | **number** |  | [optional] [default to undefined]
**dataCollection** | [**Array&lt;SubstitutionDataResponseModel&gt;**](SubstitutionDataResponseModel.md) |  | [optional] [default to undefined]

## Example

```typescript
import { SubstitutionDataResponseModelSearchResponseModel } from './api';

const instance: SubstitutionDataResponseModelSearchResponseModel = {
    count,
    dataCollection,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
