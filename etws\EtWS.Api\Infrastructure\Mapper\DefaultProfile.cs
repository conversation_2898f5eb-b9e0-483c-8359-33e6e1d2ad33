﻿namespace EtWS.Api.Infrastructure.Mapper
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.UserModels;

    public class DefaultProfile : Profile
    {
        public DefaultProfile()
        {
            this.CreateMap<SearchDataRequestModel, SearchRequestModel>();

            this.CreateMap<SearchDataRequestModel, SearchRequestModelWithUserId>();

            this.CreateMap<HistoryRequest, HistoryRequestModel>();

            this.CreateMap<StatusHistoryRequest, StatusHistoryRequestModel>();

            this.CreateMap<AvailableEqipmentRequest, AvailableEquipmentRequestModel>();

            this.CreateMap<UsersWithOpCodeResponseModel, UsersWithOPCodeResponse>();
        }
    }
}
