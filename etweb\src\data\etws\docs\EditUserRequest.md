# EditUserRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **string** |  | [default to undefined]
**eln** | **string** |  | [optional] [default to undefined]
**firstName** | **string** |  | [optional] [default to undefined]
**surname** | **string** |  | [optional] [default to undefined]
**familyName** | **string** |  | [optional] [default to undefined]
**status** | **string** |  | [optional] [default to undefined]
**schenkerId** | **number** |  | [default to undefined]
**op** | **string** |  | [optional] [default to undefined]
**type** | **string** |  | [optional] [default to undefined]
**userType** | **string** |  | [optional] [default to undefined]
**clientNumber** | **string** |  | [optional] [default to undefined]
**city** | **string** |  | [optional] [default to undefined]
**cityId** | **number** |  | [default to undefined]
**region** | **string** |  | [optional] [default to undefined]
**iptuName** | **string** |  | [optional] [default to undefined]
**phoneNumber** | **string** |  | [optional] [default to undefined]
**statusClass** | **string** |  | [optional] [default to undefined]
**userStatuses** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { EditUserRequest } from './api';

const instance: EditUserRequest = {
    id,
    eln,
    firstName,
    surname,
    familyName,
    status,
    schenkerId,
    op,
    type,
    userType,
    clientNumber,
    city,
    cityId,
    region,
    iptuName,
    phoneNumber,
    statusClass,
    userStatuses,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
