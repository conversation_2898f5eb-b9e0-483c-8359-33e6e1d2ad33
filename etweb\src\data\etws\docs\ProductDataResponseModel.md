# ProductDataResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**name** | **string** |  | [optional] [default to undefined]
**sapMaterialNum** | **string** |  | [optional] [default to undefined]
**equipmentNameFirst** | **string** |  | [optional] [default to undefined]
**matFirst** | **string** |  | [optional] [default to undefined]
**serialNumbersRequiredStatus** | **string** |  | [optional] [default to undefined]
**minimumQuantity** | **number** |  | [optional] [default to undefined]
**sapRequestType** | **number** |  | [optional] [default to undefined]
**brProjectName** | **string** |  | [optional] [default to undefined]
**sapElementCode** | **string** |  | [optional] [default to undefined]
**equipmentGroupId** | **number** |  | [optional] [default to undefined]
**unitOfMeasure** | **number** |  | [optional] [default to undefined]
**boxCapacity** | **number** |  | [optional] [default to undefined]

## Example

```typescript
import { ProductDataResponseModel } from './api';

const instance: ProductDataResponseModel = {
    id,
    name,
    sapMaterialNum,
    equipmentNameFirst,
    matFirst,
    serialNumbersRequiredStatus,
    minimumQuantity,
    sapRequestType,
    brProjectName,
    sapElementCode,
    equipmentGroupId,
    unitOfMeasure,
    boxCapacity,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
