﻿using EtDb.ApiClients.EtDb.Enums;
using EtDb.ApiClients.EtDb.Models;
using EtDb.DataHandlers.Enums;
using EtDb.DataHandlers.Models;

namespace EtDb.DataHandlers.Interfaces
{
    public interface IHistoryDataHandler
    {
        public IQueryable<History> GetAll();

        public IQueryable<History> GetHistoryByItemId(int itemId);

        public IQueryable<History> GetHistoryByFromUserId(string userId);

        public IQueryable<History> GetHistoryByToUserId(string userId);

        public IQueryable<History> GetHistoryResrvedItemsByFromUserId(string userId);

        public IQueryable<History> GetHistoryItemsToAccept(string userId);

        public IQueryable<History> GetHistoryTransferedItems(string userId);

        public IQueryable<History> GetHistoryFromUserByItemId(string userId, int itemId);

        public IQueryable<History> GetHistoryToUserByItemId(string userId, int itemId);

        public IQueryable<string> GetHistoryToUserIdUnacceptedItems(double days);

        public IQueryable<string> GetHistoryToUserIdUnacceptedItems(IQueryable<History> historyEntries, double days);

        public IQueryable<StatusHistory> GetStatusHistory();

        public Task<IDictionary<string, List<string>>> GetHistoryToUserIdCanceledItemsForCurrentDayAsync();

        void UpdateHistoryDelieverItems(
            History historyEntry,
            DocStatus docStatus,
            string toUserId,
            string currentDeliveryNum,
            int? postOfficeId = null,
            string waybillNum = null,
            DateTime? waybillDate = null);

        public void UpdateHistoryAcceptItems(History historyEntry, DocStatus docStatus, RefuseReasonsList? refuseReason);

        public Task<KeyValuePair<string, IDictionary<string, TransferedItemsModel>>> CancelHistoryExpiredTransfersAsync(double days);

        public Task UpdateHistoryTransfersToBlockedUsersAsync(IEnumerable<string> users);

        Task<StatusHistory> CreateStatusHistoryEntryAsync(int historyEntryId, string fromUserId, DocStatus docStatusOld, DocStatus docStatusNew, string sourceSystem);

        public Task RemoveHistoryEntryAsync(History historyEntry);

        public Task RemoveHistoryEntriesAsync(IEnumerable<History> historyEntries);

        public void UpdateAdditionDate(History historyEntry);

        public Task<IDictionary<string, TransferedItemsModel>> GetUsersPendingTransferedItemsAsync(IDictionary<string, TransferedItemsModel> usersCollection);

        public Task<int> GetIncomingItemstCountAsync(int opId, DateTime fromDate, DateTime toDate);

        public Task<int> GetReturnedItemsFromClientCountAsync(int opId, DateTime fromDate, DateTime toDate);
    }
}
