﻿using EtDb.ApiClients.EtDb.Enums;
using EtDb.ApiClients.EtDb.Models;

namespace EtDb.DataHandlers.Models
{
    public class TransferedItemsModel
    {
        public TransferedItemsModel(User toUser, string fromUserId, string serialNumber)
        {
            this.TotalCount = default(int);
            this.ExpiredCount = default(int);
            this.ToUserId = toUser.Id;
            this.FromUserId = fromUserId;
            this.SerialNumbers = new List<string>() { serialNumber };

            Notification toUserUnacceptedTransferNotification = toUser.Notification.SingleOrDefault(n => n.NotificationType == (int)NotificationType.UnacceptedTransfer);
            this.ToUserUnacceptedTransferNotificationId = toUserUnacceptedTransferNotification != null
                ? toUserUnacceptedTransferNotification.Id
                : 0;
        }

        public int TotalCount { get; set; }

        public int ExpiredCount { get; set; }

        public string ToUserId { get; set; }

        public int ToUserUnacceptedTransferNotificationId { get; set; }

        public string FromUserId { get; set; }

        public IList<string> SerialNumbers { get; set; }
    }
}
