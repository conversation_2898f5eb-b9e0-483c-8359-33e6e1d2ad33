﻿namespace EtWS.Api.Infrastructure.Mapper
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CityModels;
    using EtWS.Models.Responses.CommonModels;

    public class CityProfile : Profile
    {
        public CityProfile()
        {
            this.CreateMap<CitiesResponseModel, CityResponseModel>()
                .ReverseMap();

            this.CreateMap<CitiesResponseModelSearchResponseModel, SearchResponseModel<CityResponseModel>>()
                .ForMember(r => r.Count, opt => opt.MapFrom(c => c.Count));

            this.CreateMap<CityRequestModel, CitiesRequestModel>();

            this.CreateMap<CitiesResponseModelSearchResponseModel, IEnumerable<CitiesSelectItemResponseModel>>()
                .ConstructUsing(x => this.MapCitiesSelectListItems(x));

            this.CreateMap<CitiesResponseModel, CitiesSelectItemResponseModel>();

            this.CreateMap<CitiesResponseModel, SapCityCodesResponseModel>();
        }

        private IEnumerable<CitiesSelectItemResponseModel> MapCitiesSelectListItems(CitiesResponseModelSearchResponseModel response)
        {
            var items = new List<CitiesSelectItemResponseModel>();

            foreach (var city in response.DataCollection)
            {
                items.Add(new CitiesSelectItemResponseModel
                {
                    Id = city.Id,
                    Name = city.Name,
                });
            }

            return items;
        }
    }
}
