# QuantityCalculationObjectResponseModelSearchResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**count** | **number** |  | [optional] [default to undefined]
**dataCollection** | [**Array&lt;QuantityCalculationObjectResponseModel&gt;**](QuantityCalculationObjectResponseModel.md) |  | [optional] [default to undefined]

## Example

```typescript
import { QuantityCalculationObjectResponseModelSearchResponseModel } from './api';

const instance: QuantityCalculationObjectResponseModelSearchResponseModel = {
    count,
    dataCollection,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
