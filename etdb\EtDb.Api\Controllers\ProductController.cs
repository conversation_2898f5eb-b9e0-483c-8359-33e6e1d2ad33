﻿namespace EtDb.Api.Controllers
{
    using EtDb.Api.Infrastructure.Utils;
    using EtDb.Models.Requests;
    using EtDb.Models.Responses;
    using EtDb.Services.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class ProductController : BaseApiController
    {
        private readonly Lazy<IProductService> administrationService;

        public ProductController(Lazy<IProductService> administrationService)
        {
            this.administrationService = administrationService;
        }

        [HttpGet]
        [ProducesResponseType(typeof(AdministrateProductResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<AdministrateProductResponseModel>> GetProductById([Required] int id)
        {
            try
            {
                var result = await this.administrationService.Value.GetProductById(id);
                return this.Ok(result);
            }
            catch (ArgumentException arg)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPost]
        [Route("search")]
        [ProducesResponseType(typeof(SearchResponseModel<AdministrateProductResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<AdministrateProductResponseModel>>> SearchProductsAsync([Required] SearchRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.administrationService.Value.SearchProducts(request);
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (ArgumentException arg)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPost]
        [Route("insert")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> InsertProduct([Required] AdministrateProductRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var id = await this.administrationService.Value.InsertProduct(request);
                    return this.Ok(id);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (ArgumentException arg)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPut]
        [Route("update")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateProduct([Required] AdministrateProductRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    await this.administrationService.Value.UpdateProduct(request);
                    return this.Ok();
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (ArgumentException arg)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }
    }
}
