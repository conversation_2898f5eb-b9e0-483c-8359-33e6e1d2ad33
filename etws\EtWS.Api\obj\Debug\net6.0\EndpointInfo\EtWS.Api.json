{"openapi": "3.0.1", "info": {"title": "et-ws", "version": "v1"}, "paths": {"/api/account/login": {"post": {"tags": ["Account"], "operationId": "ApiAccountLoginPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/UserLoginRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/UserLoginRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/UserLoginRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginResponseModel"}}}}}}}, "/api/account/logout": {"post": {"tags": ["Account"], "operationId": "ApiAccountLogoutPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/account/set-impersonation-cookie": {"get": {"tags": ["Account"], "operationId": "ApiAccountSetImpersonationCookieGet", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/account/delete-impersonation-cookie": {"get": {"tags": ["Account"], "operationId": "ApiAccountDeleteImpersonationCookieGet", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/cities/{id}": {"get": {"tags": ["Cities"], "summary": "Gets city data by its id.", "operationId": "ApiCitiesByIdGet", "parameters": [{"name": "id", "in": "path", "description": "The id of the city.", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CityResponseModel"}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/cities/region/{id}": {"get": {"tags": ["Cities"], "summary": "Returns the region of the city as string.", "operationId": "ApiCitiesRegionByIdGet", "parameters": [{"name": "id", "in": "path", "description": "The id of the city.", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/cities/search": {"post": {"tags": ["Cities"], "summary": "Search cities based on criterias.", "operationId": "ApiCitiesSearchPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"description": "The search parameters of the request.", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CityResponseModelSearchResponseModel"}}}}}}}, "/api/cities/select-list": {"get": {"tags": ["Cities"], "operationId": "ApiCitiesSelectListGet", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CitiesSelectItemResponseModel"}}}}}}}}, "/api/cities/add": {"post": {"tags": ["Cities"], "summary": "Adds city to the database.", "operationId": "ApiCitiesAddPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"description": "The city data to save in the database.", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/CityRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/CityRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/CityRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/cities/update/{id}": {"put": {"tags": ["Cities"], "summary": "Updates an existing city in the databse.", "operationId": "ApiCitiesUpdateByIdPut", "parameters": [{"name": "id", "in": "path", "description": "The id of the city, being updated.", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"description": "The new city data.", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/CityRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/CityRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/CityRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/cities/block": {"put": {"tags": ["Cities"], "summary": "Blocks selected cities in the database.", "operationId": "ApiCitiesBlockPut", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"description": "The ids of the cities, that needs to be blocked.", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/cities/activate": {"put": {"tags": ["Cities"], "summary": "Activates selected cities in the database.", "operationId": "ApiCitiesActivatePut", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"description": "The ids of the cities, that needs to be activated.", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/cities/sap-city-code-and-cluster/{cityId}": {"get": {"tags": ["Cities"], "summary": "Gets a city by its id (request parameter) and returns the SAPCityCode and Cluster of the city.", "operationId": "ApiCitiesSapCityCodeAndClusterByCityIdGet", "parameters": [{"name": "cityId", "in": "path", "description": "The id of the city.", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SapCityCodesResponseModel"}}}}}}}, "/api/equipment/search-available-equipment": {"post": {"tags": ["Equipment"], "operationId": "ApiEquipmentSearchAvailableEquipmentPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailableEquipmentDataResponseModelSearchResponseModel"}}}}}}}, "/api/equipment/reserve-item": {"post": {"tags": ["Equipment"], "operationId": "ApiEquipmentReserveItemPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ReserveItemForTransferDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ReserveItemForTransferDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ReserveItemForTransferDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveItemHistoryResponseModel"}}}}}}}, "/api/equipment/reserve-all-items": {"post": {"tags": ["Equipment"], "operationId": "ApiEquipmentReserveAllItemsPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/equipment/reserved-items-count": {"get": {"tags": ["Equipment"], "operationId": "ApiEquipmentReservedItemsCountGet", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/equipment/remove-item": {"post": {"tags": ["Equipment"], "operationId": "ApiEquipmentRemoveItemPost", "parameters": [{"name": "itemId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveItemFromTransferResponseModel"}}}}}}}, "/api/equipment/remove-items": {"post": {"tags": ["Equipment"], "operationId": "ApiEquipmentRemoveItemsPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/equipment/remove-all-items": {"post": {"tags": ["Equipment"], "operationId": "ApiEquipmentRemoveAllItemsPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/equipment/search-transfer-data": {"post": {"tags": ["Equipment"], "operationId": "ApiEquipmentSearchTransferDataPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemDataResponseModelSearchResponseModel"}}}}}}}, "/api/equipment/post-offices-select-list": {"get": {"tags": ["Equipment"], "operationId": "ApiEquipmentPostOfficesSelectListGet", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PostOfficesSelectListResponseModel"}}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/equipment/deliver-items": {"post": {"tags": ["Equipment"], "operationId": "ApiEquipmentDeliverItemsPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/DeliverItemsDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/DeliverItemsDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/DeliverItemsDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/equipment/search-user-items-to-accept": {"post": {"tags": ["Equipment"], "operationId": "ApiEquipmentSearchUserItemsToAcceptPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserItemsToAcceptResponseModelSearchResponseModel"}}}}}}}, "/api/equipment/search-user-items-to-cancel": {"post": {"tags": ["Equipment"], "operationId": "ApiEquipmentSearchUserItemsToCancelPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserItemsToCancelResponseModelSearchResponseModel"}}}}}}}, "/api/equipment/accept-items": {"post": {"tags": ["Equipment"], "operationId": "ApiEquipmentAcceptItemsPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/AcceptItemsForTransferRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/AcceptItemsForTransferRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/AcceptItemsForTransferRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/equipment/refuse-items": {"post": {"tags": ["Equipment"], "operationId": "ApiEquipmentRefuseItemsPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/RefuseItemsForTransferRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/RefuseItemsForTransferRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/RefuseItemsForTransferRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/equipment/cancel-items": {"post": {"tags": ["Equipment"], "operationId": "ApiEquipmentCancelItemsPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/CancelItemsForTransferRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/CancelItemsForTransferRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/CancelItemsForTransferRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/equipment-group/all": {"get": {"tags": ["EquipmentGroup"], "operationId": "ApiEquipmentGroupAllGet", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EquipmentGroupResponseModel"}}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/equipment-group/all/names": {"get": {"tags": ["EquipmentGroup"], "operationId": "ApiEquipmentGroupAllNamesGet", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/equipment-group/name/{sapMaterialNum}": {"get": {"tags": ["EquipmentGroup"], "operationId": "ApiEquipmentGroupNameBySapMaterialNumGet", "parameters": [{"name": "sapMaterialNum", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/equipment-group/by-id/{id}": {"get": {"tags": ["EquipmentGroup"], "operationId": "ApiEquipmentGroupByIdByIdGet", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentGroupResponseModel"}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/equipment-type/all": {"get": {"tags": ["EquipmentType"], "operationId": "ApiEquipmentTypeAllGet", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EquipmentTypeResponseModel"}}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/equipment-type/by-id/{id}": {"get": {"tags": ["EquipmentType"], "operationId": "ApiEquipmentTypeByIdByIdGet", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentTypeResponseModel"}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/equipment-type/type-id-by-sap-material-num/{sapMaterialNum}": {"get": {"tags": ["EquipmentType"], "operationId": "ApiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet", "parameters": [{"name": "sapMaterialNum", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/equipment-type/by-send-method/{sendMethod}": {"get": {"tags": ["EquipmentType"], "operationId": "ApiEquipmentTypeBySendMethodBySendMethodGet", "parameters": [{"name": "send<PERSON>ethod", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/equipment-type/concise-equipment-types": {"post": {"tags": ["EquipmentType"], "operationId": "ApiEquipmentTypeConciseEquipmentTypesPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ConciseEquipmentTypesDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ConciseEquipmentTypesDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ConciseEquipmentTypesDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EquipmentTypeConciseDto"}}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/equipment-type/last-month-quantities": {"get": {"tags": ["EquipmentType"], "operationId": "ApiEquipmentTypeLastMonthQuantitiesGet", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/incorrect-equipment/transfer-correction": {"post": {"tags": ["IncorrectEquipment"], "operationId": "ApiIncorrectEquipmentTransferCorrectionPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/TransferCorrectionRequest"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/TransferCorrectionRequest"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/TransferCorrectionRequest"}]}}}}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/incorrect-equipment/transfer-delete": {"delete": {"tags": ["IncorrectEquipment"], "operationId": "ApiIncorrectEquipmentTransferDeleteDelete", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/DeleteTransferRequest"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/DeleteTransferRequest"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/DeleteTransferRequest"}]}}}}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/incorrect-equipment/users-with-opcodes": {"get": {"tags": ["IncorrectEquipment"], "operationId": "ApiIncorrectEquipmentUsersWithOpcodesGet", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UsersWithOPCodeResponse"}}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/incorrect-equipment/check-serial-number/{equipmentSerialNum}": {"get": {"tags": ["IncorrectEquipment"], "operationId": "ApiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet", "parameters": [{"name": "equipmentSerialNum", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/incorrect-equipment/get-user-display-names/{namePrefix}": {"get": {"tags": ["IncorrectEquipment"], "operationId": "ApiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet", "parameters": [{"name": "namePrefix", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDisplayNameResponseModel"}}}}}}}}, "/api/incorrect-equipment/check-service-id/{serviceId}": {"get": {"tags": ["IncorrectEquipment"], "operationId": "ApiIncorrectEquipmentCheckServiceIdByServiceIdGet", "parameters": [{"name": "serviceId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/incorrect-equipment/get-histories/{equipmentSerialnumber}": {"get": {"tags": ["IncorrectEquipment"], "operationId": "ApiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet", "parameters": [{"name": "equipmentSerialnumber", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HistoriesResponse"}}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/products/{id}": {"get": {"tags": ["Products"], "operationId": "ApiProductsByIdGet", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchProductDataResponseModel"}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/products/search": {"post": {"tags": ["Products"], "operationId": "ApiProductsSearchPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchProductDataResponseModelSearchResponseModel"}}}}}}}, "/api/products/add": {"post": {"tags": ["Products"], "operationId": "ApiProductsAddPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ProductDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ProductDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ProductDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/products/update": {"put": {"tags": ["Products"], "operationId": "ApiProductsUpdatePut", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ProductDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ProductDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ProductDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/quantity-calculation-object/search": {"post": {"tags": ["QuantityCalculationObject"], "operationId": "ApiQuantityCalculationObjectSearchPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuantityCalculationObjectResponseModelSearchResponseModel"}}}}}}}, "/api/quantity-calculation-object/{id}": {"get": {"tags": ["QuantityCalculationObject"], "operationId": "ApiQuantityCalculationObjectByIdGet", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuantityCalculationObjectResponseModel"}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/quantity-calculation-object/update": {"post": {"tags": ["QuantityCalculationObject"], "operationId": "ApiQuantityCalculationObjectUpdatePost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/QuantityCalculationObjectRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/QuantityCalculationObjectRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/QuantityCalculationObjectRequestModel"}]}}}}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/schenkers/{id}": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "operationId": "ApiSchenkersByIdGet", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SchenkerResponseModel"}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/schenkers/users/{schenkerId}": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "operationId": "ApiSchenkersUsersBySchenkerIdGet", "parameters": [{"name": "schenkerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDataConciseResponseModel"}}}}}}}}, "/api/schenkers/select-list": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "operationId": "ApiSchenkersSelectListGet", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchenkerSelectItemResponseModel"}}}}}}}}, "/api/schenkers/search": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "operationId": "ApiSchenkersSearchPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SchenkerResponseModelSearchResponseModel"}}}}}}}, "/api/schenkers/add": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "operationId": "ApiSchenkersAddPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SchenkerRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SchenkerRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SchenkerRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/schenkers/update": {"put": {"tags": ["<PERSON><PERSON><PERSON>"], "operationId": "ApiSchenkersUpdatePut", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SchenkerRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SchenkerRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SchenkerRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/substitutions/{id}": {"get": {"tags": ["Substitutions"], "operationId": "ApiSubstitutionsByIdGet", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubstitutionsResponseModel"}}}}, "404": {"description": "Not Found", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}}}}, "/api/substitutions/search": {"post": {"tags": ["Substitutions"], "operationId": "ApiSubstitutionsSearchPost", "parameters": [{"name": "isCurrentUserMol", "in": "query", "schema": {"type": "boolean"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubstitutionsResponseModelSearchResponseModel"}}}}}}}, "/api/substitutions/add": {"post": {"tags": ["Substitutions"], "operationId": "ApiSubstitutionsAddPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SubstitutionDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SubstitutionDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SubstitutionDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/substitutions/update": {"put": {"tags": ["Substitutions"], "operationId": "ApiSubstitutionsUpdatePut", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SubstitutionDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SubstitutionDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SubstitutionDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/substitutions": {"delete": {"tags": ["Substitutions"], "operationId": "ApiSubstitutionsDelete", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/users/{userId}": {"get": {"tags": ["Users"], "operationId": "ApiUsersByUserIdGet", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchUserDataResponseModel"}}}}}}}, "/api/users/user-by-username/{username}": {"get": {"tags": ["Users"], "operationId": "ApiUsersUserByUsernameByUsernameGet", "parameters": [{"name": "username", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchUserDataResponseModel"}}}}}}}, "/api/users/ad-account/{userId}": {"get": {"tags": ["Users"], "operationId": "ApiUsersAdAccountByUserIdGet", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/users/users-select-list": {"get": {"tags": ["Users"], "operationId": "ApiUsersUsersSelectListGet", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsersListResponseModel"}}}}}}}, "/api/users/is-user-mol/{userId}": {"get": {"tags": ["Users"], "operationId": "ApiUsersIsUserMolByUserIdGet", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "boolean"}}}}}}}, "/api/users/all-mols": {"get": {"tags": ["Users"], "operationId": "ApiUsersAllMolsGet", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDataConciseResponseModel"}}}}}}}}, "/api/users/search": {"post": {"tags": ["Users"], "operationId": "ApiUsersSearchPost", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SearchDataRequestModel"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponseModel"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchUserDataResponseModelSearchResponseModel"}}}}}}}, "/api/users": {"put": {"tags": ["Users"], "operationId": "ApiUsersPut", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/EditUserRequest"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/EditUserRequest"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/EditUserRequest"}]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/users/activate-users": {"put": {"tags": ["Users"], "operationId": "ApiUsersActivateUsersPut", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/users/block-users": {"put": {"tags": ["Users"], "operationId": "ApiUsersBlockUsersPut", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}}}}}, "/api/warehouses/select-list": {"get": {"tags": ["Warehouses"], "operationId": "ApiWarehousesSelectListGet", "parameters": [{"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WarehousesResponseModel"}}}}}}}}, "/api/warehouses/by-region": {"get": {"tags": ["Warehouses"], "operationId": "ApiWarehousesByRegionGet", "parameters": [{"name": "region", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Input-Request-Id", "in": "header", "schema": {"type": "string"}}, {"name": "Input-Timestamp", "in": "header", "schema": {"type": "string", "format": "date-time"}}], "responses": {"400": {"description": "Bad Request", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "500": {"description": "Server Error", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "headers": {"Output-Request-Id": {"schema": {"type": "string"}}, "Output-Timestamp": {"schema": {"type": "string", "format": "date-time"}}}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WarehousesResponseModel"}}}}}}}}}, "components": {"schemas": {"AcceptItemsForTransferRequestModel": {"type": "object", "properties": {"selectedItems": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "AvailableEquipmentDataResponseModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "itemId": {"type": "integer", "format": "int32"}, "userId": {"type": "string", "nullable": true}, "itemQuantity": {"type": "integer", "format": "int32"}, "isDaily": {"type": "boolean"}, "isReserved": {"type": "boolean"}, "isWaitingForConfirmation": {"type": "boolean"}, "item": {"allOf": [{"$ref": "#/components/schemas/ItemDataResponseModel"}], "nullable": true}}, "additionalProperties": false}, "AvailableEquipmentDataResponseModelSearchResponseModel": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "dataCollection": {"type": "array", "items": {"$ref": "#/components/schemas/AvailableEquipmentDataResponseModel"}, "nullable": true}}, "additionalProperties": false}, "BaseResponseModel": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}}, "additionalProperties": false}, "CancelItemsForTransferRequestModel": {"type": "object", "properties": {"selectedItems": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "refuseReason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CitiesSelectItemResponseModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CityRequestModel": {"required": ["name", "region"], "type": "object", "properties": {"name": {"maxLength": 50, "minLength": 0, "type": "string"}, "region": {"type": "integer", "format": "int32"}, "sapCityCode": {"type": "string", "nullable": true}, "cluster": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CityResponseModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "region": {"type": "integer", "format": "int32"}, "isDeleted": {"type": "boolean"}, "sapCityCode": {"type": "string", "nullable": true}, "cluster": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CityResponseModelSearchResponseModel": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "dataCollection": {"type": "array", "items": {"$ref": "#/components/schemas/CityResponseModel"}, "nullable": true}}, "additionalProperties": false}, "ConciseEquipmentTypesDataRequestModel": {"required": ["equipmentTypeIds"], "type": "object", "properties": {"equipmentTypeIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "serialNumbersRequired": {"type": "boolean"}}, "additionalProperties": false}, "DeleteTransferRequest": {"required": ["id", "itemId"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "itemId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DeliverItemsDataRequestModel": {"required": ["selectedItems", "toUserId"], "type": "object", "properties": {"selectedItems": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "toUserId": {"type": "string"}, "isSpecialUser": {"type": "boolean"}, "postOfficeId": {"type": "integer", "format": "int32", "nullable": true}, "waybillNum": {"type": "string", "nullable": true}, "waybillDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "EditUserRequest": {"required": ["cityId", "id", "schenkerId"], "type": "object", "properties": {"id": {"type": "string"}, "eln": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "surname": {"type": "string", "nullable": true}, "familyName": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "schenkerId": {"type": "integer", "format": "int32"}, "op": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "userType": {"type": "string", "nullable": true}, "clientNumber": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "cityId": {"type": "integer", "format": "int32"}, "region": {"type": "string", "nullable": true}, "iptuName": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "statusClass": {"type": "string", "nullable": true}, "userStatuses": {"type": "string", "nullable": true}}, "additionalProperties": false}, "EquipmentGroupResponseModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "containsSubstitutes": {"type": "boolean"}}, "additionalProperties": false}, "EquipmentTypeConciseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "sapMaterialNum": {"type": "string", "nullable": true}}, "additionalProperties": false}, "EquipmentTypeResponseModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "sapmaterialNum": {"type": "string", "nullable": true}, "serialNumberRequired": {"type": "boolean"}, "equipmentGroupId": {"type": "integer", "format": "int32"}, "unitOfMeasure": {"type": "integer", "format": "int32"}, "sendMethod": {"type": "string", "nullable": true}, "sapsupplyCode": {"type": "string", "nullable": true}, "minimumQuantity": {"type": "integer", "format": "int32"}, "isTransferFromSapAllowed": {"type": "boolean"}, "boxCapacity": {"type": "integer", "format": "int32", "nullable": true}, "lastMonthlyOrderQuantity": {"type": "integer", "format": "int32", "nullable": true}, "brprojectCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "HistoriesResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "insertDate": {"type": "string", "nullable": true}, "itemId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ItemDataResponseModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "icmIdSgwId": {"type": "string", "nullable": true}, "equipmentName": {"type": "string", "nullable": true}, "equipmentSerialNum": {"type": "string", "nullable": true}, "sapserialNum": {"type": "string", "nullable": true}, "sapmaterialNum": {"type": "string", "nullable": true}, "equipmentTypeId": {"type": "integer", "format": "int32"}, "equipmentMaterialGroup": {"type": "string", "nullable": true}, "typeOfUsage": {"type": "integer", "format": "int32"}, "equipmentValidity": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ItemDataResponseModelSearchResponseModel": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "dataCollection": {"type": "array", "items": {"$ref": "#/components/schemas/ItemDataResponseModel"}, "nullable": true}}, "additionalProperties": false}, "PostOfficesSelectListResponseModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "ProductDataRequestModel": {"required": ["equipmentGroupId", "equipmentNameFirst", "<PERSON><PERSON><PERSON><PERSON>", "minimumQuantity", "name", "sapMaterialNum", "sapRequestType", "unitOfMeasure"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"maxLength": 100, "minLength": 0, "type": "string"}, "sapMaterialNum": {"maxLength": 50, "minLength": 0, "pattern": "^\\s*\\S+\\s*$", "type": "string"}, "equipmentNameFirst": {"maxLength": 100, "minLength": 0, "type": "string"}, "matFirst": {"maxLength": 50, "minLength": 0, "pattern": "^\\s*\\S+\\s*$", "type": "string"}, "minimumQuantity": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "sapRequestType": {"type": "integer", "format": "int32"}, "brProjectName": {"type": "string", "nullable": true}, "sapElementCode": {"type": "string", "nullable": true}, "equipmentGroupId": {"type": "integer", "format": "int32"}, "unitOfMeasure": {"type": "integer", "format": "int32"}, "boxCapacity": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "QuantityCalculationObjectRequestModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "criticalQuantity": {"type": "integer", "format": "int32"}, "toggleToAutomaticGeneration": {"type": "boolean"}}, "additionalProperties": false}, "QuantityCalculationObjectResponseModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "criticalQuantity": {"type": "integer", "format": "int32"}, "isGroupWithSubstitutes": {"type": "boolean"}, "isManuallySet": {"type": "boolean"}, "toggleToAutomaticGeneration": {"type": "boolean"}, "lastUpdateDate": {"type": "string", "format": "date-time"}, "opCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "QuantityCalculationObjectResponseModelSearchResponseModel": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "dataCollection": {"type": "array", "items": {"$ref": "#/components/schemas/QuantityCalculationObjectResponseModel"}, "nullable": true}}, "additionalProperties": false}, "RefuseItemsForTransferRequestModel": {"type": "object", "properties": {"selectedItems": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "refuseReason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RemoveItemFromTransferResponseModel": {"type": "object", "properties": {"removedEquipmentTypeName": {"type": "string", "nullable": true}, "reservedItemsCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ReserveItemForTransferDataRequestModel": {"type": "object", "properties": {"itemId": {"type": "integer", "format": "int32", "nullable": true}, "itemSerialNumber": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ReserveItemHistoryResponseModel": {"type": "object", "properties": {"itemId": {"type": "string", "nullable": true}, "itemEquipmentTypeName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SapCityCodesResponseModel": {"type": "object", "properties": {"sapCityCode": {"type": "string", "nullable": true}, "cluster": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SchenkerRequestModel": {"required": ["opCode"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "opCode": {"maxLength": 5, "minLength": 4, "type": "string"}, "transportArea": {"type": "integer", "format": "int32"}, "processingTime": {"type": "integer", "format": "int32"}, "protectiveTime": {"type": "integer", "format": "int32"}, "cityId": {"type": "integer", "format": "int32"}, "address": {"type": "string", "nullable": true}, "localWarehouseId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "SchenkerResponseModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "opCode": {"type": "string", "nullable": true}, "transportArea": {"type": "integer", "format": "int32"}, "processingTime": {"type": "integer", "format": "int32"}, "protectiveTime": {"type": "integer", "format": "int32"}, "cityId": {"type": "integer", "format": "int32"}, "address": {"type": "string", "nullable": true}, "regionName": {"type": "string", "nullable": true}, "iptuName": {"type": "string", "nullable": true}, "localWarehouseId": {"type": "integer", "format": "int32", "nullable": true}, "localWarehouseName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SchenkerResponseModelSearchResponseModel": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "dataCollection": {"type": "array", "items": {"$ref": "#/components/schemas/SchenkerResponseModel"}, "nullable": true}}, "additionalProperties": false}, "SchenkerSelectItemResponseModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "opCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchDataRequestModel": {"type": "object", "properties": {"pageNumber": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "pageSize": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "sortBy": {"type": "string", "nullable": true}, "sortDir": {"type": "string", "nullable": true}, "query": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchProductDataResponseModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "sapMaterialNum": {"type": "string", "nullable": true}, "equipmentNameFirst": {"type": "string", "nullable": true}, "matFirst": {"type": "string", "nullable": true}, "minimumQuantity": {"type": "integer", "format": "int32"}, "sapRequestType": {"type": "integer", "format": "int32"}, "brProjectName": {"type": "string", "nullable": true}, "sapElementCode": {"type": "string", "nullable": true}, "equipmentGroupId": {"type": "integer", "format": "int32"}, "unitOfMeasure": {"type": "integer", "format": "int32"}, "boxCapacity": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "SearchProductDataResponseModelSearchResponseModel": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "dataCollection": {"type": "array", "items": {"$ref": "#/components/schemas/SearchProductDataResponseModel"}, "nullable": true}}, "additionalProperties": false}, "SearchUserDataResponseModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "eln": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "surname": {"type": "string", "nullable": true}, "familyName": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "op": {"type": "string", "nullable": true}, "regionId": {"type": "integer", "format": "int32", "nullable": true}, "region": {"type": "string", "nullable": true}, "cityId": {"type": "integer", "format": "int32"}, "city": {"type": "string", "nullable": true}, "clientNumber": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "iptuName": {"type": "string", "nullable": true}, "position": {"type": "string", "nullable": true}, "adAccount": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "isMolOfOp": {"type": "boolean"}, "opcode": {"type": "string", "nullable": true}, "schenkerId": {"type": "integer", "format": "int32", "nullable": true}, "blocked": {"enum": ["Active", "Blocked"], "type": "string", "format": "string", "x-ms-enum": {"name": "UserStatus", "modelAsString": false}}, "dataBlocked": {"type": "string", "format": "date-time", "nullable": true}, "blockedByUserId": {"type": "string", "nullable": true}, "hasPendingTransfer": {"type": "boolean"}}, "additionalProperties": false}, "SearchUserDataResponseModelSearchResponseModel": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "dataCollection": {"type": "array", "items": {"$ref": "#/components/schemas/SearchUserDataResponseModel"}, "nullable": true}}, "additionalProperties": false}, "SubstitutionDataRequestModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "opId": {"type": "integer", "format": "int32"}, "opCode": {"type": "string", "nullable": true}, "fromDate": {"type": "string", "format": "date-time"}, "toDate": {"type": "string", "format": "date-time"}, "forUserId": {"type": "string", "nullable": true}, "forUserFullName": {"type": "string", "nullable": true}, "substituteUserId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SubstitutionsResponseModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "opCode": {"type": "string", "nullable": true}, "fromDate": {"type": "string", "format": "date-time"}, "toDate": {"type": "string", "format": "date-time"}, "forUserId": {"type": "string", "nullable": true}, "forUserFullName": {"type": "string", "nullable": true}, "substituteUserId": {"type": "string", "nullable": true}, "substituteUserFullName": {"type": "string", "nullable": true}, "forUserAdAccount": {"type": "string", "nullable": true}, "forSubstituteAdAccount": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SubstitutionsResponseModelSearchResponseModel": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "dataCollection": {"type": "array", "items": {"$ref": "#/components/schemas/SubstitutionsResponseModel"}, "nullable": true}}, "additionalProperties": false}, "TransferCorrectionRequest": {"required": ["equipmentSerialNum", "transferTo"], "type": "object", "properties": {"equipmentSerialNum": {"type": "string"}, "transferTo": {"type": "string"}, "serviceId": {"type": "string", "nullable": true}, "nameOfTechnician": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}, "deliveryShop": {"type": "string", "nullable": true}, "insertDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "UserDataConciseResponseModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "eln": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserDisplayNameResponseModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "iptuName": {"type": "string", "nullable": true}, "eln": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserItemsToAcceptResponseModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "equipmentName": {"type": "string", "nullable": true}, "equipmentValidity": {"type": "string", "nullable": true}, "typeOfUsage": {"type": "integer", "format": "int32"}, "fromUserId": {"type": "string", "nullable": true}, "fromUserFullName": {"type": "string", "nullable": true}, "insertDate": {"type": "string", "format": "date-time", "nullable": true}, "equipmentSerialNum": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserItemsToAcceptResponseModelSearchResponseModel": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "dataCollection": {"type": "array", "items": {"$ref": "#/components/schemas/UserItemsToAcceptResponseModel"}, "nullable": true}}, "additionalProperties": false}, "UserItemsToCancelResponseModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "intDeliveryNum": {"type": "string", "nullable": true}, "equipmentName": {"type": "string", "nullable": true}, "toUserId": {"type": "string", "nullable": true}, "toUserFullName": {"type": "string", "nullable": true}, "insertDate": {"type": "string", "format": "date-time", "nullable": true}, "equipmentSerialNum": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserItemsToCancelResponseModelSearchResponseModel": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "dataCollection": {"type": "array", "items": {"$ref": "#/components/schemas/UserItemsToCancelResponseModel"}, "nullable": true}}, "additionalProperties": false}, "UserLoginRequestModel": {"required": ["password", "username"], "type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "additionalProperties": false}, "UserLoginResponseModel": {"type": "object", "properties": {"userRoles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "username": {"type": "string", "nullable": true}, "profilePicture": {"type": "string", "format": "byte", "nullable": true}}, "additionalProperties": false}, "UsersListResponseModel": {"type": "object", "properties": {"usersSelectListResponseModels": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/UsersSelectListResponseModel"}}, "nullable": true}, "iptuListResponseModel": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UsersSelectListResponseModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UsersWithOPCodeResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "iptuName": {"type": "string", "nullable": true}, "opcode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ValidationProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}, "errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": {}}, "WarehousesResponseModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}