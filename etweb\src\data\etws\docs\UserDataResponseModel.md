# UserDataResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **string** |  | [optional] [default to undefined]
**eln** | **string** |  | [optional] [default to undefined]
**displayName** | **string** |  | [optional] [default to undefined]
**iptuName** | **string** |  | [optional] [default to undefined]
**adAccount** | **string** |  | [optional] [default to undefined]
**schenkerId** | **number** |  | [optional] [default to undefined]
**blocked** | **string** |  | [optional] [default to undefined]
**dataBlocked** | **string** |  | [optional] [default to undefined]
**blockedByUser** | **string** |  | [optional] [default to undefined]
**hasPendingTransfer** | **boolean** |  | [optional] [default to undefined]

## Example

```typescript
import { UserDataResponseModel } from './api';

const instance: UserDataResponseModel = {
    id,
    eln,
    displayName,
    iptuName,
    adAccount,
    schenkerId,
    blocked,
    dataBlocked,
    blockedByUser,
    hasPendingTransfer,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
