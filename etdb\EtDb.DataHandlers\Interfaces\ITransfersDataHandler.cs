﻿using ET.DataHandlers.Models;
using EtDb.ApiClients.EtDb.Models;
using EtDb.DataHandlers.Models;
using System.Linq.Expressions;


namespace EtDb.DataHandlers.Interfaces
{
    public interface ITransfersDataHandler
    {
        IQueryable<Transfers> GetAllTransfers();

        IQueryable<TransferListItems> GetAllTransferListItems();

        IDictionary<string, IEnumerable<TransferListItemWithDate>> GetAllTransferListItemsByOpFilteredBy(Expression<Func<Transfers, bool>> filter);

        int GetTransferIdByRequestNumber(string requestNumber);

        int GetNumberOfTransfersByOp(string opCode);

        void AddTransfer(Transfers transfer);

        void AddTransferListItems(params TransferListItems[] listItems);

        Task<FilteredDataModel<Transfers>> GetTransfersGridDataModel(string sortBy, string dir, int page, int pageDataSize, string query, IQueryable<Transfers> transfers);

        Transfers GetTransferById(int id);

        IQueryable<TransferListItems> GetTransferListItemsByTransferId(int transferId);

        void DeleteTransferListItem(int id);

        TransferListItems GetTransferListItemById(int itemId);

        void UpdateTransferListItem(TransferListItems listItem);

        void UpdateTransfer(Transfers transfer);
    }
}
