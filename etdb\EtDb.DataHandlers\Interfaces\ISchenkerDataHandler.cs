﻿using EtDb.ApiClients.EtDb.Models;
using EtDb.DataHandlers.Models;

namespace EtDb.DataHandlers.Interfaces
{
    public interface ISchenkerDataHandler
    {
        public IQueryable<Schenkers> GetAll();

        Task<FilteredDataModel<Schenkers>> GetFilteredSchenkers(string sortBy, string sortDir, int pageNumber, int pageSize, string query);

        Task<IQueryable<Schenkers>> GetSchenkersWithUsers();

        Task<Schenkers> GetSchenkerById(int id);

        Task<Schenkers> GetSchenkerById(int id, Func<IQueryable<Schenkers>, IQueryable<Schenkers>> includeProperties);

        Task<Schenkers> GetSchenkerById(int id, params string[] includeProperties);

        Task<int> InsertSchenker(SchenkerDto schenkerToInsert);

        Task UpdateSchenker(SchenkerDto schenkerToUpdate);

        Task<bool> IsUserMol(string userId);
    }
}
