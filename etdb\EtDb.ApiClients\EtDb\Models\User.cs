﻿using System;
using System.Collections.Generic;

namespace EtDb.ApiClients.EtDb.Models
{
    public partial class User
    {
        public User()
        {
            AvailableEquipment = new HashSet<AvailableEquipment>();
            ErrorUser = new HashSet<Error>();
            ErrorUser2 = new HashSet<Error>();
            HistoryFromUser = new HashSet<History>();
            HistoryToUser = new HashSet<History>();
            InverseBlockedByUser = new HashSet<User>();
            Notification = new HashSet<Notification>();
            RecipientHistoryFromUser = new HashSet<RecipientHistory>();
            RecipientHistoryToUser = new HashSet<RecipientHistory>();
            Schenkers = new HashSet<Schenkers>();
            StatusHistory = new HashSet<StatusHistory>();
            SubstitutionsForUser = new HashSet<Substitutions>();
            SubstitutionsSubstituteUser = new HashSet<Substitutions>();
        }

        public string Id { get; set; }
        public string Eln { get; set; }
        public string FirstName { get; set; }
        public string Surname { get; set; }
        public string FamilyName { get; set; }
        public string FullName { get; set; }
        public string DisplayName { get; set; }
        public string Iptuname { get; set; }
        public string Position { get; set; }
        public string Adaccount { get; set; }
        public string Email { get; set; }
        public string Opcode { get; set; }
        public int Blocked { get; set; }
        public DateTime? DataBlocked { get; set; }
        public string BlockedByUserId { get; set; }
        public byte ActiveFl { get; set; }
        public DateTime? DateInactive { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime? DeletedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public string ModifiedBy { get; set; }
        public int? CityId { get; set; }
        public int? SchenkerId { get; set; }
        public string ClientNumber { get; set; }
        public string PhoneNumber { get; set; }

        public virtual User BlockedByUser { get; set; }
        public virtual Cities City { get; set; }
        public virtual Schenkers Schenker { get; set; }
        public virtual ICollection<AvailableEquipment> AvailableEquipment { get; set; }
        public virtual ICollection<Error> ErrorUser { get; set; }
        public virtual ICollection<Error> ErrorUser2 { get; set; }
        public virtual ICollection<History> HistoryFromUser { get; set; }
        public virtual ICollection<History> HistoryToUser { get; set; }
        public virtual ICollection<User> InverseBlockedByUser { get; set; }
        public virtual ICollection<Notification> Notification { get; set; }
        public virtual ICollection<RecipientHistory> RecipientHistoryFromUser { get; set; }
        public virtual ICollection<RecipientHistory> RecipientHistoryToUser { get; set; }
        public virtual ICollection<Schenkers> Schenkers { get; set; }
        public virtual ICollection<StatusHistory> StatusHistory { get; set; }
        public virtual ICollection<Substitutions> SubstitutionsForUser { get; set; }
        public virtual ICollection<Substitutions> SubstitutionsSubstituteUser { get; set; }
    }
}
