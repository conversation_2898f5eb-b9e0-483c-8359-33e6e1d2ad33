{"root": ["../../src/app.tsx", "../../src/i18n.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/languageselector.tsx", "../../src/components/loginform.tsx", "../../src/components/navbar.tsx", "../../src/components/mode-toggle.tsx", "../../src/components/theme-provider.tsx", "../../src/components/administration/usereditdialog.tsx", "../../src/components/administration/usereditform.tsx", "../../src/components/administration/usertable.tsx", "../../src/components/auth/requireauth.tsx", "../../src/components/auth/roles.tsx", "../../src/components/context/authcontext.tsx", "../../src/components/correction/centralwarehouse.tsx", "../../src/components/correction/correction.tsx", "../../src/components/correction/op.tsx", "../../src/components/correction/remove.tsx", "../../src/components/correction/serialnumberinput.tsx", "../../src/components/correction/serviceid.tsx", "../../src/components/correction/technician.tsx", "../../src/components/correction/transfertoinput.tsx", "../../src/components/ui/tablefilter.tsx", "../../src/components/ui/tablepagination.tsx", "../../src/components/ui/accordion.tsx", "../../src/components/ui/alert.tsx", "../../src/components/ui/avatar.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/checkbox.tsx", "../../src/components/ui/command.tsx", "../../src/components/ui/dialog.tsx", "../../src/components/ui/drawer.tsx", "../../src/components/ui/dropdown-menu.tsx", "../../src/components/ui/error-state.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/label.tsx", "../../src/components/ui/loading-state.tsx", "../../src/components/ui/navigation-menu.tsx", "../../src/components/ui/pagination.tsx", "../../src/components/ui/popover.tsx", "../../src/components/ui/select.tsx", "../../src/components/ui/separator.tsx", "../../src/components/ui/sheet.tsx", "../../src/components/ui/table.tsx", "../../src/components/utils/navigation.ts", "../../src/components/utils/validations.ts", "../../src/data/query.ts", "../../src/data/etws/api.ts", "../../src/data/etws/base.ts", "../../src/data/etws/common.ts", "../../src/data/etws/configuration.ts", "../../src/data/etws/index.ts", "../../src/hooks/usecities.ts", "../../src/hooks/useschenkers.ts", "../../src/hooks/useusers.ts", "../../src/lang/bg.ts", "../../src/lang/en.ts", "../../src/lib/utils.ts", "../../src/pages/homepage.tsx", "../../src/pages/login.tsx", "../../src/pages/administration/userspage.tsx", "../../src/utils/axiosinterceptor.ts"], "errors": true, "version": "5.8.3"}