# QuantityCalculationObjectRequestModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**criticalQuantity** | **number** |  | [optional] [default to undefined]
**toggleToAutomaticGeneration** | **boolean** |  | [optional] [default to undefined]

## Example

```typescript
import { QuantityCalculationObjectRequestModel } from './api';

const instance: QuantityCalculationObjectRequestModel = {
    id,
    criticalQuantity,
    toggleToAutomaticGeneration,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
