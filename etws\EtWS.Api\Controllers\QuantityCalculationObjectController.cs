﻿namespace EtWS.Api.Controllers
{
    using EtWS.Api.Infrastructure.Utils;
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Services.CriticalQuantitiesService;

    public class QuantityCalculationObjectController : BaseApiController
    {
        private readonly Lazy<ICriticalQuantitiesService> criticalQuantitiesService;

        public QuantityCalculationObjectController(Lazy<ICriticalQuantitiesService> criticalQuantitiesService)
        {
            this.criticalQuantitiesService = criticalQuantitiesService;
        }

        [HttpPost("search")]
        [ProducesResponseType(typeof(QuantityCalculationObjectResponseModelSearchResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<QuantityCalculationObjectResponseModelSearchResponseModel>> SearchQuantityCalculationObjectsAsync([Required] SearchDataRequestModel request)
        {
            try
            {
                var result = await this.criticalQuantitiesService.Value.SearchQuantityCalculationObjectsAsync(request);

                return this.Ok(result);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("{id}")]
        [ProducesResponseType(typeof(QuantityCalculationObjectResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<QuantityCalculationObjectResponseModel>> GetByIdAsync(int id)
        {
            try
            {
                var result = await this.criticalQuantitiesService.Value.GetQuantityCalculationObjectByIdAsync(id);

                return this.Ok(result);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("update")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<BaseResponseModel>> UpdateQuantityCalculationObjectAsync(QuantityCalculationObjectRequestModel request)
        {
            try
            {
                await this.criticalQuantitiesService.Value.UpdateQuantityCalculationObjectAsync(request);

                return this.Ok(new BaseResponseModel { Success = true, Message = "Object has been successfully updated." });
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
