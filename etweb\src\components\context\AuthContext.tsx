import React, { createContext, useEffect, useReducer, type ReactNode } from "react";
import { useNavigate } from "react-router";
import { type UserLoginResponseModel } from "../../data/etws";
import { setupAxiosInterceptors } from "../../utils/axiosInterceptor";

interface State {
  user: UserLoginResponseModel | null;
  loading: boolean;
  error: string | null;
}

interface Action {
  type: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  payload?: any;
}

const INITIAL_STATE: State = {
  user: JSON.parse(localStorage.getItem("user") as string) || null,
  loading: false,
  error: null,
};

export const AuthContext = createContext<{
  user: UserLoginResponseModel | null;
  loading: boolean;
  error: string | null;
  dispatch: React.Dispatch<Action>;
}>({
  user: null,
  loading: false,
  error: null,
  dispatch: () => undefined,
});

const AuthReducer = (state: State, action: Action): State => {
  switch (action.type) {
    case "LOGIN_START":
      return {
        user: null,
        loading: true,
        error: null,
      };
    case "LOGIN_SUCCESS":
      return {
        user: action.payload,
        loading: false,
        error: null,
      };
    case "LOGIN_FAILURE":
      return {
        user: null,
        loading: false,
        error: action.payload,
      };
    case "LOGOUT":
      return {
        user: null,
        loading: false,
        error: null,
      };
    case "SESSION_EXPIRED":
      return {
        user: null,
        loading: false,
        error: "sessionExpired", // This will be translated in the component
      };
    default:
      return state;
  }
};

export const AuthContextProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(AuthReducer, INITIAL_STATE);
  const navigate = useNavigate();

  // Set up axios interceptors for handling expired sessions
  useEffect(() => {
    const cleanup = setupAxiosInterceptors(dispatch, navigate);
    return cleanup; // Cleanup interceptors when component unmounts
  }, [dispatch, navigate]);

  useEffect(() => {
    localStorage.setItem("user", JSON.stringify(state.user));
  }, [state.user]);

  return (
    <AuthContext.Provider
      value={{
        user: state.user,
        loading: state.loading,
        error: state.error,
        dispatch,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
