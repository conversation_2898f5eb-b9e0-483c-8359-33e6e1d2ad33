﻿namespace EtDb.DataHandlers
{
    using System.Linq;
    using EtDb.ApiClients.EtDb.Context;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using EtDb.Infrastructure.Enumerations;
    using Microsoft.EntityFrameworkCore;

    public class EquipmentTypeDataHandler : BaseDataHandler, IEquipmentTypeDataHandler
    {
        public EquipmentTypeDataHandler(Lazy<EtDbContext> dbContext)
            : base(dbContext)
        {
        }

        public async Task<EquipmentType> GetEquipmentTypeByIdAsync(int id)
        {
            var equipmentType = await this.dbContext.Value.EquipmentTypes.SingleOrDefaultAsync(et => et.Id == id);

            if (equipmentType == null)
            {
                throw new ArgumentException($"No Equipment Type with id: {id} was found.");
            }

            return equipmentType;
        }

        public IQueryable<EquipmentType> GetEquipmentTypes()
        {
            IOrderedQueryable<EquipmentType> equipmentTypes = this.dbContext.Value
                .EquipmentTypes
                .Include(e => e.EquipmentGroup)
                .OrderBy(t => t.Name);

            return equipmentTypes;
        }

        public async Task<int> GetEquipmentTypeIdAsync(string sapMaterialNum)
        {
            return await this.GetEquipmentTypes()
                .Where(e => e.SapmaterialNum == sapMaterialNum)
                .Select(e => e.Id)
                .SingleOrDefaultAsync();
        }

        // TODO: Delete if not needed
        //public IQueryable<EquipmentType> GetEquipmentTypesInGroup(string equipmentGroupName)
        //{
        //    return this.GetEquipmentTypes()
        //        .Where(e => e.EquipmentGroup.Name == equipmentGroupName);
        //}

        //public void AttachEquipmentType(EquipmentType equipmentType)
        //{
        //    this.dbContext.Value.EquipmentTypes.Attach(equipmentType);
        //}

        public async Task<IEnumerable<EquipmentType>> GetEquipmentTypesAsync(SendMethod sendMethod)
        {
            return await this.dbContext.Value
                .EquipmentTypes
                .Where(e => e.SendMethod == (int)sendMethod)
                .ToListAsync();
        }

        public IEnumerable<EquipmentTypeConciseDto> GetConciseEquipmentTypes(IEnumerable<int> equipmentTypeIds, bool serialNumbersRequired = true)
        {
            IQueryable<EquipmentType> equipmentTypesQuery = this.dbContext.Value.EquipmentTypes;

            if (serialNumbersRequired)
            {
                equipmentTypesQuery = equipmentTypesQuery.Where(e => e.SerialNumberRequired == 1);
            }

            equipmentTypesQuery = equipmentTypesQuery.Where(e => equipmentTypeIds.Contains(e.Id))
                .OrderBy(e => e.EquipmentGroupId)
                .ThenBy(e => e.SapmaterialNum);

            return equipmentTypesQuery.Select(e => new EquipmentTypeConciseDto
            {
                Id = e.Id,
                Name = e.Name,
                SAPMaterialNum = e.SapmaterialNum
            });
        }

        public async Task<IDictionary<string, int>> GetEquipmentTypesLastMonthQuantitiesAsync()
        {
            return await this.dbContext.Value.EquipmentTypes
            .Where(e => e.SerialNumberRequired == 1)
            .ToDictionaryAsync(e => e.Id.ToString(), e => e.LastMonthlyOrderQuantity ?? 0);
        }
    }
}
