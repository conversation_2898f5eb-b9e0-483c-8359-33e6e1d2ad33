﻿namespace EtDb.Services
{
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using EtDb.Models.Requests;
    using EtDb.Models.Responses;
    using EtDb.Services.Interfaces;

    public class ProductService : IProductService
    {
        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<IProductDataHandler> productDataHandler;

        public ProductService(Lazy<IProductDataHandler> productDataHandler, Lazy<IMapper> mapper)
        {
            this.productDataHandler = productDataHandler;
            this.mapper = mapper;
        }

        public async Task<AdministrateProductResponseModel> GetProductById(int id)
        {
            var data = await this.productDataHandler.Value.GetProductById(id);

            return this.mapper.Value.Map<AdministrateProductDto, AdministrateProductResponseModel>(data);
        }

        public async Task<SearchResponseModel<AdministrateProductResponseModel>> SearchProducts(SearchRequestModel request)
        {
            var data = await this.productDataHandler.Value.GetFilteredProducts(
                request.SortBy, request.SortDir, request.PageNumber, request.PageSize, request.Query);

            return this.mapper.Value.Map<FilteredDataModel<AdministrateProductDto>, SearchResponseModel<AdministrateProductResponseModel>>(data);
        }

        public async Task<int> InsertProduct(AdministrateProductRequestModel request)
        {
            var data = this.mapper.Value.Map<AdministrateProductRequestModel, AdministrateProductDto>(request);
            return await this.productDataHandler.Value.InsertProduct(data);
        }

        public async Task UpdateProduct(AdministrateProductRequestModel request)
        {
            var data = this.mapper.Value.Map<AdministrateProductRequestModel, AdministrateProductDto>(request);
            await this.productDataHandler.Value.UpdateProduct(data);
        }
    }
}
