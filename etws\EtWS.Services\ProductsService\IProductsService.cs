﻿namespace EtWS.Services.ProductsService
{
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.UserModels;

    public interface IProductsService : IService
    {
        Task<SearchProductDataResponseModel> GetProductByIdAsync(int id);

        Task<SearchResponseModel<SearchProductDataResponseModel>> GetProductsAsync(SearchDataRequestModel request);

        Task<int> AddProductAsync(ProductDataRequestModel request);

        Task UpdateProductAsync(ProductDataRequestModel request);
    }
}
