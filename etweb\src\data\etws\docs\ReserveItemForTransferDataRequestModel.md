# ReserveItemForTransferDataRequestModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**itemId** | **number** |  | [optional] [default to undefined]
**itemSerialNumber** | **string** |  | [optional] [default to undefined]
**quantity** | **number** |  | [optional] [default to undefined]

## Example

```typescript
import { ReserveItemForTransferDataRequestModel } from './api';

const instance: ReserveItemForTransferDataRequestModel = {
    itemId,
    itemSerialNumber,
    quantity,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
