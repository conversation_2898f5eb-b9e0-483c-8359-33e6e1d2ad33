import { SchenkersApi } from "@/data/etws";
import { useQuery } from "@tanstack/react-query";

const shenkersApi = new SchenkersApi(undefined, "/et-ws", undefined);

export const useSchenkersSelectList = () => {
  return useQuery({
    queryKey: ["schenkers", "select-list"],
    queryFn: async () => {
      const response = await shenkersApi.apiSchenkersSelectListGet();
      return response.data;
    },
  });
};
