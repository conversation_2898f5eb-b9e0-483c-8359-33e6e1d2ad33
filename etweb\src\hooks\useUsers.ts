import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { 
  UsersApi, 
  type EditUserRequest, 
  type SearchDataRequestModel, 
  type SearchUserDataResponseModel, 
  type SearchUserDataResponseModelSearchResponseModel,
} from "@/data/etws";

const usersApi = new UsersApi(undefined, "/et-ws", undefined);

export const useUsersSearch = (searchParams: SearchDataRequestModel) => {
  return useQuery({
    queryKey: ["users", "search", searchParams],
    queryFn: async ({ queryKey }) => {
      const searchParams = queryKey[2] as SearchDataRequestModel;
      const response = await usersApi.apiUsersSearchPost(searchParams);
      return response.data as SearchUserDataResponseModelSearchResponseModel;
    },
    enabled: true,
  });
};

export const useUserById = (userId: string) => {
  return useQuery({
    queryKey: ["users", userId],
    queryFn: async () => {
      const response = await usersApi.apiUsersByUserIdGet(userId);
      return response.data as SearchUserDataResponseModel;
    },
    enabled: !!userId,
  });
};

export const useUpdateUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (userData:EditUserRequest) => {
      const response = await usersApi.apiUsersPut(userData);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch users data
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
    onError: (error) => {
      console.error("Error updating user:", error);
    },
  });
};

export const useActivateUsers = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (userIds: string[]) => {
      const response = await usersApi.apiUsersActivateUsersPut(userIds);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
    onError: (error) => {
      console.error("Error activating users:", error);
    },
  });
};

export const useBlockUsers = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (userIds: string[]) => {
      const response = await usersApi.apiUsersBlockUsersPut(userIds);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
    onError: (error) => {
      console.error("Error blocking users:", error);
    },
  });
};

export const useUsersSelectList = () => {
  return useQuery({
    queryKey: ["users", "select-list"],
    queryFn: async () => {
      const response = await usersApi.apiUsersUsersSelectListGet();
      return response.data;
    },
  });
};
