﻿{
  "ProviderId": "Unchase.OpenAPI.ConnectedService",
  "Version": "********",
  "GettingStartedDocument": {
    "Uri": "https://github.com/unchase/Unchase.OpenAPI.Connectedservice/"
  },
  "ExtendedData": {
    "ServiceName": "MpssWs",
    "AcceptAllUntrustedCertificates": false,
    "GeneratedFileName": "MpssWsClient",
    "Endpoint": "http://microdevapp1.drcenter.btk.bg:31655/mpss-ws/swagger/v1/swagger.json",
    "GeneratedFileNamePrefix": null,
    "GenerateCSharpClient": true,
    "GenerateTypeScriptClient": false,
    "GenerateCSharpController": false,
    "OpenApiToCSharpClientCommand": {
      "ClientBaseClass": "BaseApiClient",
      "ConfigurationClass": "IApiClientConfiguration",
      "GenerateClientClasses": true,
      "GenerateClientInterfaces": true,
      "ClientBaseInterface": null,
      "InjectHttpClient": true,
      "DisposeHttpClient": true,
      "ProtectedMethods": [],
      "GenerateExceptionClasses": false,
      "ExceptionClass": "ApiException",
      "WrapDtoExceptions": false,
      "UseHttpClientCreationMethod": false,
      "HttpClientType": "System.Net.Http.HttpClient",
      "UseHttpRequestMessageCreationMethod": true,
      "UseBaseUrl": false,
      "GenerateBaseUrlProperty": true,
      "GenerateSyncMethods": false,
      "GeneratePrepareRequestAndProcessResponseAsAsyncMethods": false,
      "ExposeJsonSerializerSettings": true,
      "ClientClassAccessModifier": "public",
      "TypeAccessModifier": "public",
      "GenerateContractsOutput": false,
      "ContractsNamespace": null,
      "ContractsOutputFilePath": null,
      "ParameterDateTimeFormat": "s",
      "ParameterDateFormat": "yyyy-MM-dd",
      "GenerateUpdateJsonSerializerSettingsMethod": true,
      "UseRequestAndResponseSerializationSettings": false,
      "SerializeTypeInformation": false,
      "QueryNullValue": "",
      "ClassName": "MpssWs",
      "OperationGenerationMode": 4,
      "AdditionalNamespaceUsages": [
        "Bss.Standard.Infrastructure.NSwag"
      ],
      "AdditionalContractNamespaceUsages": [],
      "GenerateOptionalParameters": false,
      "GenerateJsonMethods": false,
      "EnforceFlagEnums": false,
      "ParameterArrayType": "System.Collections.Generic.IEnumerable",
      "ParameterDictionaryType": "System.Collections.Generic.IDictionary",
      "ResponseArrayType": "System.Collections.Generic.ICollection",
      "ResponseDictionaryType": "System.Collections.Generic.IDictionary",
      "WrapResponses": false,
      "WrapResponseMethods": [],
      "GenerateResponseClasses": true,
      "ResponseClass": "SwaggerResponse",
      "Namespace": "EtDb.ApiClients.MpssWs",
      "RequiredPropertiesMustBeDefined": true,
      "DateType": "System.DateTime",
      "JsonConverters": null,
      "AnyType": "object",
      "DateTimeType": "System.DateTime",
      "TimeType": "System.TimeSpan",
      "TimeSpanType": "System.TimeSpan",
      "ArrayType": "System.Collections.Generic.ICollection",
      "ArrayInstanceType": "System.Collections.ObjectModel.Collection",
      "DictionaryType": "System.Collections.Generic.IDictionary",
      "DictionaryInstanceType": "System.Collections.Generic.Dictionary",
      "ArrayBaseType": "System.Collections.ObjectModel.Collection",
      "DictionaryBaseType": "System.Collections.Generic.Dictionary",
      "ClassStyle": 0,
      "JsonLibrary": 0,
      "GenerateDefaultValues": true,
      "GenerateDataAnnotations": true,
      "ExcludedTypeNames": [],
      "ExcludedParameterNames": [],
      "HandleReferences": false,
      "GenerateImmutableArrayProperties": false,
      "GenerateImmutableDictionaryProperties": false,
      "JsonSerializerSettingsTransformationMethod": null,
      "InlineNamedArrays": false,
      "InlineNamedDictionaries": false,
      "InlineNamedTuples": true,
      "InlineNamedAny": false,
      "GenerateDtoTypes": true,
      "GenerateOptionalPropertiesAsNullable": false,
      "GenerateNullableReferenceTypes": false,
      "TemplateDirectory": null,
      "TypeNameGeneratorType": null,
      "PropertyNameGeneratorType": null,
      "EnumNameGeneratorType": null,
      "ChecksumCacheEnabled": false,
      "ServiceHost": null,
      "ServiceSchemes": null,
      "output": "MpssWsClient.cs",
      "newLineBehavior": 0
    },
    "ExcludeTypeNamesLater": false,
    "OpenApiToTypeScriptClientCommand": null,
    "OpenApiToCSharpControllerCommand": null,
    "Variables": null,
    "Runtime": 0,
    "CopySpecification": false,
    "OpenGeneratedFilesOnComplete": false,
    "UseRelativePath": false,
    "ConvertFromOdata": false,
    "OpenApiConvertSettings": {
      "ServiceRoot": "http://localhost",
      "Version": {
        "Major": 1,
        "Minor": 0,
        "Build": 1,
        "Revision": -1,
        "MajorRevision": -1,
        "MinorRevision": -1
      },
      "EnableKeyAsSegment": null,
      "EnableUnqualifiedCall": false,
      "EnableOperationPath": true,
      "EnableOperationImportPath": true,
      "EnableNavigationPropertyPath": true,
      "TagDepth": 4,
      "PrefixEntityTypeNameBeforeKey": false,
      "OpenApiSpecVersion": 1,
      "EnableOperationId": true,
      "EnableUriEscapeFunctionCall": false,
      "VerifyEdmModel": false,
      "IEEE754Compatible": false,
      "TopExample": 50,
      "EnablePagination": false,
      "PageableOperationName": "listMore",
      "EnableDiscriminatorValue": false,
      "EnableDerivedTypesReferencesForResponses": false,
      "EnableDerivedTypesReferencesForRequestBody": false,
      "PathPrefix": "OData",
      "RoutePathPrefixProvider": {
        "PathPrefix": "OData",
        "Parameters": null
      },
      "ShowLinks": false,
      "ShowSchemaExamples": false,
      "RequireDerivedTypesConstraintForBoundOperations": false,
      "ShowRootPath": false,
      "ShowMsDosGroupPath": true,
      "PathProvider": null
    },
    "OpenApiSpecVersion": 0,
    "UseNetworkCredentials": false,
    "NetworkCredentialsUserName": null,
    "NetworkCredentialsPassword": null,
    "NetworkCredentialsDomain": null,
    "WebProxyUri": null,
    "UseWebProxy": false,
    "UseWebProxyCredentials": false,
    "WebProxyNetworkCredentialsUserName": null,
    "WebProxyNetworkCredentialsPassword": null,
    "WebProxyNetworkCredentialsDomain": null
  }
}