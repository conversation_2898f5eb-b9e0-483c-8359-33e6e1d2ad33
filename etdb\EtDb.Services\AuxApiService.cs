﻿namespace EtDb.Services
{
    using System.Threading.Tasks;

    using EtDb.ApiClients.EtDb.Context;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.Models.AuxModels;
    using EtDb.Models.Requests;
    using EtDb.Services.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class AuxApiService : IAuxApiService
    {
        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<EtDbContext> dbContext;

        public AuxApiService(Lazy<EtDbContext> dbContext, Lazy<IMapper> mapper)
        {
            this.dbContext = dbContext;
            this.mapper = mapper;
        }

        public async Task<int> InsertItemAsync(InsertItemRequestModel request)
        {
            var item = this.mapper.Value.Map<Item>(request);
            await this.dbContext.Value.Items.AddAsync(item);
            await this.dbContext.Value.SaveChangesAsync();

            return item.Id;
        }

        public async Task UpdateItemAsync(int equipmentId, UpdateItemRequestModel request)
        {
            var itemToUpdate = await this.dbContext.Value.Items
                .SingleOrDefaultAsync(item => item.Id == equipmentId);

            if (itemToUpdate != null)
            {
                itemToUpdate.AdditionDate = DateTime.UtcNow;
                itemToUpdate.TypeOfUsage = request.TypeOfUsage;
                itemToUpdate.EquipmentValidity = request.EquipmentValidity;
                itemToUpdate.EquipmentName = request.EquipmentName;
                itemToUpdate.SapmaterialNum = request.SapmaterialNum;
                itemToUpdate.EquipmentMaterialGroup = request.EquipmentMaterialGroup;
                await this.dbContext.Value.SaveChangesAsync();
            }
            else
            {
                throw new ArgumentException($"No Item found for the provided Id: {equipmentId}.");
            }
        }

        public async Task<int> InsertHistoryAsync(InsertHistoryRequestModel request)
        {
            var history = this.mapper.Value.Map<History>(request);
            await this.dbContext.Value.Histories.AddAsync(history);
            await this.dbContext.Value.SaveChangesAsync();

            return history.Id;
        }

        public async Task UpdateHistoryAsync(int equipmentId)
        {
            var historiesToUpdate = await this.dbContext.Value.Histories
                .Where(h => h.ItemId == equipmentId && GlobalConstants.DocStatusesList.Contains(h.DocStatus)).ToListAsync();

            if (historiesToUpdate != null && historiesToUpdate.Any())
            {
                historiesToUpdate.ForEach(h => h.DocStatus = GlobalConstants.DocStatusProvidedToClient);
                await this.dbContext.Value.SaveChangesAsync();
            }
            else
            {
                throw new ArgumentException($"No Histories found for the provided equipment Id: {equipmentId}.");
            }
        }

        public async Task<int> InsertAvailableEquipmentAsync(InsertAvailableEquipmentRequestModel request)
        {
            var availableEquipment = this.mapper.Value.Map<AvailableEquipment>(request);
            await this.dbContext.Value.AvailableEquipments.AddAsync(availableEquipment);
            await this.dbContext.Value.SaveChangesAsync();

            return availableEquipment.Id;
        }

        public async Task UpdateAvailableEquipmentAsync(int equipmentId, UpdateAvailableEquipmentRequestModel request)
        {
            var equipmentsToUpdate = await this.dbContext.Value.AvailableEquipments
                .Where(e => e.ItemId == equipmentId && e.UserId == request.UserId).ToListAsync();

            if (equipmentsToUpdate != null && equipmentsToUpdate.Any())
            {
                equipmentsToUpdate.ForEach(e => e.ItemQuantity += request.DeliveryItemQty);
                await this.dbContext.Value.SaveChangesAsync();
            }
            else
            {
                throw new ArgumentException($"No Available Equipments found for the provided equipment Id: {equipmentId} and user Id: {request.UserId}.");
            }
        }

        public async Task InsertStatusHistoryAsync(int equipmentId)
        {
            var historiesToInsert = await this.dbContext.Value.Histories
                .Where(h => h.ItemId == equipmentId && GlobalConstants.DocStatusesList.Contains(h.DocStatus)).ToListAsync();

            if (historiesToInsert != null && historiesToInsert.Any())
            {
                foreach (var history in historiesToInsert)
                {
                    var statusHistoryAux = this.mapper.Value.Map<StatusHistoryAux>(history);
                    var statusHistory = this.mapper.Value.Map<StatusHistory>(statusHistoryAux);
                    await this.dbContext.Value.StatusHistories.AddAsync(statusHistory);
                }

                await this.dbContext.Value.SaveChangesAsync();
            }
            else
            {
                throw new ArgumentException($"No History found for the provided equipment Id: {equipmentId}.");
            }
        }

        public async Task<int> InsertStatusHistoryAsync(InsertStatusHistoryRequestModel request)
        {
            var statusHistory = this.mapper.Value.Map<StatusHistory>(request);
            await this.dbContext.Value.StatusHistories.AddAsync(statusHistory);
            await this.dbContext.Value.SaveChangesAsync();

            return statusHistory.Id;
        }

        public async Task<int> InsertNotificationAsync(string userId)
        {
            var newNotification = new Notification { NotificationType = 1, UserId = userId, IsRead = false };
            await this.dbContext.Value.Notifications.AddAsync(newNotification);
            await this.dbContext.Value.SaveChangesAsync();

            return newNotification.Id;
        }

        public async Task UpdateNotificationAsync(string userId)
        {
            var notificationsToUpdate = await this.dbContext.Value.Notifications
                .Where(n => n.UserId == userId && n.NotificationType == 1).ToListAsync();

            if (notificationsToUpdate != null && notificationsToUpdate.Any())
            {
                notificationsToUpdate.ForEach(n => n.IsRead = false);
                await this.dbContext.Value.SaveChangesAsync();
            }
            else
            {
                throw new ArgumentException($"No Notifications found for the provided user Id: {userId}.");
            }
        }
    }
}
