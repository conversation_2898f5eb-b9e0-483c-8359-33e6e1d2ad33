﻿namespace EtDb.DataHandlers
{
    using EtDb.ApiClients.EtDb.Context;
    using EtDb.DataHandlers.Extensions;
    using EtDb.DataHandlers.Models;
    using EtDb.Infrastructure.Constants;
    using System.Linq;

    public abstract class BaseDataHandler
    {
        protected BaseDataHandler(Lazy<EtDbContext> dbContext)
        {
            this.dbContext = dbContext;
        }

        protected readonly Lazy<EtDbContext> dbContext;

        protected async Task<FilteredDataModel<T>> GetFilteredData<T>(IQueryable<T> collection, string sortBy, string sortDir, int pageNumber, int pageSize, string query)
            where T : class
        {
            pageNumber = pageNumber <= 0 ? FilterConstants.DefaultPageNumber : pageNumber;

            collection = collection.FilterBy(query);
            collection = collection.OrderBy(sortBy, sortDir).AsQueryable();

            var allDataRowsCount = collection.Count();
            pageSize = pageSize <= 0 ? allDataRowsCount : pageSize;
            collection = collection.Skip((pageNumber - 1) * pageSize).Take(pageSize);

            return await Task.FromResult(new FilteredDataModel<T>()
            {
                DataRows = collection,
                AllDataRowsCount = allDataRowsCount
            });
        }
    }
}
