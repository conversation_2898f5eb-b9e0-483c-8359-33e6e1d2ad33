// User roles constants matching the backend UserRoles class
export enum UserRoles {
  Admin = "Admin",
  Mol = "Mol",
  Finance = "Finance",
  Manager = "Manager",
  Technician = "Technician",
  ServiceManager = "ServiceManager",
  InventoryController = "InventoryController",
  ImplementationController = "ImplementationController",
  Subcontractor = "Subcontractor",
  User = "User",
}

// Helper constants for role groups (matching backend)
export const ApprovalRoles = [
  UserRoles.ServiceManager,
  UserRoles.InventoryController,
  UserRoles.ImplementationController,
];

export const AdministrationAuthorizationRoles = [
  UserRoles.Manager,
  UserRoles.ServiceManager,
];
