﻿{
  "openapi": "3.0.1",
  "info": {
    "title": "et-db",
    "version": "v1"
  },
  "servers": [
    {
      "url": "http://microit9app2.drcenter.btk.bg:25897/et-db"
    }
  ],
  "paths": {
    "/api/aux-api/insert-item": {
      "post": {
        "tags": [
          "AuxApi"
        ],
        "operationId": "ApiAuxApiInsertItemPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/InsertItemRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/InsertItemRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/InsertItemRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          }
        }
      }
    },
    "/api/aux-api/update-item": {
      "put": {
        "tags": [
          "AuxApi"
        ],
        "operationId": "ApiAuxApiUpdateItemPut",
        "parameters": [
          {
            "name": "equipmentId",
            "in": "query",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/UpdateItemRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/UpdateItemRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/UpdateItemRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/aux-api/insert-history": {
      "post": {
        "tags": [
          "AuxApi"
        ],
        "operationId": "ApiAuxApiInsertHistoryPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/InsertHistoryRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/InsertHistoryRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/InsertHistoryRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          }
        }
      }
    },
    "/api/aux-api/update-history": {
      "put": {
        "tags": [
          "AuxApi"
        ],
        "operationId": "ApiAuxApiUpdateHistoryPut",
        "parameters": [
          {
            "name": "equipmentId",
            "in": "query",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/aux-api/insert-available-equipment": {
      "post": {
        "tags": [
          "AuxApi"
        ],
        "operationId": "ApiAuxApiInsertAvailableEquipmentPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/InsertAvailableEquipmentRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/InsertAvailableEquipmentRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/InsertAvailableEquipmentRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          }
        }
      }
    },
    "/api/aux-api/update-available-equipment": {
      "put": {
        "tags": [
          "AuxApi"
        ],
        "operationId": "ApiAuxApiUpdateAvailableEquipmentPut",
        "parameters": [
          {
            "name": "equipmentId",
            "in": "query",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/UpdateAvailableEquipmentRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/UpdateAvailableEquipmentRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/UpdateAvailableEquipmentRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/aux-api/insert-status-history-by-equipmentId": {
      "post": {
        "tags": [
          "AuxApi"
        ],
        "operationId": "ApiAuxApiInsertStatusHistoryByEquipmentIdPost",
        "parameters": [
          {
            "name": "equipmentId",
            "in": "query",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/aux-api/insert-status-history": {
      "post": {
        "tags": [
          "AuxApi"
        ],
        "operationId": "ApiAuxApiInsertStatusHistoryPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/InsertStatusHistoryRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/InsertStatusHistoryRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/InsertStatusHistoryRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          }
        }
      }
    },
    "/api/aux-api/insert-notification": {
      "post": {
        "tags": [
          "AuxApi"
        ],
        "operationId": "ApiAuxApiInsertNotificationPost",
        "parameters": [
          {
            "name": "userId",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          }
        }
      }
    },
    "/api/aux-api/update-notification": {
      "put": {
        "tags": [
          "AuxApi"
        ],
        "operationId": "ApiAuxApiUpdateNotificationPut",
        "parameters": [
          {
            "name": "userId",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/cities": {
      "get": {
        "tags": [
          "Cities"
        ],
        "operationId": "ApiCitiesGet",
        "parameters": [
          {
            "name": "id",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CitiesResponseModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/cities/search": {
      "post": {
        "tags": [
          "Cities"
        ],
        "operationId": "ApiCitiesSearchPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CitiesResponseModelSearchResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/cities/add": {
      "post": {
        "tags": [
          "Cities"
        ],
        "operationId": "ApiCitiesAddPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/CitiesRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/CitiesRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/CitiesRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          }
        }
      }
    },
    "/api/cities/update": {
      "put": {
        "tags": [
          "Cities"
        ],
        "operationId": "ApiCitiesUpdatePut",
        "parameters": [
          {
            "name": "id",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/CitiesRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/CitiesRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/CitiesRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/cities/block": {
      "put": {
        "tags": [
          "Cities"
        ],
        "operationId": "ApiCitiesBlockPut",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            },
            "text/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            },
            "application/*+json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/cities/activate": {
      "put": {
        "tags": [
          "Cities"
        ],
        "operationId": "ApiCitiesActivatePut",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            },
            "text/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            },
            "application/*+json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/cities/region": {
      "get": {
        "tags": [
          "Cities"
        ],
        "operationId": "ApiCitiesRegionGet",
        "parameters": [
          {
            "name": "cityId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "string"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/sapmat-mapp-get": {
      "get": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentSapmatMappGetGet",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/SapmatMapp"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/search-available-equipment": {
      "post": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentSearchAvailableEquipmentPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AvailableEquipmentResponseModelSearchResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/user-reserved-items/{userId}": {
      "get": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentUserReservedItemsByUserIdGet",
        "parameters": [
          {
            "name": "userId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/AvailableEquipmentResponseModel"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/reserve-item": {
      "post": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentReserveItemPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/ReserveItemForTransferRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/ReserveItemForTransferRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/ReserveItemForTransferRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ReserveItemHistoryResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/reserve-all-items": {
      "post": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentReserveAllItemsPost",
        "parameters": [
          {
            "name": "userId",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/reserved-items-count/{userId}": {
      "get": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentReservedItemsCountByUserIdGet",
        "parameters": [
          {
            "name": "userId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/remove-item": {
      "post": {
        "tags": [
          "Equipment"
        ],
        "summary": "Removes item from transfer.",
        "operationId": "ApiEquipmentRemoveItemPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "description": "The item id and the user id.",
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/RemoveItemForTransferRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/RemoveItemForTransferRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/RemoveItemForTransferRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "string"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/remove-items": {
      "post": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentRemoveItemsPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/RemoveItemsForTransferRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/RemoveItemsForTransferRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/RemoveItemsForTransferRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/remove-all-items": {
      "post": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentRemoveAllItemsPost",
        "parameters": [
          {
            "name": "userId",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/available-equipments": {
      "put": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentAvailableEquipmentsPut",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/AvailableEquipmentRequestModel"
                }
              }
            },
            "text/json": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/AvailableEquipmentRequestModel"
                }
              }
            },
            "application/*+json": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/AvailableEquipmentRequestModel"
                }
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/search-transfer-data": {
      "post": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentSearchTransferDataPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ItemResponseModelSearchResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/search-daily-equipment": {
      "post": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentSearchDailyEquipmentPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AvailableEquipmentResponseModelSearchResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/deliver-items": {
      "post": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentDeliverItemsPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/DeliverItemsRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/DeliverItemsRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/DeliverItemsRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/post-offices-select-list": {
      "get": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentPostOfficesSelectListGet",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/PostOfficesSelectListResponseModel"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/search-user-items-to-accept": {
      "post": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentSearchUserItemsToAcceptPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UserItemsToAcceptResponseModelSearchResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/search-user-items-to-cancel": {
      "post": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentSearchUserItemsToCancelPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModelWithUserId"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UserItemsToCancelResponseModelSearchResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/refuse-items": {
      "post": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentRefuseItemsPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/RefuseItemsRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/RefuseItemsRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/RefuseItemsRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "$ref": "#/components/schemas/TransferedItemsModel"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/accept-items": {
      "post": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentAcceptItemsPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AcceptItemsRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AcceptItemsRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AcceptItemsRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "$ref": "#/components/schemas/TransferedItemsModel"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment/cancel-items": {
      "post": {
        "tags": [
          "Equipment"
        ],
        "operationId": "ApiEquipmentCancelItemsPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/CancelItemsRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/CancelItemsRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/CancelItemsRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "$ref": "#/components/schemas/TransferedItemsModel"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment-group/all": {
      "get": {
        "tags": [
          "EquipmentGroup"
        ],
        "operationId": "ApiEquipmentGroupAllGet",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/EquipmentGroupResponseModel"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment-group/all/names": {
      "get": {
        "tags": [
          "EquipmentGroup"
        ],
        "operationId": "ApiEquipmentGroupAllNamesGet",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "type": "string"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment-group/name/{sapMaterialNum}": {
      "get": {
        "tags": [
          "EquipmentGroup"
        ],
        "operationId": "ApiEquipmentGroupNameBySapMaterialNumGet",
        "parameters": [
          {
            "name": "sapMaterialNum",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "string"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment-group/by-id/{id}": {
      "get": {
        "tags": [
          "EquipmentGroup"
        ],
        "operationId": "ApiEquipmentGroupByIdByIdGet",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/EquipmentGroupResponseModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment-type/all": {
      "get": {
        "tags": [
          "EquipmentType"
        ],
        "operationId": "ApiEquipmentTypeAllGet",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/EquipmentTypeResponseModel"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment-type/by-id/{id}": {
      "get": {
        "tags": [
          "EquipmentType"
        ],
        "operationId": "ApiEquipmentTypeByIdByIdGet",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/EquipmentTypeResponseModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment-type/type-id-by-sap-material-num/{sapMaterialNum}": {
      "get": {
        "tags": [
          "EquipmentType"
        ],
        "operationId": "ApiEquipmentTypeTypeIdBySapMaterialNumBySapMaterialNumGet",
        "parameters": [
          {
            "name": "sapMaterialNum",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment-type/by-send-method/{sendMethod}": {
      "get": {
        "tags": [
          "EquipmentType"
        ],
        "operationId": "ApiEquipmentTypeBySendMethodBySendMethodGet",
        "parameters": [
          {
            "name": "sendMethod",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/EquipmentTypeResponseModel"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment-type/concise-equipment-types": {
      "post": {
        "tags": [
          "EquipmentType"
        ],
        "operationId": "ApiEquipmentTypeConciseEquipmentTypesPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/ConciseEquipmentTypesRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/ConciseEquipmentTypesRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/ConciseEquipmentTypesRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/EquipmentTypeConciseDto"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/equipment-type/last-month-quantities": {
      "get": {
        "tags": [
          "EquipmentType"
        ],
        "operationId": "ApiEquipmentTypeLastMonthQuantitiesGet",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "integer",
                    "format": "int32"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/history/block-user-transfers": {
      "post": {
        "tags": [
          "History"
        ],
        "operationId": "ApiHistoryBlockUserTransfersPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            },
            "text/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            },
            "application/*+json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/history/cancel-expired-transfers": {
      "post": {
        "tags": [
          "History"
        ],
        "operationId": "ApiHistoryCancelExpiredTransfersPost",
        "parameters": [
          {
            "name": "cancellationDays",
            "in": "query",
            "schema": {
              "type": "number",
              "format": "double"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "$ref": "#/components/schemas/TransferedItemsModel"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/history/reserved-items": {
      "get": {
        "tags": [
          "History"
        ],
        "operationId": "ApiHistoryReservedItemsGet",
        "parameters": [
          {
            "name": "userId",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/History"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/get-itemId": {
      "post": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentGetItemIdPost",
        "parameters": [
          {
            "name": "equipmentSerialNumber",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/update-items-addition-dates": {
      "put": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentUpdateItemsAdditionDatesPut",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "integer",
                "format": "int32"
              }
            },
            "text/json": {
              "schema": {
                "type": "integer",
                "format": "int32"
              }
            },
            "application/*+json": {
              "schema": {
                "type": "integer",
                "format": "int32"
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/get-histories-by-itemId/{itemId}": {
      "get": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentGetHistoriesByItemIdByItemIdGet",
        "parameters": [
          {
            "name": "itemId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/History"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/add-history": {
      "post": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentAddHistoryPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/HistoryRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/HistoryRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/HistoryRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/update-available-equipment-quantity": {
      "put": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentUpdateAvailableEquipmentQuantityPut",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AvailableEquipmentRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AvailableEquipmentRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AvailableEquipmentRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/add-status-history": {
      "post": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentAddStatusHistoryPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/StatusHistoryRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/StatusHistoryRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/StatusHistoryRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/delete-history/{id}": {
      "delete": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentDeleteHistoryByIdDelete",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/delete-status-histories/{historyId}": {
      "delete": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentDeleteStatusHistoriesByHistoryIdDelete",
        "parameters": [
          {
            "name": "historyId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/delete-errors-by-history/{historyId}": {
      "delete": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentDeleteErrorsByHistoryByHistoryIdDelete",
        "parameters": [
          {
            "name": "historyId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/get-technician-id": {
      "post": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentGetTechnicianIdPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/UserRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/UserRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/UserRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "string"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/get-all-users-with-opcode": {
      "get": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentGetAllUsersWithOpcodeGet",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/UsersWithOpCodeResponseModel"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/get-available-equipment-id": {
      "post": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentGetAvailableEquipmentIdPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AvailableEquipmentRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AvailableEquipmentRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AvailableEquipmentRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/add-available-equipment": {
      "post": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentAddAvailableEquipmentPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AvailableEquipmentRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AvailableEquipmentRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AvailableEquipmentRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "201": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/update-available-equipment-to-zero/{itemId}": {
      "put": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentUpdateAvailableEquipmentToZeroByItemIdPut",
        "parameters": [
          {
            "name": "itemId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/check-serial-number/{equipmentSerialNum}": {
      "get": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet",
        "parameters": [
          {
            "name": "equipmentSerialNum",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/incorrect-equipment/user-and-iptu-names": {
      "get": {
        "tags": [
          "IncorrectEquipment"
        ],
        "operationId": "ApiIncorrectEquipmentUserAndIptuNamesGet",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/UserResponseModel"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/product": {
      "get": {
        "tags": [
          "Product"
        ],
        "operationId": "ApiProductGet",
        "parameters": [
          {
            "name": "id",
            "in": "query",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AdministrateProductResponseModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/product/search": {
      "post": {
        "tags": [
          "Product"
        ],
        "operationId": "ApiProductSearchPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AdministrateProductResponseModelSearchResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/product/insert": {
      "post": {
        "tags": [
          "Product"
        ],
        "operationId": "ApiProductInsertPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AdministrateProductRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AdministrateProductRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AdministrateProductRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          }
        }
      }
    },
    "/api/product/update": {
      "put": {
        "tags": [
          "Product"
        ],
        "operationId": "ApiProductUpdatePut",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AdministrateProductRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AdministrateProductRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/AdministrateProductRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/quantity-calculation-object/search": {
      "post": {
        "tags": [
          "QuantityCalculationObject"
        ],
        "operationId": "ApiQuantityCalculationObjectSearchPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/QuantityCalculationObjectResponseModelSearchResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/quantity-calculation-object": {
      "get": {
        "tags": [
          "QuantityCalculationObject"
        ],
        "operationId": "ApiQuantityCalculationObjectGet",
        "parameters": [
          {
            "name": "id",
            "in": "query",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/QuantityCalculationObjectResponseModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/quantity-calculation-object/update": {
      "post": {
        "tags": [
          "QuantityCalculationObject"
        ],
        "operationId": "ApiQuantityCalculationObjectUpdatePost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/QuantityCalculationObjectRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/QuantityCalculationObjectRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/QuantityCalculationObjectRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/schenker": {
      "get": {
        "tags": [
          "Schenker"
        ],
        "operationId": "ApiSchenkerGet",
        "parameters": [
          {
            "name": "id",
            "in": "query",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SchenkersResponseModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/schenker/users": {
      "get": {
        "tags": [
          "Schenker"
        ],
        "operationId": "ApiSchenkerUsersGet",
        "parameters": [
          {
            "name": "id",
            "in": "query",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/UserConciseResponseModel"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/schenker/opcodes-list": {
      "get": {
        "tags": [
          "Schenker"
        ],
        "operationId": "ApiSchenkerOpcodesListGet",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/SchenkerOpCodeResponseModel"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/schenker/search": {
      "post": {
        "tags": [
          "Schenker"
        ],
        "operationId": "ApiSchenkerSearchPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SchenkersResponseModelSearchResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/schenker/insert": {
      "post": {
        "tags": [
          "Schenker"
        ],
        "operationId": "ApiSchenkerInsertPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SchenkersRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SchenkersRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SchenkersRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          }
        }
      }
    },
    "/api/schenker/update": {
      "put": {
        "tags": [
          "Schenker"
        ],
        "operationId": "ApiSchenkerUpdatePut",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SchenkersRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SchenkersRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SchenkersRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/snapshot/lastAvailableSnapshotForDate": {
      "get": {
        "tags": [
          "Snapshot"
        ],
        "operationId": "ApiSnapshotLastAvailableSnapshotForDateGet",
        "parameters": [
          {
            "name": "date",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SnapshotDates"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/snapshot/allSnapshotsWithDataBetweenDates": {
      "get": {
        "tags": [
          "Snapshot"
        ],
        "operationId": "ApiSnapshotAllSnapshotsWithDataBetweenDatesGet",
        "parameters": [
          {
            "name": "fromDate",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "toDate",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "includeLastAvailableBeforeFromDate",
            "in": "query",
            "schema": {
              "type": "boolean",
              "default": true
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/SnapshotDates"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/substitutions": {
      "get": {
        "tags": [
          "Substitutions"
        ],
        "operationId": "ApiSubstitutionsGet",
        "parameters": [
          {
            "name": "id",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SubstitutionsResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/substitutions/search": {
      "post": {
        "tags": [
          "Substitutions"
        ],
        "operationId": "ApiSubstitutionsSearchPost",
        "parameters": [
          {
            "name": "isCurrentUserMol",
            "in": "query",
            "schema": {
              "type": "boolean",
              "default": false
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SubstitutionsResponseModelSearchResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/substitutions/add": {
      "post": {
        "tags": [
          "Substitutions"
        ],
        "operationId": "ApiSubstitutionsAddPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SubstitutionsRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SubstitutionsRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SubstitutionsRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/substitutions/edit": {
      "put": {
        "tags": [
          "Substitutions"
        ],
        "operationId": "ApiSubstitutionsEditPut",
        "parameters": [
          {
            "name": "id",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SubstitutionsRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SubstitutionsRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SubstitutionsRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/substitutions/delete": {
      "delete": {
        "tags": [
          "Substitutions"
        ],
        "operationId": "ApiSubstitutionsDeleteDelete",
        "parameters": [
          {
            "name": "id",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/substitutions/filter-substitutes": {
      "get": {
        "tags": [
          "Substitutions"
        ],
        "operationId": "ApiSubstitutionsFilterSubstitutesGet",
        "parameters": [
          {
            "name": "forUserId",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SubstitutionsResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/substitutions/edit-user": {
      "post": {
        "tags": [
          "Substitutions"
        ],
        "operationId": "ApiSubstitutionsEditUserPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/EditUsersRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/EditUsersRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/EditUsersRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          }
        }
      }
    },
    "/api/transfers/by-op-within-date-range": {
      "get": {
        "tags": [
          "Transfers"
        ],
        "operationId": "ApiTransfersByOpWithinDateRangeGet",
        "parameters": [
          {
            "name": "fromDate",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "toDate",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "status",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "string",
              "enum": [
                "SentForApproval",
                "ApprovedByServiceManagement",
                "ApprovedByInvControl",
                "ApprovedByImplControl",
                "Finalized",
                "AutomaticallyGenerated",
                "AutomaticApprovedByServiceManagement",
                "AutomaticApprovedByProductsAndServices",
                "AutomaticRearranged",
                "AutomaticApprovedByInvControl",
                "PendingAutomaticApprovalByProductsAndServices"
              ],
              "x-ms-enum": {
                "name": "TransferStatus",
                "modelAsString": false
              }
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "array",
                    "items": {
                      "$ref": "#/components/schemas/TransferListItemWithDate"
                    }
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/transfers/grid-data": {
      "post": {
        "tags": [
          "Transfers"
        ],
        "operationId": "ApiTransfersGridDataPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/TransfersGridRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/TransfersGridRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/TransfersGridRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/TransfersFilteredDataModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/transfers/list-items-by-send-method": {
      "get": {
        "tags": [
          "Transfers"
        ],
        "operationId": "ApiTransfersListItemsBySendMethodGet",
        "parameters": [
          {
            "name": "transferId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "bRProject",
            "in": "query",
            "schema": {
              "type": "boolean"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/TransferListItems"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/{userId}": {
      "get": {
        "tags": [
          "Users"
        ],
        "summary": "Gets user data by provided user id.",
        "operationId": "ApiUsersByUserIdGet",
        "parameters": [
          {
            "name": "userId",
            "in": "path",
            "required": true,
            "description": "The id of the user.",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SearchUserResponseModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/ad-account/{userId}": {
      "get": {
        "tags": [
          "Users"
        ],
        "summary": "Gets user ad account name.",
        "operationId": "ApiUsersAdAccountByUserIdGet",
        "parameters": [
          {
            "name": "userId",
            "in": "path",
            "required": true,
            "description": "The id of the user.",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "string"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/user-by-username/{username}": {
      "get": {
        "tags": [
          "Users"
        ],
        "summary": "Gets user data by provided username.",
        "operationId": "ApiUsersUserByUsernameByUsernameGet",
        "parameters": [
          {
            "name": "username",
            "in": "path",
            "required": true,
            "description": "The username of the user.",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SearchUserResponseModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/users-by-eln-collection": {
      "post": {
        "tags": [
          "Users"
        ],
        "summary": "Returns all users based on provided collection of elns.",
        "operationId": "ApiUsersUsersByElnCollectionPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "description": "Collection of users elns.",
          "content": {
            "application/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            },
            "text/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            },
            "application/*+json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/UserResponseModel"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/users-by-custom-iptu-collection-and-user-schenker-id": {
      "post": {
        "tags": [
          "Users"
        ],
        "summary": "Returns all users based on provided collection of custom iptu names and user's schenker id.",
        "operationId": "ApiUsersUsersByCustomIptuCollectionAndUserSchenkerIdPost",
        "parameters": [
          {
            "name": "userSchenkerId",
            "in": "query",
            "description": "The user schenker id.",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "description": "Collection of custom iptu names.",
          "content": {
            "application/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            },
            "text/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            },
            "application/*+json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/UserResponseModel"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/is-user-mol/{userId}": {
      "get": {
        "tags": [
          "Users"
        ],
        "summary": "Checks if a user is mol.",
        "operationId": "ApiUsersIsUserMolByUserIdGet",
        "parameters": [
          {
            "name": "userId",
            "in": "path",
            "required": true,
            "description": "The id of the user.",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "boolean"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/all-mols": {
      "get": {
        "tags": [
          "Users"
        ],
        "summary": "Gets data for all mols in database.",
        "operationId": "ApiUsersAllMolsGet",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/UserConciseResponseModel"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/most-frequent-fransfers-to": {
      "get": {
        "tags": [
          "Users"
        ],
        "summary": "Gets most frequest transfers to for a given 'from' user.",
        "operationId": "ApiUsersMostFrequentFransfersToGet",
        "parameters": [
          {
            "name": "fromUserId",
            "in": "query",
            "required": true,
            "description": "The id of the user for which the data is extracted for. The user id is checked against FromUserId column.",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/UserResponseModel"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/search": {
      "post": {
        "tags": [
          "Users"
        ],
        "operationId": "ApiUsersSearchPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchUsersRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchUsersRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SearchUsersRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SearchUserResponseModelSearchResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/users": {
      "put": {
        "tags": [
          "Users"
        ],
        "summary": "Updates user in database.",
        "operationId": "ApiUsersPut",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "description": "The edit user request model.",
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/EditUsersRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/EditUsersRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/EditUsersRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/activate-users": {
      "put": {
        "tags": [
          "Users"
        ],
        "summary": "Activate selected users.",
        "operationId": "ApiUsersActivateUsersPut",
        "parameters": [
          {
            "name": "userId",
            "in": "query",
            "required": true,
            "description": "The id of the user, performing the activation.",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "description": "The ids if the users, selected for activation.",
          "content": {
            "application/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            },
            "text/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            },
            "application/*+json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/block-users": {
      "put": {
        "tags": [
          "Users"
        ],
        "summary": "Block selected users.",
        "operationId": "ApiUsersBlockUsersPut",
        "parameters": [
          {
            "name": "userId",
            "in": "query",
            "required": true,
            "description": "The id of the user, performing the blocking.",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "description": "The ids if the users, selected for blocking.",
          "content": {
            "application/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            },
            "text/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            },
            "application/*+json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/update-user-notification": {
      "patch": {
        "tags": [
          "Users"
        ],
        "operationId": "ApiUsersUpdateUserNotificationPatch",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/UpdateUserNotificationRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/UpdateUserNotificationRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/UpdateUserNotificationRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/remove-user-notification": {
      "patch": {
        "tags": [
          "Users"
        ],
        "operationId": "ApiUsersRemoveUserNotificationPatch",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/RemoveUserNotificationRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/RemoveUserNotificationRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/RemoveUserNotificationRequestModel"
                  }
                ]
              }
            }
          }
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/warehouses/select-list": {
      "get": {
        "tags": [
          "Warehouses"
        ],
        "operationId": "ApiWarehousesSelectListGet",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WarehousesResponseModel"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/warehouses/by-region": {
      "get": {
        "tags": [
          "Warehouses"
        ],
        "operationId": "ApiWarehousesByRegionGet",
        "parameters": [
          {
            "name": "region",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WarehousesResponseModel"
                  }
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BaseResponseModel"
                }
              }
            }
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "AcceptItemsRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "selectedItems": {
            "type": "array",
            "nullable": true,
            "items": {
              "type": "integer",
              "format": "int32"
            }
          },
          "userId": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "AdministrateProductRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "equipmentGroupId",
          "equipmentNameFirst",
          "id",
          "matFirst",
          "minimumQuantity",
          "name",
          "sapMaterialNum",
          "sapRequestType",
          "unitOfMeasure"
        ],
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "maxLength": 100,
            "minLength": 0
          },
          "sapMaterialNum": {
            "type": "string",
            "maxLength": 50,
            "minLength": 0,
            "pattern": "^\\s*\\S+\\s*$"
          },
          "equipmentNameFirst": {
            "type": "string",
            "maxLength": 100,
            "minLength": 0
          },
          "matFirst": {
            "type": "string",
            "maxLength": 50,
            "minLength": 0,
            "pattern": "^\\s*\\S+\\s*$"
          },
          "minimumQuantity": {
            "type": "integer",
            "format": "int32",
            "maximum": 2147483647.0,
            "minimum": 0.0
          },
          "sapRequestType": {
            "type": "integer",
            "format": "int32"
          },
          "brProjectName": {
            "type": "string",
            "nullable": true
          },
          "sapElementCode": {
            "type": "string",
            "nullable": true
          },
          "equipmentGroupId": {
            "type": "integer",
            "format": "int32"
          },
          "unitOfMeasure": {
            "type": "integer",
            "format": "int32"
          },
          "boxCapacity": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          }
        }
      },
      "AdministrateProductResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "sapMaterialNum": {
            "type": "string",
            "nullable": true
          },
          "equipmentNameFirst": {
            "type": "string",
            "nullable": true
          },
          "matFirst": {
            "type": "string",
            "nullable": true
          },
          "serialNumbersRequiredStatus": {
            "type": "string",
            "format": "string",
            "enum": [
              "Optional",
              "Required"
            ],
            "x-ms-enum": {
              "name": "SerialNumberRequiredStatus",
              "modelAsString": false
            }
          },
          "minimumQuantity": {
            "type": "integer",
            "format": "int32"
          },
          "sapRequestType": {
            "type": "integer",
            "format": "int32"
          },
          "brProjectName": {
            "type": "string",
            "nullable": true
          },
          "sapElementCode": {
            "type": "string",
            "nullable": true
          },
          "equipmentGroupId": {
            "type": "integer",
            "format": "int32"
          },
          "unitOfMeasure": {
            "type": "integer",
            "format": "int32"
          },
          "boxCapacity": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          }
        }
      },
      "AdministrateProductResponseModelSearchResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "count": {
            "type": "integer",
            "format": "int32"
          },
          "dataCollection": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/AdministrateProductResponseModel"
            }
          }
        }
      },
      "AvailableEquipment": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "itemId": {
            "type": "integer",
            "format": "int32"
          },
          "userId": {
            "type": "string",
            "nullable": true
          },
          "itemQuantity": {
            "type": "integer",
            "format": "int32"
          },
          "isDaily": {
            "type": "boolean"
          },
          "item": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/Item"
              }
            ]
          },
          "user": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/User"
              }
            ]
          }
        }
      },
      "AvailableEquipmentByOpsnapshots": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentQuantity": {
            "type": "integer",
            "format": "int32"
          },
          "opid": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentTypeId": {
            "type": "integer",
            "format": "int32"
          },
          "snapshotDateId": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentType": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/EquipmentType"
              }
            ]
          },
          "op": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/Schenkers"
              }
            ]
          },
          "snapshotDate": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/SnapshotDates"
              }
            ]
          }
        }
      },
      "AvailableEquipmentRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "itemId": {
            "type": "integer",
            "format": "int32"
          },
          "userId": {
            "type": "string",
            "nullable": true
          },
          "itemQuantity": {
            "type": "integer",
            "format": "int32"
          },
          "isDaily": {
            "type": "boolean"
          }
        }
      },
      "AvailableEquipmentResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "itemId": {
            "type": "integer",
            "format": "int32"
          },
          "userId": {
            "type": "string",
            "nullable": true
          },
          "itemQuantity": {
            "type": "integer",
            "format": "int32"
          },
          "isDaily": {
            "type": "boolean"
          },
          "isReserved": {
            "type": "boolean"
          },
          "isWaitingForConfirmation": {
            "type": "boolean"
          },
          "item": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/ItemResponseModel"
              }
            ]
          }
        }
      },
      "AvailableEquipmentResponseModelSearchResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "count": {
            "type": "integer",
            "format": "int32"
          },
          "dataCollection": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/AvailableEquipmentResponseModel"
            }
          }
        }
      },
      "BaseResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "message": {
            "type": "string",
            "nullable": true
          },
          "success": {
            "type": "boolean"
          }
        }
      },
      "CancelItemsRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "selectedItems": {
            "type": "array",
            "nullable": true,
            "items": {
              "type": "integer",
              "format": "int32"
            }
          },
          "refuseReason": {
            "type": "string",
            "nullable": true
          },
          "userId": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "Cities": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "region": {
            "type": "integer",
            "format": "int32"
          },
          "isDeleted": {
            "type": "boolean"
          },
          "deletedOn": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "createdOn": {
            "type": "string",
            "format": "date-time"
          },
          "createdBy": {
            "type": "string",
            "nullable": true
          },
          "modifiedOn": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "modifiedBy": {
            "type": "string",
            "nullable": true
          },
          "sapcityCode": {
            "type": "string",
            "nullable": true
          },
          "cluster": {
            "type": "string",
            "nullable": true
          },
          "schenkers": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/Schenkers"
            }
          },
          "user": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/User"
            }
          }
        }
      },
      "CitiesRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "name",
          "region"
        ],
        "properties": {
          "name": {
            "type": "string",
            "maxLength": 50,
            "minLength": 0
          },
          "region": {
            "type": "integer",
            "format": "int32"
          },
          "sapCityCode": {
            "type": "string",
            "nullable": true
          },
          "cluster": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "CitiesResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "region": {
            "type": "integer",
            "format": "int32"
          },
          "isDeleted": {
            "type": "boolean"
          },
          "sapCityCode": {
            "type": "string",
            "nullable": true
          },
          "cluster": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "CitiesResponseModelSearchResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "count": {
            "type": "integer",
            "format": "int32"
          },
          "dataCollection": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/CitiesResponseModel"
            }
          }
        }
      },
      "ConciseEquipmentTypesRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "equipmentTypeIds"
        ],
        "properties": {
          "equipmentTypeIds": {
            "type": "array",
            "items": {
              "type": "integer",
              "format": "int32"
            }
          },
          "serialNumbersRequired": {
            "type": "boolean"
          }
        }
      },
      "DeliverItemsRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "selectedItems": {
            "type": "array",
            "nullable": true,
            "items": {
              "type": "integer",
              "format": "int32"
            }
          },
          "fromUserId": {
            "type": "string",
            "nullable": true
          },
          "toUserId": {
            "type": "string",
            "nullable": true
          },
          "isSpecialUser": {
            "type": "boolean"
          },
          "postOfficeId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "waybillNum": {
            "type": "string",
            "nullable": true
          },
          "waybillDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          }
        }
      },
      "EditUsersRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "cityId",
          "id",
          "schenkerId"
        ],
        "properties": {
          "id": {
            "type": "string"
          },
          "eln": {
            "type": "string",
            "nullable": true
          },
          "firstName": {
            "type": "string",
            "nullable": true
          },
          "surname": {
            "type": "string",
            "nullable": true
          },
          "familyName": {
            "type": "string",
            "nullable": true
          },
          "status": {
            "type": "string",
            "nullable": true
          },
          "schenkerId": {
            "type": "integer",
            "format": "int32"
          },
          "op": {
            "type": "string",
            "nullable": true
          },
          "type": {
            "type": "string",
            "nullable": true
          },
          "userType": {
            "type": "string",
            "format": "string",
            "enum": [
              "Installer",
              "MOL"
            ],
            "x-ms-enum": {
              "name": "UserType",
              "modelAsString": false
            }
          },
          "clientNumber": {
            "type": "string",
            "nullable": true
          },
          "city": {
            "type": "string",
            "nullable": true
          },
          "cityId": {
            "type": "integer",
            "format": "int32"
          },
          "region": {
            "type": "string",
            "nullable": true
          },
          "iptuName": {
            "type": "string",
            "nullable": true
          },
          "phoneNumber": {
            "type": "string",
            "nullable": true
          },
          "statusClass": {
            "type": "string",
            "nullable": true
          },
          "userStatuses": {
            "type": "string",
            "format": "string",
            "enum": [
              "Active",
              "Blocked"
            ],
            "x-ms-enum": {
              "name": "UserStatus",
              "modelAsString": false
            }
          }
        }
      },
      "EquipmentGroupResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "containsSubstitutes": {
            "type": "boolean"
          }
        }
      },
      "EquipmentGroups": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "containsSubstitutes": {
            "type": "boolean"
          },
          "equipmentType": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/EquipmentType"
            }
          },
          "quantityCalculationObject": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/QuantityCalculationObject"
            }
          }
        }
      },
      "EquipmentType": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "sapmaterialNum": {
            "type": "string",
            "nullable": true
          },
          "serialNumberRequired": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentGroupId": {
            "type": "integer",
            "format": "int32"
          },
          "unitOfMeasure": {
            "type": "integer",
            "format": "int32"
          },
          "sendMethod": {
            "type": "integer",
            "format": "int32"
          },
          "sapsupplyCode": {
            "type": "string",
            "nullable": true
          },
          "minimumQuantity": {
            "type": "integer",
            "format": "int32"
          },
          "isTransferFromSapAllowed": {
            "type": "boolean"
          },
          "boxCapacity": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "lastMonthlyOrderQuantity": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "brprojectCode": {
            "type": "string",
            "nullable": true
          },
          "equipmentGroup": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/EquipmentGroups"
              }
            ]
          },
          "availableEquipmentByOpsnapshots": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/AvailableEquipmentByOpsnapshots"
            }
          },
          "item": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/Item"
            }
          },
          "quantityCalculationObject": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/QuantityCalculationObject"
            }
          },
          "transferListItems": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/TransferListItems"
            }
          }
        }
      },
      "EquipmentTypeConciseDto": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "sapMaterialNum": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "EquipmentTypeResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "sapmaterialNum": {
            "type": "string",
            "nullable": true
          },
          "serialNumberRequired": {
            "type": "boolean"
          },
          "equipmentGroupId": {
            "type": "integer",
            "format": "int32"
          },
          "unitOfMeasure": {
            "type": "integer",
            "format": "int32"
          },
          "sendMethod": {
            "type": "string",
            "nullable": true
          },
          "sapsupplyCode": {
            "type": "string",
            "nullable": true
          },
          "minimumQuantity": {
            "type": "integer",
            "format": "int32"
          },
          "isTransferFromSapAllowed": {
            "type": "boolean"
          },
          "boxCapacity": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "lastMonthlyOrderQuantity": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "brprojectCode": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "Error": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "requestId": {
            "type": "string",
            "nullable": true
          },
          "sourceReq": {
            "type": "string",
            "nullable": true
          },
          "historyId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "requestText": {
            "type": "string",
            "nullable": true
          },
          "errorCode": {
            "type": "string",
            "nullable": true
          },
          "errorText": {
            "type": "string",
            "nullable": true
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "userId": {
            "type": "string",
            "nullable": true
          },
          "user2Id": {
            "type": "string",
            "nullable": true
          },
          "history": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/History"
              }
            ]
          },
          "user": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/User"
              }
            ]
          },
          "user2": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/User"
              }
            ]
          }
        }
      },
      "History": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "itemId": {
            "type": "integer",
            "format": "int32"
          },
          "itemTypeOfUsage": {
            "type": "integer",
            "format": "int32"
          },
          "itemValidity": {
            "type": "string",
            "nullable": true
          },
          "icmIdSgwId": {
            "type": "string",
            "nullable": true
          },
          "sourceSystem": {
            "type": "string",
            "nullable": true
          },
          "deliveryType": {
            "type": "string",
            "nullable": true
          },
          "delivelyFromPoint": {
            "type": "string",
            "nullable": true
          },
          "deliveryShop": {
            "type": "string",
            "nullable": true
          },
          "fromUserId": {
            "type": "string",
            "nullable": true
          },
          "toUserId": {
            "type": "string",
            "nullable": true
          },
          "deliveryNumSap": {
            "type": "string",
            "nullable": true
          },
          "deliveryDateSap": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "deliveryItemQty": {
            "type": "integer",
            "format": "int32"
          },
          "intDeliveryNum": {
            "type": "string",
            "nullable": true
          },
          "insertDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "operationType": {
            "type": "integer",
            "format": "int32"
          },
          "docStatus": {
            "type": "integer",
            "format": "int32"
          },
          "serviceIdFrom": {
            "type": "string",
            "nullable": true
          },
          "serviceIdTo": {
            "type": "string",
            "nullable": true
          },
          "orderType": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "crmorderId": {
            "type": "string",
            "nullable": true
          },
          "refuseReason": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "autoGenerated": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "orderAction": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "postOfficeId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "waybillDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "waybillNum": {
            "type": "string",
            "nullable": true
          },
          "transferNumber": {
            "type": "integer",
            "format": "int64",
            "nullable": true
          },
          "fromUser": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/User"
              }
            ]
          },
          "item": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/Item"
              }
            ]
          },
          "postOffice": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/PostOffice"
              }
            ]
          },
          "toUser": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/User"
              }
            ]
          },
          "error": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/Error"
            }
          },
          "statusHistory": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/StatusHistory"
            }
          }
        }
      },
      "HistoryRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "itemId": {
            "type": "integer",
            "format": "int32"
          },
          "icmIdSgwId": {
            "type": "string",
            "nullable": true
          },
          "sourceSystem": {
            "type": "string",
            "nullable": true
          },
          "deliveryType": {
            "type": "string",
            "nullable": true
          },
          "intDeliveryNum": {
            "type": "string",
            "nullable": true
          },
          "delivelyFromPoint": {
            "type": "string",
            "nullable": true
          },
          "deliveryShop": {
            "type": "string",
            "nullable": true
          },
          "toUserId": {
            "type": "string",
            "nullable": true
          },
          "fromUserId": {
            "type": "string",
            "nullable": true
          },
          "deliveryDateSap": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "deliveryItemQty": {
            "type": "integer",
            "format": "int32"
          },
          "insertDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "operationType": {
            "type": "integer",
            "format": "int32"
          },
          "docStatus": {
            "type": "integer",
            "format": "int32"
          },
          "serviceIdTo": {
            "type": "string",
            "nullable": true
          },
          "autoGenerated": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "itemTypeOfUsage": {
            "type": "integer",
            "format": "int32"
          },
          "itemValidity": {
            "type": "string",
            "nullable": true
          },
          "crmorderId": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "InsertAvailableEquipmentRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "itemId": {
            "type": "integer",
            "format": "int32"
          },
          "userId": {
            "type": "string",
            "nullable": true
          },
          "itemQuantity": {
            "type": "integer",
            "format": "int32"
          },
          "isDaily": {
            "type": "boolean"
          }
        }
      },
      "InsertHistoryRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "itemId": {
            "type": "integer",
            "format": "int32"
          },
          "icmIdSgwId": {
            "type": "string",
            "nullable": true
          },
          "sourceSystem": {
            "type": "string",
            "nullable": true
          },
          "deliveryType": {
            "type": "string",
            "nullable": true
          },
          "intDeliveryNum": {
            "type": "string",
            "nullable": true
          },
          "delivelyFromPoint": {
            "type": "string",
            "nullable": true
          },
          "deliveryShop": {
            "type": "string",
            "nullable": true
          },
          "fromUserId": {
            "type": "string",
            "nullable": true
          },
          "toUserId": {
            "type": "string",
            "nullable": true
          },
          "deliveryDateSap": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "deliveryItemQty": {
            "type": "integer",
            "format": "int32"
          },
          "insertDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "serviceIdFrom": {
            "type": "string",
            "nullable": true
          },
          "serviceIdTo": {
            "type": "string",
            "nullable": true
          },
          "orderType": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "crmorderId": {
            "type": "string",
            "nullable": true
          },
          "orderAction": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "operationType": {
            "type": "integer",
            "format": "int32"
          },
          "docStatus": {
            "type": "integer",
            "format": "int32"
          },
          "autoGenerated": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "itemTypeOfUsage": {
            "type": "integer",
            "format": "int32"
          },
          "itemValidity": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "InsertItemRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "sapmaterialNum"
        ],
        "properties": {
          "icmIdSgwId": {
            "type": "string",
            "nullable": true
          },
          "sourceSystem": {
            "type": "string",
            "nullable": true
          },
          "modifyDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "equipmentName": {
            "type": "string",
            "nullable": true
          },
          "equipmentSerialNum": {
            "type": "string",
            "nullable": true
          },
          "sapserialNum": {
            "type": "string",
            "nullable": true
          },
          "sapmaterialNum": {
            "type": "string"
          },
          "equipmentTypeId": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentMaterialGroup": {
            "type": "string",
            "nullable": true
          },
          "typeOfUsage": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentValidity": {
            "type": "string",
            "nullable": true
          },
          "additionDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          }
        }
      },
      "InsertStatusHistoryRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "historyId": {
            "type": "integer",
            "format": "int32"
          },
          "docStatusNew": {
            "type": "integer",
            "format": "int32"
          },
          "sourceSystem": {
            "type": "string",
            "nullable": true
          },
          "elnId": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "Item": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "icmIdSgwId": {
            "type": "string",
            "nullable": true
          },
          "sourceSystem": {
            "type": "string",
            "nullable": true
          },
          "modifyDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "equipmentName": {
            "type": "string",
            "nullable": true
          },
          "equipmentSerialNum": {
            "type": "string",
            "nullable": true
          },
          "sapserialNum": {
            "type": "string",
            "nullable": true
          },
          "sapmaterialNum": {
            "type": "string",
            "nullable": true
          },
          "equipmentTypeId": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentMaterialGroup": {
            "type": "string",
            "nullable": true
          },
          "typeOfUsage": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentValidity": {
            "type": "string",
            "nullable": true
          },
          "additionDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "equipmentType": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/EquipmentType"
              }
            ]
          },
          "availableEquipment": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/AvailableEquipment"
            }
          },
          "histories": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/History"
            }
          }
        }
      },
      "ItemResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "icmIdSgwId": {
            "type": "string",
            "nullable": true
          },
          "equipmentName": {
            "type": "string",
            "nullable": true
          },
          "equipmentSerialNum": {
            "type": "string",
            "nullable": true
          },
          "sapserialNum": {
            "type": "string",
            "nullable": true
          },
          "sapmaterialNum": {
            "type": "string",
            "nullable": true
          },
          "equipmentTypeId": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentMaterialGroup": {
            "type": "string",
            "nullable": true
          },
          "typeOfUsage": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentValidity": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "ItemResponseModelSearchResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "count": {
            "type": "integer",
            "format": "int32"
          },
          "dataCollection": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/ItemResponseModel"
            }
          }
        }
      },
      "Notification": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "notificationType": {
            "type": "integer",
            "format": "int32"
          },
          "text": {
            "type": "string",
            "nullable": true
          },
          "userId": {
            "type": "string",
            "nullable": true
          },
          "isRead": {
            "type": "boolean"
          },
          "user": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/User"
              }
            ]
          }
        }
      },
      "PostOffice": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "deleteFl": {
            "type": "integer",
            "format": "int32"
          },
          "history": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/History"
            }
          }
        }
      },
      "PostOfficesSelectListResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "ProblemDetails": {
        "type": "object",
        "additionalProperties": {},
        "properties": {
          "type": {
            "type": "string",
            "nullable": true
          },
          "title": {
            "type": "string",
            "nullable": true
          },
          "status": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "detail": {
            "type": "string",
            "nullable": true
          },
          "instance": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "QuantityCalculationObject": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "criticalQuantity": {
            "type": "integer",
            "format": "int32"
          },
          "isGroupWithSubstitutes": {
            "type": "boolean"
          },
          "isManuallySet": {
            "type": "boolean"
          },
          "manualSetDaysValid": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "lastUpdateDate": {
            "type": "string",
            "format": "date-time"
          },
          "deliveryTimeInDays": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "opid": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentGroupId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "equipmentTypeId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "equipmentGroup": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/EquipmentGroups"
              }
            ]
          },
          "equipmentType": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/EquipmentType"
              }
            ]
          },
          "op": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/Schenkers"
              }
            ]
          }
        }
      },
      "QuantityCalculationObjectRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "criticalQuantity": {
            "type": "integer",
            "format": "int32"
          },
          "toggleToAutomaticGeneration": {
            "type": "boolean"
          }
        }
      },
      "QuantityCalculationObjectResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "criticalQuantity": {
            "type": "integer",
            "format": "int32"
          },
          "isGroupWithSubstitutes": {
            "type": "boolean"
          },
          "isManuallySet": {
            "type": "boolean"
          },
          "toggleToAutomaticGeneration": {
            "type": "boolean"
          },
          "lastUpdateDate": {
            "type": "string",
            "format": "date-time"
          },
          "opCode": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "QuantityCalculationObjectResponseModelSearchResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "count": {
            "type": "integer",
            "format": "int32"
          },
          "dataCollection": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/QuantityCalculationObjectResponseModel"
            }
          }
        }
      },
      "RecipientHistory": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "fromUserId": {
            "type": "string",
            "nullable": true
          },
          "toUserId": {
            "type": "string",
            "nullable": true
          },
          "count": {
            "type": "integer",
            "format": "int32"
          },
          "fromUser": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/User"
              }
            ]
          },
          "toUser": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/User"
              }
            ]
          }
        }
      },
      "RefuseItemsRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "selectedItems": {
            "type": "array",
            "nullable": true,
            "items": {
              "type": "integer",
              "format": "int32"
            }
          },
          "refuseReason": {
            "type": "string",
            "nullable": true
          },
          "userId": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "RemoveItemForTransferRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "itemId": {
            "type": "integer",
            "format": "int32"
          },
          "userId": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "RemoveItemsForTransferRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "selectedItems": {
            "type": "array",
            "nullable": true,
            "items": {
              "type": "integer",
              "format": "int32"
            }
          },
          "userId": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "RemoveUserNotificationRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "userId"
        ],
        "properties": {
          "userId": {
            "type": "string"
          },
          "notificationId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "notificationType": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "ReserveItemForTransferRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "itemId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "itemSerialNumber": {
            "type": "string",
            "nullable": true
          },
          "userId": {
            "type": "string",
            "nullable": true
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          }
        }
      },
      "ReserveItemHistoryResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "itemId": {
            "type": "string",
            "nullable": true
          },
          "itemEquipmentTypeName": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "SapmatMapp": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "sapmaterialNum": {
            "type": "string",
            "nullable": true
          },
          "matFirst": {
            "type": "string",
            "nullable": true
          },
          "equipmentNameFirst": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "SchenkerOpCodeResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "string",
            "nullable": true
          },
          "opCode": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "Schenkers": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "opcode": {
            "type": "string",
            "nullable": true
          },
          "transportArea": {
            "type": "integer",
            "format": "int32"
          },
          "processingTime": {
            "type": "integer",
            "format": "int32"
          },
          "protectiveTime": {
            "type": "integer",
            "format": "int32"
          },
          "molid": {
            "type": "string",
            "nullable": true
          },
          "address": {
            "type": "string",
            "nullable": true
          },
          "cityId": {
            "type": "integer",
            "format": "int32"
          },
          "warehouseId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "includedInMonthlyCalculations": {
            "type": "boolean",
            "nullable": true
          },
          "city": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/Cities"
              }
            ]
          },
          "mol": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/User"
              }
            ]
          },
          "warehouse": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/Warehouses"
              }
            ]
          },
          "availableEquipmentByOpsnapshots": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/AvailableEquipmentByOpsnapshots"
            }
          },
          "quantityCalculationObject": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/QuantityCalculationObject"
            }
          },
          "transfers": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/Transfers"
            }
          },
          "user": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/User"
            }
          }
        }
      },
      "SchenkersRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "id",
          "opCode"
        ],
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "opCode": {
            "type": "string",
            "maxLength": 5,
            "minLength": 4
          },
          "transportArea": {
            "type": "integer",
            "format": "int32"
          },
          "processingTime": {
            "type": "integer",
            "format": "int32"
          },
          "protectiveTime": {
            "type": "integer",
            "format": "int32"
          },
          "cityId": {
            "type": "integer",
            "format": "int32"
          },
          "address": {
            "type": "string",
            "nullable": true
          },
          "localWarehouseId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          }
        }
      },
      "SchenkersResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "string",
            "nullable": true
          },
          "opCode": {
            "type": "string",
            "nullable": true
          },
          "transportArea": {
            "type": "integer",
            "format": "int32"
          },
          "processingTime": {
            "type": "integer",
            "format": "int32"
          },
          "protectiveTime": {
            "type": "integer",
            "format": "int32"
          },
          "cityId": {
            "type": "integer",
            "format": "int32"
          },
          "address": {
            "type": "string",
            "nullable": true
          },
          "regionName": {
            "type": "string",
            "nullable": true
          },
          "iptuName": {
            "type": "string",
            "nullable": true
          },
          "localWarehouseId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "localWarehouseName": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "SchenkersResponseModelSearchResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "count": {
            "type": "integer",
            "format": "int32"
          },
          "dataCollection": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/SchenkersResponseModel"
            }
          }
        }
      },
      "SearchRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "pageNumber": {
            "type": "integer",
            "format": "int32",
            "maximum": 2147483647.0,
            "minimum": 0.0
          },
          "pageSize": {
            "type": "integer",
            "format": "int32",
            "maximum": 2147483647.0,
            "minimum": 0.0
          },
          "sortBy": {
            "type": "string",
            "nullable": true
          },
          "sortDir": {
            "type": "string",
            "nullable": true
          },
          "query": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "SearchRequestModelWithUserId": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "userId"
        ],
        "properties": {
          "pageNumber": {
            "type": "integer",
            "format": "int32",
            "maximum": 2147483647.0,
            "minimum": 0.0
          },
          "pageSize": {
            "type": "integer",
            "format": "int32",
            "maximum": 2147483647.0,
            "minimum": 0.0
          },
          "sortBy": {
            "type": "string",
            "nullable": true
          },
          "sortDir": {
            "type": "string",
            "nullable": true
          },
          "query": {
            "type": "string",
            "nullable": true
          },
          "userId": {
            "type": "string"
          }
        }
      },
      "SearchUserResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "string",
            "nullable": true
          },
          "eln": {
            "type": "string",
            "nullable": true
          },
          "firstName": {
            "type": "string",
            "nullable": true
          },
          "surname": {
            "type": "string",
            "nullable": true
          },
          "familyName": {
            "type": "string",
            "nullable": true
          },
          "fullName": {
            "type": "string",
            "nullable": true
          },
          "op": {
            "type": "string",
            "nullable": true
          },
          "regionId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "region": {
            "type": "string",
            "nullable": true
          },
          "cityId": {
            "type": "integer",
            "format": "int32"
          },
          "city": {
            "type": "string",
            "nullable": true
          },
          "clientNumber": {
            "type": "string",
            "nullable": true
          },
          "phoneNumber": {
            "type": "string",
            "nullable": true
          },
          "displayName": {
            "type": "string",
            "nullable": true
          },
          "iptuName": {
            "type": "string",
            "nullable": true
          },
          "position": {
            "type": "string",
            "nullable": true
          },
          "adAccount": {
            "type": "string",
            "nullable": true
          },
          "email": {
            "type": "string",
            "nullable": true
          },
          "opcode": {
            "type": "string",
            "nullable": true
          },
          "schenkerId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "blocked": {
            "type": "string",
            "format": "string",
            "enum": [
              "Active",
              "Blocked"
            ],
            "x-ms-enum": {
              "name": "UserStatus",
              "modelAsString": false
            }
          },
          "dataBlocked": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "blockedByUserId": {
            "type": "string",
            "nullable": true
          },
          "hasPendingTransfer": {
            "type": "boolean"
          }
        }
      },
      "SearchUserResponseModelSearchResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "count": {
            "type": "integer",
            "format": "int32"
          },
          "dataCollection": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/SearchUserResponseModel"
            }
          }
        }
      },
      "SearchUsersRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "pageNumber": {
            "type": "integer",
            "format": "int32",
            "maximum": 2147483647.0,
            "minimum": 0.0
          },
          "pageSize": {
            "type": "integer",
            "format": "int32",
            "maximum": 2147483647.0,
            "minimum": 0.0
          },
          "sortBy": {
            "type": "string",
            "nullable": true
          },
          "sortDir": {
            "type": "string",
            "nullable": true
          },
          "query": {
            "type": "string",
            "nullable": true
          },
          "filteredUsersEln": {
            "type": "array",
            "nullable": true,
            "items": {
              "type": "string"
            }
          }
        }
      },
      "SnapshotDates": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "date": {
            "type": "string",
            "format": "date-time"
          },
          "availableEquipmentByOpsnapshots": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/AvailableEquipmentByOpsnapshots"
            }
          }
        }
      },
      "StatusHistory": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "historyId": {
            "type": "integer",
            "format": "int32"
          },
          "docStatusOld": {
            "type": "integer",
            "format": "int32"
          },
          "docStatusNew": {
            "type": "integer",
            "format": "int32"
          },
          "sourceSystem": {
            "type": "string",
            "nullable": true
          },
          "dateInsert": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "userId": {
            "type": "string",
            "nullable": true
          },
          "history": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/History"
              }
            ]
          },
          "user": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/User"
              }
            ]
          }
        }
      },
      "StatusHistoryRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "historyId": {
            "type": "integer",
            "format": "int32"
          },
          "docStatusOld": {
            "type": "integer",
            "format": "int32"
          },
          "docStatusNew": {
            "type": "integer",
            "format": "int32"
          },
          "sourceSystem": {
            "type": "string",
            "nullable": true
          },
          "dateInsert": {
            "type": "string",
            "format": "date-time"
          }
        }
      },
      "Substitutions": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "opcode": {
            "type": "string",
            "nullable": true
          },
          "fromDate": {
            "type": "string",
            "format": "date-time"
          },
          "toDate": {
            "type": "string",
            "format": "date-time"
          },
          "forUserId": {
            "type": "string",
            "nullable": true
          },
          "substituteUserId": {
            "type": "string",
            "nullable": true
          },
          "isActive": {
            "type": "boolean"
          },
          "forUser": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/User"
              }
            ]
          },
          "substituteUser": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/User"
              }
            ]
          }
        }
      },
      "SubstitutionsRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "opId": {
            "type": "integer",
            "format": "int32"
          },
          "opCode": {
            "type": "string",
            "nullable": true
          },
          "fromDate": {
            "type": "string",
            "format": "date-time"
          },
          "toDate": {
            "type": "string",
            "format": "date-time"
          },
          "forUserId": {
            "type": "string",
            "nullable": true
          },
          "forUserFullName": {
            "type": "string",
            "nullable": true
          },
          "substituteUserId": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "SubstitutionsResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "opCode": {
            "type": "string",
            "nullable": true
          },
          "fromDate": {
            "type": "string",
            "format": "date-time"
          },
          "toDate": {
            "type": "string",
            "format": "date-time"
          },
          "forUserId": {
            "type": "string",
            "nullable": true
          },
          "forUserFullName": {
            "type": "string",
            "nullable": true
          },
          "substituteUserId": {
            "type": "string",
            "nullable": true
          },
          "substituteUserFullName": {
            "type": "string",
            "nullable": true
          },
          "forUserAdAccount": {
            "type": "string",
            "nullable": true
          },
          "forSubstituteAdAccount": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "SubstitutionsResponseModelSearchResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "count": {
            "type": "integer",
            "format": "int32"
          },
          "dataCollection": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/SubstitutionsResponseModel"
            }
          }
        }
      },
      "TransferedItemsModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "totalCount": {
            "type": "integer",
            "format": "int32"
          },
          "expiredCount": {
            "type": "integer",
            "format": "int32"
          },
          "toUserId": {
            "type": "string",
            "nullable": true
          },
          "toUserUnacceptedTransferNotificationId": {
            "type": "integer",
            "format": "int32"
          },
          "fromUserId": {
            "type": "string",
            "nullable": true
          },
          "serialNumbers": {
            "type": "array",
            "nullable": true,
            "items": {
              "type": "string"
            }
          }
        }
      },
      "TransferListItems": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentTypeId": {
            "type": "integer",
            "format": "int32"
          },
          "transferId": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentType": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/EquipmentType"
              }
            ]
          },
          "transfer": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/Transfers"
              }
            ]
          }
        }
      },
      "TransferListItemWithDate": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "equipmentTypeSAPMaterialNumber": {
            "type": "string",
            "nullable": true
          },
          "equipmentTypeId": {
            "type": "integer",
            "format": "int32"
          },
          "quantity": {
            "type": "integer",
            "format": "int32"
          },
          "deliveryDate": {
            "type": "string",
            "format": "date-time"
          },
          "status": {
            "type": "string",
            "format": "string",
            "enum": [
              "SentForApproval",
              "ApprovedByServiceManagement",
              "ApprovedByInvControl",
              "ApprovedByImplControl",
              "Finalized",
              "AutomaticallyGenerated",
              "AutomaticApprovedByServiceManagement",
              "AutomaticApprovedByProductsAndServices",
              "AutomaticRearranged",
              "AutomaticApprovedByInvControl",
              "PendingAutomaticApprovalByProductsAndServices"
            ],
            "x-ms-enum": {
              "name": "TransferStatus",
              "modelAsString": false
            }
          },
          "sendMethod": {
            "type": "string",
            "format": "string",
            "enum": [
              "Transfer",
              "BR",
              "BRProject"
            ],
            "x-ms-enum": {
              "name": "SendMethod",
              "modelAsString": false
            }
          }
        }
      },
      "Transfers": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "requestNumber": {
            "type": "string",
            "nullable": true
          },
          "deliveryDate": {
            "type": "string",
            "format": "date-time"
          },
          "isDeleted": {
            "type": "boolean"
          },
          "deletedOn": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "createdOn": {
            "type": "string",
            "format": "date-time"
          },
          "createdBy": {
            "type": "string",
            "nullable": true
          },
          "modifiedOn": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "modifiedBy": {
            "type": "string",
            "nullable": true
          },
          "isCritical": {
            "type": "boolean"
          },
          "status": {
            "type": "integer",
            "format": "int32"
          },
          "schenkerId": {
            "type": "integer",
            "format": "int32"
          },
          "isAutomaticallyGenerated": {
            "type": "boolean"
          },
          "isWarehouseTransport": {
            "type": "boolean"
          },
          "isHidden": {
            "type": "boolean"
          },
          "transferNumSerCwtoMs": {
            "type": "integer",
            "format": "int64",
            "nullable": true
          },
          "transferNumSerMstoRs": {
            "type": "integer",
            "format": "int64",
            "nullable": true
          },
          "transferNumSerRstoOp": {
            "type": "integer",
            "format": "int64",
            "nullable": true
          },
          "brdeliveryNumbers": {
            "type": "string",
            "nullable": true
          },
          "brprojectDeliveryNumbers": {
            "type": "string",
            "nullable": true
          },
          "schenker": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/Schenkers"
              }
            ]
          },
          "transferListItems": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/TransferListItems"
            }
          }
        }
      },
      "TransfersFilteredDataModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "allDataRowsCount": {
            "type": "integer",
            "format": "int32"
          },
          "dataRows": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/Transfers"
            }
          }
        }
      },
      "TransfersGridRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "opCode": {
            "type": "string",
            "nullable": true
          },
          "fromDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "toDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "status": {
            "type": "string",
            "nullable": true,
            "enum": [
              "SentForApproval",
              "ApprovedByServiceManagement",
              "ApprovedByInvControl",
              "ApprovedByImplControl",
              "Finalized",
              "AutomaticallyGenerated",
              "AutomaticApprovedByServiceManagement",
              "AutomaticApprovedByProductsAndServices",
              "AutomaticRearranged",
              "AutomaticApprovedByInvControl",
              "PendingAutomaticApprovalByProductsAndServices"
            ]
          },
          "query": {
            "type": "string",
            "nullable": true
          },
          "sortBy": {
            "type": "string",
            "nullable": true
          },
          "dir": {
            "type": "string",
            "nullable": true
          },
          "page": {
            "type": "integer",
            "format": "int32"
          },
          "pageDataSize": {
            "type": "integer",
            "format": "int32"
          }
        }
      },
      "UpdateAvailableEquipmentRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "userId": {
            "type": "string",
            "nullable": true
          },
          "deliveryItemQty": {
            "type": "integer",
            "format": "int32"
          }
        }
      },
      "UpdateItemRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "sapmaterialNum"
        ],
        "properties": {
          "typeOfUsage": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentValidity": {
            "type": "string",
            "nullable": true
          },
          "equipmentName": {
            "type": "string",
            "nullable": true
          },
          "sapmaterialNum": {
            "type": "string"
          },
          "equipmentMaterialGroup": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "UpdateUserNotificationRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "userId": {
            "type": "string",
            "nullable": true
          },
          "notificationType": {
            "type": "string",
            "nullable": true
          },
          "text": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "User": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "string",
            "nullable": true
          },
          "eln": {
            "type": "string",
            "nullable": true
          },
          "firstName": {
            "type": "string",
            "nullable": true
          },
          "surname": {
            "type": "string",
            "nullable": true
          },
          "familyName": {
            "type": "string",
            "nullable": true
          },
          "fullName": {
            "type": "string",
            "nullable": true
          },
          "displayName": {
            "type": "string",
            "nullable": true
          },
          "iptuname": {
            "type": "string",
            "nullable": true
          },
          "position": {
            "type": "string",
            "nullable": true
          },
          "adaccount": {
            "type": "string",
            "nullable": true
          },
          "email": {
            "type": "string",
            "nullable": true
          },
          "opcode": {
            "type": "string",
            "nullable": true
          },
          "blocked": {
            "type": "integer",
            "format": "int32"
          },
          "dataBlocked": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "blockedByUserId": {
            "type": "string",
            "nullable": true
          },
          "activeFl": {
            "type": "integer",
            "format": "int32"
          },
          "dateInactive": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "isDeleted": {
            "type": "boolean"
          },
          "deletedOn": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "createdOn": {
            "type": "string",
            "format": "date-time"
          },
          "createdBy": {
            "type": "string",
            "nullable": true
          },
          "modifiedOn": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "modifiedBy": {
            "type": "string",
            "nullable": true
          },
          "cityId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "schenkerId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "clientNumber": {
            "type": "string",
            "nullable": true
          },
          "phoneNumber": {
            "type": "string",
            "nullable": true
          },
          "blockedByUser": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/User"
              }
            ]
          },
          "city": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/Cities"
              }
            ]
          },
          "schenker": {
            "nullable": true,
            "allOf": [
              {
                "$ref": "#/components/schemas/Schenkers"
              }
            ]
          },
          "availableEquipment": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/AvailableEquipment"
            }
          },
          "errorUser": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/Error"
            }
          },
          "errorUser2": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/Error"
            }
          },
          "historyFromUser": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/History"
            }
          },
          "historyToUser": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/History"
            }
          },
          "inverseBlockedByUser": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/User"
            }
          },
          "notification": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/Notification"
            }
          },
          "recipientHistoryFromUser": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/RecipientHistory"
            }
          },
          "recipientHistoryToUser": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/RecipientHistory"
            }
          },
          "schenkers": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/Schenkers"
            }
          },
          "statusHistory": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/StatusHistory"
            }
          },
          "substitutionsForUser": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/Substitutions"
            }
          },
          "substitutionsSubstituteUser": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/Substitutions"
            }
          }
        }
      },
      "UserConciseResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "string",
            "nullable": true
          },
          "eln": {
            "type": "string",
            "nullable": true
          },
          "fullName": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "UserItemsToAcceptResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "equipmentName": {
            "type": "string",
            "nullable": true
          },
          "equipmentValidity": {
            "type": "string",
            "nullable": true
          },
          "typeOfUsage": {
            "type": "integer",
            "format": "int32"
          },
          "fromUserId": {
            "type": "string",
            "nullable": true
          },
          "fromUserFullName": {
            "type": "string",
            "nullable": true
          },
          "insertDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "equipmentSerialNum": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "UserItemsToAcceptResponseModelSearchResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "count": {
            "type": "integer",
            "format": "int32"
          },
          "dataCollection": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/UserItemsToAcceptResponseModel"
            }
          }
        }
      },
      "UserItemsToCancelResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "intDeliveryNum": {
            "type": "string",
            "nullable": true
          },
          "equipmentName": {
            "type": "string",
            "nullable": true
          },
          "toUserId": {
            "type": "string",
            "nullable": true
          },
          "toUserFullName": {
            "type": "string",
            "nullable": true
          },
          "insertDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "equipmentSerialNum": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "UserItemsToCancelResponseModelSearchResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "count": {
            "type": "integer",
            "format": "int32"
          },
          "dataCollection": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/UserItemsToCancelResponseModel"
            }
          }
        }
      },
      "UserRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "userId": {
            "type": "string",
            "nullable": true
          },
          "тechnicianName": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "UserResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "string",
            "nullable": true
          },
          "eln": {
            "type": "string",
            "nullable": true
          },
          "displayName": {
            "type": "string",
            "nullable": true
          },
          "iptuName": {
            "type": "string",
            "nullable": true
          },
          "adAccount": {
            "type": "string",
            "nullable": true
          },
          "schenkerId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "blocked": {
            "type": "string",
            "format": "string",
            "enum": [
              "Active",
              "Blocked"
            ],
            "x-ms-enum": {
              "name": "UserStatus",
              "modelAsString": false
            }
          },
          "dataBlocked": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "blockedByUserId": {
            "type": "string",
            "nullable": true
          },
          "hasPendingTransfer": {
            "type": "boolean"
          }
        }
      },
      "UsersWithOpCodeResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "string",
            "nullable": true
          },
          "displayName": {
            "type": "string",
            "nullable": true
          },
          "iptuName": {
            "type": "string",
            "nullable": true
          },
          "opcode": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "ValidationProblemDetails": {
        "type": "object",
        "additionalProperties": {},
        "properties": {
          "type": {
            "type": "string",
            "nullable": true
          },
          "title": {
            "type": "string",
            "nullable": true
          },
          "status": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "detail": {
            "type": "string",
            "nullable": true
          },
          "instance": {
            "type": "string",
            "nullable": true
          },
          "errors": {
            "type": "object",
            "nullable": true,
            "additionalProperties": {
              "type": "array",
              "items": {
                "type": "string"
              }
            }
          }
        }
      },
      "Warehouses": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "schenkers": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/Schenkers"
            }
          }
        }
      },
      "WarehousesResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "type": "string",
            "nullable": true
          }
        }
      }
    }
  }
}