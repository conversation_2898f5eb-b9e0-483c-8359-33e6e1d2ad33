import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useUpdateUser } from "@/hooks/useUsers";
import type {
  CitiesSelectItemResponseModel,
  EditUserRequest,
  SchenkerSelectItemResponseModel,
  SearchUserDataResponseModel,
} from "@/data/etws";
import { DialogDescription, DialogHeader, DialogTitle } from "../ui/dialog";
import { useTranslation } from "react-i18next";

interface UserEditFormProps {
  userId: string;
  user: SearchUserDataResponseModel;
  citiesData: CitiesSelectItemResponseModel[];
  schenkersData: SchenkerSelectItemResponseModel[];
  onClose: () => void;
}

const UserEditForm = ({
  userId,
  user,
  citiesData,
  schenkersData,
  onClose,
}: UserEditFormProps) => {
      const { t } = useTranslation();
    
  const updateUserMutation = useUpdateUser();
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { isSubmitting },
  } = useForm<EditUserRequest>({
    defaultValues: {
      id: userId,
      eln: user?.eln || "",
      firstName: user?.firstName || "",
      surname: user?.surname || "",
      familyName: user?.familyName || "",
      op: user?.op || "",
      cityId: user?.cityId || 0,
      region: user?.region || "",
      clientNumber: user?.clientNumber || "",
      schenkerId: user?.schenkerId || 0,
      userType: user?.isMolOfOp ? "MOL" : "Installer",
      phoneNumber: user?.phoneNumber || "",
    },
  });

  const onSubmit = async (data: EditUserRequest) => {
    await updateUserMutation.mutateAsync(data, {
      onSuccess: () => {
        onClose();
      },
    });
  };

  return (
    <>      <DialogHeader>
        <DialogTitle>{t("userEditForm.title")}</DialogTitle>
        <DialogDescription>
          {t("userEditForm.description")}
        </DialogDescription>
      </DialogHeader><form onSubmit={handleSubmit(onSubmit)} className="space-y-4 p-4 sm:p-6 max-h-[70vh] overflow-y-auto">        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="eln" className="text-sm font-medium">{t("userEditForm.fields.eln")}</Label>
            <Input id="eln" {...register("eln")} disabled className="w-full" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="firstName" className="text-sm font-medium">{t("userEditForm.fields.firstName")}</Label>
            <Input id="firstName" disabled {...register("firstName")} className="w-full" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="surname" className="text-sm font-medium">{t("userEditForm.fields.surname")}</Label>
            <Input id="surname" disabled {...register("surname")} className="w-full" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="familyName" className="text-sm font-medium">{t("userEditForm.fields.familyName")}</Label>
            <Input id="familyName" disabled {...register("familyName")} className="w-full" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="op" className="text-sm font-medium">{t("userEditForm.fields.op")}</Label>
            <Select
              value={watch("schenkerId")?.toString() || ""}
              onValueChange={(value) => {
                const numericValue = value ? parseInt(value, 10) : 0;
                setValue("schenkerId", numericValue);
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t("userEditForm.placeholders.selectOp")} />
              </SelectTrigger>
              <SelectContent>
                {schenkersData?.map((schenker) => (
                  <SelectItem key={schenker.id} value={schenker.id || ""}>
                    {schenker.opCode}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="city" className="text-sm font-medium">{t("userEditForm.fields.city")}</Label>
            <Select
              value={watch("cityId")?.toString() || ""}
              onValueChange={(value) => {
                const numericValue = value ? parseInt(value, 10) : 0;
                setValue("cityId", numericValue);
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t("userEditForm.placeholders.selectCity")} />
              </SelectTrigger>
              <SelectContent>
                {citiesData?.map((city) => (
                  <SelectItem key={city.id} value={city.id?.toString() || ""}>
                    {city.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="region" className="text-sm font-medium">{t("userEditForm.fields.region")}</Label>
            <Input id="region" disabled {...register("region")} className="w-full" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="status" className="text-sm font-medium">{t("userEditForm.fields.molStatus")}</Label>
            <Select
              value={watch("userType") || ""}
              onValueChange={(value) => setValue("userType", value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t("userEditForm.placeholders.selectStatus")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Installer">{t("userEditForm.options.no")}</SelectItem>
                <SelectItem value="MOL">{t("userEditForm.options.yes")}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="clientNumber" className="text-sm font-medium">{t("userEditForm.fields.clientNumber")}</Label>
            <Input id="clientNumber" {...register("clientNumber")} className="w-full" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phoneNumber" className="text-sm font-medium">{t("userEditForm.fields.phoneNumber")}</Label>
            <Input id="phoneNumber" {...register("phoneNumber")} className="w-full" />
          </div>
        </div>        <div className="flex flex-col sm:flex-row justify-end gap-3 pt-4 mt-6 border-t">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onClose}
            className="w-full sm:w-auto order-2 sm:order-1"
          >
            {t("userEditForm.buttons.cancel")}
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || updateUserMutation.isPending}
            className="w-full sm:w-auto order-1 sm:order-2"
          >
            {isSubmitting || updateUserMutation.isPending
              ? t("userEditForm.buttons.saving")
              : t("userEditForm.buttons.save")}
          </Button>
        </div>
      </form>
    </>
  );
};

export default UserEditForm;
