import { useContext, useState, type ChangeEvent, type FormEvent } from "react";
import { AuthContext } from "../components/context/AuthContext";
import { type UserLoginRequestModel } from "../data/etws";
import { useNavigate, useSearchParams } from "react-router";
import { useTranslation } from "react-i18next";
import { AxiosError } from "axios";
import { login } from "../data/query";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

const LoginForm = () => {
  const { t } = useTranslation();

  const { loading, error, dispatch } = useContext(AuthContext);
  const [credentials, setCredentials] = useState<UserLoginRequestModel | undefined>();
  const [loginStatus, setloginStatus] = useState<string>("");

  const handleChange = (e: ChangeEvent<HTMLInputElement>): void => {
    let updatedValue = e.target.value;
    if (e.target.id === "username" && updatedValue.includes("@")) {
      updatedValue = updatedValue.split("@")[0];
    }
    setCredentials((prev) => ({
      ...(prev as UserLoginRequestModel),
      [e.target.id]: updatedValue,
    }));
  };

  const [searchParams] = useSearchParams({ url: "" });
  const historyUrl = searchParams.get("url");
  const navigate = useNavigate();

  const handleClick = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();
    setloginStatus("");

    // Clear any previous auth errors (like session expired)
    if (error) {
      dispatch({ type: "LOGOUT" });
    }

    if (!credentials) return;

    dispatch({ type: "LOGIN_START" });
    try {
      const res = await login(credentials);
      dispatch({ type: "LOGIN_SUCCESS", payload: res.data });

      if (historyUrl) {
        navigate(historyUrl);
      } else {
        navigate("/");
      }
    } catch (err) {
      const error = err as AxiosError;
      if (error.status === 400) {
        setloginStatus(t("loginErrorStatus"));
      }
      
      dispatch({
        type: "LOGIN_FAILURE",
        payload: error.response?.data?.message  ? error.response?.data?.message  : error.message,
      });
    }
  };
  
  return (
    <form onSubmit={handleClick}>
    <Card className="mx-auto max-w-sm min-w-xs">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold">Login</CardTitle>
        <CardDescription>Enter your email and password to login to your account</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              name="username"
              type="text"
              autoComplete="username"
              required
              onChange={handleChange} />
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              required
              onChange={handleChange} />
          </div>
          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? (
              <div
              className="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent rounded-full text-white"
              role="status"
              aria-label="loading"
              ></div>
            ) : (
              "Login"
            )}
          </Button>
        </div>
      </CardContent>
        {(loginStatus || error) && (
          <div className="mt-4">
            <p className="text-red-400 text-sm text-center">
              {error ? t(error) : loginStatus}
            </p>
          </div>
        )}
    </Card>
    </form>
  );
};

export default LoginForm;
