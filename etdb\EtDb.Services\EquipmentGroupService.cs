﻿namespace EtDb.Services
{
    using EtDb.DataHandlers.Interfaces;
    using EtDb.Models.Responses.EquipmentModels;
    using EtDb.Services.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class EquipmentGroupService : IEquipmentGroupService
    {
        private readonly Lazy<IEquipmentGroupDataHandler> equipmentGroupDataHandler;
        private readonly Lazy<IMapper> mapper;

        public EquipmentGroupService(Lazy<IEquipmentGroupDataHandler> equipmentGroupDataHandler, Lazy<IMapper> mapper)
        {
            this.equipmentGroupDataHandler = equipmentGroupDataHandler;
            this.mapper = mapper;
        }

        public async Task<IEnumerable<EquipmentGroupResponseModel>> GetEquipmentGroupsAsync()
        {
            var response = await this.equipmentGroupDataHandler.Value.GetEquipmentGroups().ToListAsync();
            return this.mapper.Value.Map<IEnumerable<EquipmentGroupResponseModel>>(response);
        }

        public async Task<IEnumerable<string>> GetEquipmentGroupsNamesAsync()
        {
            return await this.equipmentGroupDataHandler.Value.GetEquipmentGroupsNames().ToListAsync();
        }

        public async Task<string> GetEquipmentGroupNameAsync(string sapMaterialNum)
        {
            return await this.equipmentGroupDataHandler.Value.GetEquipmentGroupNameAsync(sapMaterialNum);
        }

        public async Task<EquipmentGroupResponseModel> GetEquipmentGroupByIdAsync(int equipmentGroupId)
        {
            var response = await this.equipmentGroupDataHandler.Value.GetEquipmentGroupByIdAsync(equipmentGroupId);
            return this.mapper.Value.Map<EquipmentGroupResponseModel>(response);
        }
    }
}
