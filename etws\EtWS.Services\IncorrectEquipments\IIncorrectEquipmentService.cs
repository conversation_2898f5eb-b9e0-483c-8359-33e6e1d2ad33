﻿namespace EtWS.Services.IncorrectEquipments
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.HistoriyModels;
    using EtWS.Models.Responses.UserModels;

    public interface IIncorrectEquipmentService : IService
    {
        Task<BaseResponseModel> TransferCorrectionAsync(TransferCorrectionRequest request);

        Task<BaseResponseModel> DeleteTransferByHistoryId(DeleteTransferRequest getHistoryRequest);

        Task<IEnumerable<UsersWithOPCodeResponse>> GetUsersWithOPCodesAsync();

        Task<BaseResponseModel> DoesSerialNumberExistAsync(string equipmentSerialNum);

        Task<ICollection<UserDisplayNameResponseModel>> GetUserDisplayName(string prefixnamePrefix);

        Task<BaseResponseModel> CheckServiceId(string serviceId);

        Task<IEnumerable<HistoriesResponse>> GetAllHistoriesByEquipmentSerialNumber(string equipmentSerialnumber);
    }
}
