﻿namespace EtDb.Api.Controllers
{
    using EtDb.Api.Infrastructure.Utils;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Enums;
    using EtDb.DataHandlers.Models;
    using EtDb.Models.Requests;
    using EtDb.Models.Requests.EquipmentModels;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.EquipmentModels;
    using EtDb.Models.Responses.UserModels;
    using EtDb.Services.Interfaces;

    public class EquipmentController : BaseApiController
    {
        private readonly Lazy<IEquipmentService> equipmentService;

        public EquipmentController(Lazy<IEquipmentService> equipmentService)
        {
            this.equipmentService = equipmentService;
        }

        [HttpGet]
        [Route("sapmat-mapp-get")]
        [ProducesResponseType(typeof(List<SapmatMapp>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<List<SapmatMapp>>> GetSapmatMappAsync()
        {
            try
            {
                var result = await this.equipmentService.Value.GetAllSapmatMappAsync();

                if (result != null && result.Any())
                {
                    return this.Ok(result);
                }

                return this.NotFound(new BaseResponseModel { Success = false, Message = "No SAP material mappings found." });
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("search-available-equipment")]
        [ProducesResponseType(typeof(SearchResponseModel<AvailableEquipmentResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<AvailableEquipmentResponseModel>>> SearchAvailableEquipmentAsync([Required] SearchRequestModelWithUserId request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.equipmentService.Value.SearchAvailableEquipmentAsync(request);
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("user-reserved-items/{userId}")]
        [ProducesResponseType(typeof(IEnumerable<AvailableEquipmentResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<AvailableEquipmentResponseModel>>> GetAllUserReservedItemsForTransferAsync(string userId)
        {
            try
            {
                return this.Ok(await this.equipmentService.Value.GetAllUserReservedItemsForTransferAsync(userId));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("reserve-item")]
        [ProducesResponseType(typeof(ReserveItemHistoryResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ReserveItemHistoryResponseModel>> ReserveItemForTransferAsync([Required] ReserveItemForTransferRequestModel request)
        {
            try
            {
                if (request.ItemId != null)
                {
                    return this.Ok(await this.equipmentService.Value.ReserveItemForTransferAsync(request.ItemId.Value, request.UserId, request.Quantity));
                }
                else if (request.ItemSerialNumber != null)
                {
                    return this.Ok(await this.equipmentService.Value.ReserveItemForTransferAsync(request.ItemSerialNumber.Trim(), request.UserId, request.Quantity));
                }

                return this.BadRequest("Please provide either item id or item serial number!");
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("reserve-all-items")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> ReserveAllItemsForTransferAsync([Required] string userId)
        {
            try
            {
                await this.equipmentService.Value.ReserveAllItemsForTransferAsync(userId);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("reserved-items-count/{userId}")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> CountAllUserReservedItemsForTransfer([Required] string userId)
        {
            try
            {
                return this.Ok(await this.equipmentService.Value.CountAllUserReservedItemsForTransfer(userId));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Removes item from transfer.
        /// </summary>
        /// <param name="request">The item id and the user id.</param>
        /// <returns>The items equipmentType name.</returns>
        [HttpPost("remove-item")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<string>> RemoveItemForTransferAsync([Required] RemoveItemForTransferRequestModel request)
        {
            try
            {
                return this.Ok(await this.equipmentService.Value.RemoveItemAsync(request.ItemId, request.UserId));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("remove-items")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> RemoveItemsForTransferAsync([Required] RemoveItemsForTransferRequestModel request)
        {
            try
            {
                await this.equipmentService.Value.RemoveItemsAsync(request.SelectedItems, request.UserId);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("remove-all-items")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> RemoveAllItemsForTransferAsync([Required] string userId)
        {
            try
            {
                await this.equipmentService.Value.RemoveAllItemsAsync(userId);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPut("available-equipments")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateAvailableEquipmentsAsync([Required] IList<AvailableEquipmentRequestModel> request)
        {
            try
            {
                await this.equipmentService.Value.UpdateAvailableEquipmentsAsync(request);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("search-transfer-data")]
        [ProducesResponseType(typeof(SearchResponseModel<ItemResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<ItemResponseModel>>> SearchTransferDataAsync([Required] SearchRequestModelWithUserId request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.equipmentService.Value.SearchTransferDataAsync(request);
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("search-daily-equipment")]
        [ProducesResponseType(typeof(SearchResponseModel<AvailableEquipmentResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<AvailableEquipmentResponseModel>>> SearchDailyEquipmentAsync([Required] SearchRequestModelWithUserId request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.equipmentService.Value.SearchDailyEquipmentAsync(request);
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("deliver-items")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> DeliverItemsAsync(DeliverItemsRequestModel request)
        {
            try
            {
                await this.equipmentService.Value.DeliverItemsAsync(request);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("post-offices-select-list")]
        [ProducesResponseType(typeof(IEnumerable<PostOfficesSelectListResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<PostOfficesSelectListResponseModel>>> GetAllActivePostOfficesAsync()
        {
            try
            {
                return this.Ok(await this.equipmentService.Value.GetAllActivePostOfficesAsync());
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("search-user-items-to-accept")]
        [ProducesResponseType(typeof(SearchResponseModel<UserItemsToAcceptResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<UserItemsToAcceptResponseModel>>> SearchUserItemsToAcceptDataAsync([Required] SearchRequestModelWithUserId request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.equipmentService.Value.SearchUserItemsToAcceptDataAsync(request);
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("search-user-items-to-cancel")]
        [ProducesResponseType(typeof(SearchResponseModel<UserItemsToCancelResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<UserItemsToCancelResponseModel>>> SearchUserItemsToCancelDataAsync([Required] SearchRequestModelWithUserId request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.equipmentService.Value.SearchUserItemsToCancelDataAsync(request);
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("refuse-items")]
        [ProducesResponseType(typeof(IDictionary<string, TransferedItemsModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IDictionary<string, TransferedItemsModel>>> RefuseItemsAsync([Required] RefuseItemsRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var refuseReasonValid = Enum.TryParse(request.RefuseReason, true, out RefuseReasonsList refuseReasonEnum);

                    var result = await this.equipmentService.Value.RefuseItemsAsync(request.SelectedItems, refuseReasonValid ? refuseReasonEnum : null, request.UserId);
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("accept-items")]
        [ProducesResponseType(typeof(IDictionary<string, TransferedItemsModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IDictionary<string, TransferedItemsModel>>> AcceptItemsAsync([Required] AcceptItemsRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.equipmentService.Value.AcceptItemsAsync(request.SelectedItems, request.UserId);
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("cancel-items")]
        [ProducesResponseType(typeof(IDictionary<string, TransferedItemsModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IDictionary<string, TransferedItemsModel>>> CancelItemsAsync([Required] CancelItemsRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var refuseReasonValid = Enum.TryParse(request.RefuseReason, true, out RefuseReasonsList refuseReasonEnum);

                    var result = await this.equipmentService.Value.CancelItemsAsync(request.SelectedItems, refuseReasonValid ? refuseReasonEnum : null, request.UserId);
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
