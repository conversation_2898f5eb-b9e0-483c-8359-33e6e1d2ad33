﻿namespace EtWS.Services.Interfaces
{
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.SchenkerModels;
    using EtWS.Models.Responses.UserModels;

    public interface ISchenkersService : IService
    {
        Task<SchenkerResponseModel> GetSchenkerByIdAsync(int id);

        Task<IEnumerable<UserDataConciseResponseModel>> GetSchenkerUsersAsync(int schenkerId);

        Task<IEnumerable<SchenkerSelectItemResponseModel>> GetSchenkerOpCodesListAsync();

        Task<SearchResponseModel<SchenkerResponseModel>> SearchSchenkersAsync(SearchDataRequestModel request);

        Task<int> AddSchenkerAsync(SchenkerRequestModel request);

        Task UpdateSchenkerAsync(SchenkerRequestModel request);
    }
}
