import { use<PERSON><PERSON>, FormProvider } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useTranslation } from "react-i18next";
import { useState, lazy, Suspense } from "react";
import { CheckCircle, Info, XCircle } from "lucide-react";
import SerialNumberInput from "./SerialNumberInput";
import TransferToInput from "./TransferToInput";
import { useDeleteTransfer, useSubmitCorrection } from "../../data/query";
import type { DeleteTransferRequest } from "@/data/etws";
import { Button } from "../ui/button";
import { Alert, AlertDescription } from "../ui/alert";

// Lazy load components
const Technician = lazy(() => import("./Technician"));
const ServiceId = lazy(() => import("./ServiceId"));
const Op = lazy(() => import("./Op"));
const Remove = lazy(() => import("./Remove"));

// Define the schema with conditional validation

interface FormData {
  equipmentSerialNum: string;
  transferTo: string;
  deliveryShop: string;
  nameOfTechnician: string;
  serviceId: string;
  userId: string;
  deleteId: string;
  deleteItemId: string;
}

const Correction = () => {
  const { t } = useTranslation();
  const schema = yup.object().shape({
    equipmentSerialNum: yup.string().required(t("serialNumberRequired")),
    transferTo: yup.string().required("transferToRequired"),
    nameOfTechnician: yup
      .string()
      .when("transferTo", {
        is: "technician",
        then: (schema) => schema.required("technicianNameRequired"),
        otherwise: (schema) => schema.notRequired(),
      })
      .required("technicianNameRequired"),
    serviceId: yup
      .string()
      .when("transferTo", {
        is: "client",
        then: (schema) => schema.required("serviceIdRequired"),
        otherwise: (schema) => schema.notRequired(),
      })
      .required("serviceIdRequired"),
    userId: yup
      .string()
      .when("transferTo", {
        is: "op",
        then: (schema) => schema.required("opIdRequired"),
        otherwise: (schema) => schema.notRequired(),
      })
      .required("opIdRequired"),
    deliveryShop: yup
      .string()
      .when("transferTo", {
        is: "op",
        then: (schema) => schema.required("deliveryShopRequired"),
        otherwise: (schema) => schema.notRequired(),
      })
      .required("opIdRequired"),
    deleteId: yup
      .string()
      .when("transferTo", {
        is: "remove",
        then: (schema) => schema.required("deleteIdDateRequired"),
        otherwise: (schema) => schema.notRequired(),
      })
      .required("deleteIdDateRequired"),
    deleteItemId: yup
      .string()
      .when("transferTo", {
        is: "remove",
        then: (schema) => schema.required("deleteItemIdDateRequired"),
        otherwise: (schema) => schema.notRequired(),
      })
      .required("deleteItemIdDateRequired"),
  });
  const [isSerialNumberValid, setIsSerialNumberValid] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState<boolean | null>(null);
  const { mutate, isPending: isSubmitting } = useSubmitCorrection();
  const { mutate: deleteDate, isPending } = useDeleteTransfer();

  const methods = useForm<FormData>({
    resolver: yupResolver(schema),
    mode: "onChange",
  });

  const { handleSubmit, watch } = methods;
  const transferToInput = watch("transferTo");

  const onSubmit = async (data: FormData) => {
    if (transferToInput == "remove") {
      const req: DeleteTransferRequest = {
        id: Number(data.deleteId),
        itemId: Number(data.deleteItemId),
      };
      deleteDate(req, {
        onSuccess: () => {
          setSubmitSuccess(true);
          methods.reset();
        },
        onError: () => {
          setSubmitSuccess(false);
        },
      });
      return;
    } else {
      mutate(data, {
        onSuccess: () => {
          setSubmitSuccess(true);
          methods.reset();
        },
        onError: () => {
          setSubmitSuccess(false);
        },
      });
    }
  };

  return (
    <div className="flex items-center justify-center p-4 min-h-[calc(100vh-65px)]">
      <div className="w-full max-w-md overflow-hidden rounded-2xl shadow-xl border">
        <div className="bg-primary-foreground px-8 py-5">
          <h2 className="text-xl font-bold ">{t("transferCorrection")}</h2>
        </div>{" "}
        <div className="px-8 py-6">
          {submitSuccess === true && (
            <Alert className="mb-6 border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-700">
                {t("transferSuccessful")}
              </AlertDescription>
            </Alert>
          )}

          {submitSuccess === false && (
            <Alert variant="destructive" className="mb-6">
              <XCircle className="h-4 w-4" />
              <AlertDescription>{t("transferFailed")}</AlertDescription>
            </Alert>
          )}

          <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
              <SerialNumberInput
                label={t("serialNumber")}
                name="equipmentSerialNum"
                onValidationChange={setIsSerialNumberValid}
              />

              {isSerialNumberValid && (
                <TransferToInput label={t("transferTo")} name="transferTo" />
              )}

              <Suspense fallback={<div>{t("loading")}</div>}>
                {transferToInput === "technician" && <Technician />}
                {transferToInput === "client" && <ServiceId />}
                {transferToInput === "centralWarehouse" && (
                  <Alert className="border text-blue-600 dark:text-blue-500">
                    <Info />
                    <AlertDescription className="text-blue-600 dark:text-blue-500">
                      {t("transferToCentralWarehouse")}
                    </AlertDescription>
                  </Alert>
                )}
                {transferToInput === "SAPVirtualWarehouse" && (
                  <Alert className="border text-blue-600 dark:text-blue-500">
                    <Info />
                    <AlertDescription className="text-blue-600 dark:text-blue-500">
                      {t("transferSAPVirtualWarehouse")}
                    </AlertDescription>
                  </Alert>
                )}
                {transferToInput === "op" && <Op />}
                {transferToInput === "remove" && <Remove />}
              </Suspense>

              {transferToInput && (
                <Button
                  type="submit"
                  disabled={isSubmitting || isPending}
                  className={`w-full rounded-lg px-4 py-3 text-sm font-semibold transition-all 
                    ${
                      isSubmitting || isPending
                        ? "cursor-not-allowed"
                        : "focus:ring-4 focus:ring-indigo-500/20"
                    }`}
                >
                  {isSubmitting || isPending ? (
                    <div className="flex items-center justify-center">
                      <div className="h-4 w-4 animate-spin rounded-full border-2  border-t-transparent mr-2"></div>
                      {t("processing")}
                    </div>
                  ) : (
                    t("submit")
                  )}
                </Button>
              )}
            </form>
          </FormProvider>
        </div>
      </div>
    </div>
  );
};

export default Correction;
