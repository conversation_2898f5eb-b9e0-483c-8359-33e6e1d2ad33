import { useContext, useMemo } from "react";
import { AuthContext } from "../context/AuthContext";
import { UserRoles } from "../../utils/userRoles";

export const useRoles = () => {
  const { user } = useContext(AuthContext);

  const IsInRole = useMemo(
    () => (role: string) => {
      return user?.userRoles?.includes(role) ?? false;
    },
    [user?.userRoles]
  );

  const hasRequiredRole = (requiredRoles: UserRoles[]) => {
    return requiredRoles.some((role) => IsInRole(role));
  };

  return { IsInRole, hasRequiredRole };
};
