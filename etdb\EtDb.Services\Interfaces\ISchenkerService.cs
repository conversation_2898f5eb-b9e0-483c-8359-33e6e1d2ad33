﻿namespace EtDb.Services.Interfaces
{
    using EtDb.Models.Requests;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.UserModels;

    public interface ISchenkerService : IService
    {
        Task<SchenkersResponseModel> GetSchenkerById(int id);

        Task<IEnumerable<UserConciseResponseModel>> GetSchenkerUsers(int schenkerId);

        Task<List<SchenkerOpCodeResponseModel>> GetSchenkerOpCodesList();

        Task<SearchResponseModel<SchenkersResponseModel>> SearchSchenkers(SearchRequestModel request);

        Task<int> InsertSchenker(SchenkersRequestModel request);

        Task UpdateSchenker(SchenkersRequestModel request);
    }
}
