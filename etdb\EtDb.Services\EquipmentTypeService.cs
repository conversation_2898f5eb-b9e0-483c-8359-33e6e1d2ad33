﻿namespace EtDb.Services
{
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using EtDb.Infrastructure.Enumerations;
    using EtDb.Models.Responses.EquipmentModels;
    using EtDb.Services.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class EquipmentTypeService : IEquipmentTypeService
    {
        private readonly Lazy<IEquipmentTypeDataHandler> equipmentGroupDataHandler;
        private readonly Lazy<IMapper> mapper;

        public EquipmentTypeService(Lazy<IEquipmentTypeDataHandler> equipmentGroupDataHandler, Lazy<IMapper> mapper)
        {
            this.equipmentGroupDataHandler = equipmentGroupDataHandler;
            this.mapper = mapper;
        }

        public async Task<EquipmentTypeResponseModel> GetEquipmentTypeByIdAsync(int id)
        {
            var response = await this.equipmentGroupDataHandler.Value.GetEquipmentTypeByIdAsync(id);
            return this.mapper.Value.Map<EquipmentTypeResponseModel>(response);
        }

        public async Task<IEnumerable<EquipmentTypeResponseModel>> GetEquipmentTypesAsync()
        {
            var response = await this.equipmentGroupDataHandler.Value.GetEquipmentTypes().ToListAsync();
            return this.mapper.Value.Map<IEnumerable<EquipmentTypeResponseModel>>(response);
        }

        public async Task<int> GetEquipmentTypeIdAsync(string sapMaterialNum)
        {
           return await this.equipmentGroupDataHandler.Value.GetEquipmentTypeIdAsync(sapMaterialNum);
        }

        public async Task<IEnumerable<EquipmentTypeResponseModel>> GetEquipmentTypesAsync(SendMethod sendMethod)
        {
            var response = await this.equipmentGroupDataHandler.Value.GetEquipmentTypesAsync(sendMethod);
            return this.mapper.Value.Map<IEnumerable<EquipmentTypeResponseModel>>(response);
        }

        public async Task<IEnumerable<EquipmentTypeConciseDto>> GetConciseEquipmentTypesAsync(IEnumerable<int> equipmentTypeIds, bool serialNumbersRequired)
        {
            return await Task.FromResult(this.equipmentGroupDataHandler.Value.GetConciseEquipmentTypes(equipmentTypeIds, serialNumbersRequired).ToList());
        }

        public async Task<IDictionary<string, int>> GetEquipmentTypesLastMonthQuantitiesAsync()
        {
            return await this.equipmentGroupDataHandler.Value.GetEquipmentTypesLastMonthQuantitiesAsync();
        }
    }
}
