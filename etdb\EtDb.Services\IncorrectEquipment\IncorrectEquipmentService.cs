﻿namespace EtDb.Services.IncorrectEquipment
{
    using System.Collections.Generic;
    using System.Linq;

    using EtDb.ApiClients.EtDb.Context;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.Models.Requests;
    using EtDb.Models.Requests.EquipmentModels;
    using EtDb.Models.Responses.UserModels;
    using EtDb.Services.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class IncorrectEquipmentService : IIncorrectEquipmentService
    {
        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<EtDbContext> dbContext;

        public IncorrectEquipmentService(Lazy<EtDbContext> dbContext, Lazy<IMapper> mapper)
        {
            this.dbContext = dbContext;
            this.mapper = mapper;
        }

        public async Task<int?> GetItemIdBySerialNumberAsync(string equipmentSerialNum)
        {
            return await this.dbContext.Value.Items
                .AsNoTracking()
                .Where(e => e.EquipmentSerialNum == equipmentSerialNum)
                .OrderByDescending(e => e.AdditionDate)
                .Select(e => e.Id)
                .FirstOrDefaultAsync();
        }

        public async Task UpdateItemAdditionDateAsync(int itemId)
        {
            var currentDate = DateTime.Now;

            var itemToUpdate = await this.dbContext.Value.Items
                .FirstOrDefaultAsync(item => item.Id == itemId);

            if (itemToUpdate != null)
            {
                itemToUpdate.AdditionDate = currentDate;
                await this.dbContext.Value.SaveChangesAsync();
            }
            else
            {
                throw new ArgumentException($"No item found with the provided Id: {itemId}.");
            }
        }

        public async Task<IEnumerable<History>> GetAllHistoriesByItemId(int itemId)
        {
            var histories = await this.dbContext.Value.Histories
                .AsNoTracking()
                .Where(h => h.ItemId == itemId)
                .OrderByDescending(h => h.InsertDate)
                .ToListAsync();

            return histories;
        }

        public async Task<int> AddHistoryAsync(HistoryRequestModel request)
        {
            var history = this.mapper.Value.Map<History>(request);

            await this.dbContext.Value.Histories.AddAsync(history);

            await this.dbContext.Value.SaveChangesAsync();

            return history.Id;
        }

        public async Task<bool> DeleteHistoryByIdAsync(int id)
        {
            var history = await this.dbContext.Value.Histories
                .FirstOrDefaultAsync(h => h.Id == id);

            if (history == null)
            {
                return false;
            }

            this.dbContext.Value.Histories.Remove(history);
            await this.dbContext.Value.SaveChangesAsync();

            return true;
        }

        public async Task<bool> UpdateAvailableEquipmentById(AvailableEquipmentRequestModel request)
        {
            var availableEquipment = await this.dbContext.Value.AvailableEquipments
                .FirstOrDefaultAsync(a => a.ItemId == request.ItemId && a.UserId == request.UserId);

            if (availableEquipment == null)
            {
                return false;
            }

            availableEquipment.ItemQuantity = request.ItemQuantity;

            await this.dbContext.Value.SaveChangesAsync();

            return true;
        }

        public async Task AddStatusHistoryAsync(StatusHistoryRequestModel request)
        {
            var statusHistory = this.mapper.Value.Map<StatusHistory>(request);

            await this.dbContext.Value.StatusHistories.AddAsync(statusHistory);

            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task<bool> DeleteStatusHistoryAsync(int historyId)
        {
            var statusHistories = await this.dbContext.Value.StatusHistories
                .AsNoTracking()
                .Where(sh => sh.HistoryId == historyId)
                .ToListAsync();

            if (statusHistories == null || !statusHistories.Any())
            {
                return false;
            }

            this.dbContext.Value.StatusHistories.RemoveRange(statusHistories);

            await this.dbContext.Value.SaveChangesAsync();

            return true;
        }

        public async Task<bool> DeleteErrorsByHistoryIdAsync(int historyId)
        {
            var errors = await this.dbContext.Value.Errors
                .AsNoTracking()
                .Where(e => e.HistoryId == historyId)
                .ToListAsync();

            if (errors == null || !errors.Any())
            {
                return false;
            }

            this.dbContext.Value.Errors.RemoveRange(errors);

            await this.dbContext.Value.SaveChangesAsync();

            return true;
        }

        public async Task<string> GetTechnicianIdByNameAsync(UserRequestModel request)
        {
            var iptuName = await this.dbContext.Value.Users
                .AsNoTracking()
                .Where(t => t.Id == request.UserId)
                .Select(t => t.Iptuname)
                .FirstOrDefaultAsync();

            var technicianId = await this.dbContext.Value.Users
                .AsNoTracking()
                .Where(t => t.DisplayName.ToLower() == request.ТechnicianName.ToLower() && t.Iptuname.ToLower() == iptuName.ToLower())
                .Select(t => t.Id)
                .FirstOrDefaultAsync();

            return technicianId;
        }

        public async Task<IEnumerable<UsersWithOpCodeResponseModel>> GetAllUsersWithOpCodeAsync()
        {
            var users = await this.dbContext.Value.Users
                .Where(u => u.Opcode.StartsWith("OP"))
                .ToListAsync();

            var result = this.mapper.Value.Map<List<UsersWithOpCodeResponseModel>>(users);

            return result;
        }

        public async Task<int?> GetAvailableEquipmentIdByTechnicianNameAndItemIdAsync(AvailableEquipmentRequestModel request)
        {
            if (request == null || request.ItemId == 0 || string.IsNullOrEmpty(request.UserId))
            {
                throw new ArgumentException("Invalid request data.");
            }

            var availableEquipmentId = await this.dbContext.Value.AvailableEquipments
                .Where(e => e.ItemId == request.ItemId && e.UserId == request.UserId)
                .Select(e => e.Id)
                .FirstOrDefaultAsync();

            return availableEquipmentId != 0 ? availableEquipmentId : (int?)null;
        }

        public async Task AddAvailableEquipmentAsync(AvailableEquipmentRequestModel request)
        {
            var availableEquipment = this.mapper.Value.Map<AvailableEquipment>(request);

            await this.dbContext.Value.AvailableEquipments.AddAsync(availableEquipment);

            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task<bool> UpdateAvailableEquipmentToZeroAsync(int itemId)
        {
            var availableItems = await this.dbContext.Value.AvailableEquipments
                .Where(i => i.ItemId == itemId && i.ItemQuantity == 1)
                .ToListAsync();

            if (!availableItems.Any())
            {
                return false;
            }

            availableItems.ForEach(item => item.ItemQuantity = 0);
            await this.dbContext.Value.SaveChangesAsync();

            return true;
        }

        public async Task<bool> DoesSerialNumberExistAsync(string equipmentSerialNum)
        {
            return await this.dbContext.Value.Items
                .AsNoTracking()
                .AnyAsync(e => e.EquipmentSerialNum == equipmentSerialNum);
        }

        public async Task<IEnumerable<UserResponseModel>> GetUserDisplayAndIptuNames()
        {
            var customIptuNames = GlobalConstants.IPTUCustomNames
                .Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(name => name.Trim())
                .ToList();

            var result = await this.dbContext.Value.Users
                .AsNoTracking()
                .Where(u => u.Iptuname.StartsWith("ИПТУ") || customIptuNames.Contains(u.Iptuname.Trim()))
                .OrderBy(u => u.DisplayName)
                .Select(u => new UserResponseModel
                {
                    Id = u.Id,
                    DisplayName = u.DisplayName,
                    IPTUName = u.Iptuname,
                    Eln = u.Eln,
                })
                .ToListAsync();

            return result;
        }
    }
}
