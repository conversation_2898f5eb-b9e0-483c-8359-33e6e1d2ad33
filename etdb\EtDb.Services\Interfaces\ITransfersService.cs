﻿namespace EtDb.Services.Interfaces
{
    using ET.Database.Enums;
    using ET.DataHandlers.Models;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Models;
    using EtDb.Models.Requests.TransferModels;

    public interface ITransfersService : IService
    {
        IDictionary<string, IEnumerable<TransferListItemWithDate>> GetTransferListItemsByOpWithinDateRange(DateTime fromDate, DateTime toDate, TransferStatus status);

        Task<FilteredDataModel<Transfers>> GetTransfersGridData(TransfersGridRequestModel model);

        Task<IList<TransferListItems>> GetTransferListItemsBySendMethodAsync(int transferId, bool bRProject);
    }
}