﻿namespace EtDb.Services.Interfaces
{
    using EtDb.Models.Responses.EquipmentModels;

    public interface IEquipmentGroupService : IService
    {
        Task<IEnumerable<EquipmentGroupResponseModel>> GetEquipmentGroupsAsync();

        Task<IEnumerable<string>> GetEquipmentGroupsNamesAsync();

        Task<string> GetEquipmentGroupNameAsync(string sapMaterialNum);

        Task<EquipmentGroupResponseModel> GetEquipmentGroupByIdAsync(int equipmentGroupId);
    }
}
