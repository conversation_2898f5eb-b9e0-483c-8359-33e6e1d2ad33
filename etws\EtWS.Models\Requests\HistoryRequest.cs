﻿namespace EtWS.Models.Requests
{
    public class HistoryRequest
    {
        public int ItemId { get; set; }

        public string IcmIdSgwId { get; set; }

        public string SourceSystem { get; set; }

        public string DeliveryType { get; set; }

        public string IntDeliveryNum { get; set; } = Guid.NewGuid().ToString();

        public string DelivelyFromPoint { get; set; }

        public string DeliveryShop { get; set; }

        public string ToUserId { get; set; }

        public string FromUserId { get; set; }

        public DateTime? DeliveryDateSap { get; set; } = DateTime.UtcNow;

        public int DeliveryItemQty { get; set; }

        public DateTime? InsertDate { get; set; } = DateTime.UtcNow;

        public int OperationType { get; set; }

        public int DocStatus { get; set; }

        public string ServiceIdTo { get; set; }

        public int? AutoGenerated { get; set; }

        public int ItemTypeOfUsage { get; set; }

        public string ItemValidity { get; set; }

        public string CrmorderId { get; set; }
    }
}
