import { useContext } from "react";
import { useLocation, Navigate, Outlet } from "react-router";
import { AuthContext } from "../context/AuthContext";

export default function RequireAuth() {
  const location = useLocation();
  const { user } = useContext(AuthContext);

  const content = user ? <Outlet /> : <Navigate to={`/login?url=${encodeURIComponent(location.pathname + location.search)}`} replace />;
  return content;
}
