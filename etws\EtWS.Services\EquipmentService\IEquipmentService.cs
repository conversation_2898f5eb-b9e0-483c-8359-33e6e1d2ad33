﻿namespace EtWS.Services.EquipmentService
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.EquipmentModels;

    public interface IEquipmentService : IService
    {
        Task<SearchResponseModel<AvailableEquipmentDataResponseModel>> SearchAvailableEquipmentAsync(SearchDataRequestModel request, string userId);

        Task<ReserveItemHistoryDataResponseModel> ReserveItemForTransferAsync(ReserveItemForTransferDataRequestModel request, string userId);

        Task<int> ReserveAllItemsForTransferAsync(string userId);

        Task<int> CountAllUserReservedItemsForTransfer(string userId);

        Task<RemoveItemFromTransferResponseModel> RemoveItemFromTransferAsync(int itemId, string userId);

        Task RemoveItemsFromTransferAsync(IEnumerable<int> selectedItems, string userId);

        Task<int> RemoveAllItemsFromTransferAsync(string userId);

        Task<SearchResponseModel<ItemDataResponseModel>> SearchTransferDataAsync(SearchDataRequestModel request, string userId);

        Task<IEnumerable<PostOfficesSelectListResponseModel>> GetAllActivePostOfficesAsync();

        Task DeliverItemsAsync(DeliverItemsDataRequestModel request, string userId);

        Task<SearchResponseModel<UserItemsToAcceptResponseModel>> SearchUserItemsToAcceptDataAsync(SearchDataRequestModel request, string userId);

        Task<SearchResponseModel<UserItemsToCancelResponseModel>> SearchUserItemsToCancelDataAsync(SearchDataRequestModel request, string userId);

        Task AcceptItemsAsync(AcceptItemsForTransferRequestModel request, string userId);

        Task RefuseItemsAsync(RefuseItemsForTransferRequestModel request, string userId);

        Task CancelItemsAsync(CancelItemsForTransferRequestModel request, string userId);
    }
}
