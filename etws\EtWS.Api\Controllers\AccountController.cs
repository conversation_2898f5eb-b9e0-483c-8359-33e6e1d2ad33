﻿namespace EtWS.Api.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using EtWS.Api.Infrastructure.Utils;
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.UserModels;
    using EtWS.Services.SubstitutionsService;
    using EtWS.Services.UserManagerService;
    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Authentication.Cookies;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using Net.Ams.Interfaces;
    using Net.Ams.Models;
    using Net.Common.Mvc;
    using Newtonsoft.Json;

    public class AccountController : BaseApiController
    {
        private readonly IUserManager userManager;
        private readonly ISubstitutionsService substitutionsService;
        private readonly IUserManagerService userManagerService;

        public AccountController(IUserManager userManager, ISubstitutionsService substitutionsService, IUserManagerService userManagerService)
        {
            this.userManager = userManager;
            this.substitutionsService = substitutionsService;
            this.userManagerService = userManagerService;
        }

        [AllowAnonymous]
        [HttpPost("login")]
        [ProducesResponseType(typeof(UserLoginResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<UserLoginResponseModel>> AuthenticateAsync([Required] UserLoginRequestModel request)
        {
            if (request == null || !this.ModelState.IsValid)
            {
                var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
            }

            try
            {
                request.Username = this.userManager.GetUsername(request.Username);

                if (this.userManager.IsUserAuthenticated(request.Username, request.Password))
                {
                    ApplicationUserModel user = this.userManager.GetProfileInfo(request.Username);
                    var profilePicture = this.userManager.GetProfilePicture(request.Username, "/");

                    ClaimsIdentity identity = await this.GenerateClaims(user);
                    var claimsPrincipal = new ClaimsPrincipal(identity);
                    await this.HttpContext.SignInAsync(
                        CookieAuthenticationDefaults.AuthenticationScheme,
                        claimsPrincipal);

                    UserLoginResponseModel result = new ()
                    {
                        Username = user.Username,
                        UserRoles = user.UserRoles,
                        ProfilePicture = profilePicture,
                    };

                    return this.Ok(result);
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }

            return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = GlobalConstants.WrongPasswordOrUsernameMessage });
        }

        [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
        [HttpPost("logout")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> LogoutAsync()
        {
            try
            {
                await this.HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
                return this.Ok(ResponseHandler.CreateResponse());
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
        [HttpGet("set-impersonation-cookie")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        public async Task<ActionResult> SetImpersonationCookie()
        {
            try
            {
                var activeSubstitution = this.substitutionsService.GetActiveSubstitution((await this.userManagerService.GetCurrentUserAsync()).Id);

                var impersonatedUsername = await this.userManagerService.GetUserADAccountAsync(activeSubstitution.ForUserId);
                var substituteUsername = await this.userManagerService.GetUserADAccountAsync(activeSubstitution.SubstituteUserId);

                var keyValues = new Dictionary<string, string>
                {
                    { CookieConstants.ImpersonatedUser, impersonatedUsername },
                    { CookieConstants.SubstituteUser, substituteUsername },
                };

                // TODO: determine if the cookie must be encrypted?
                this.Response.Cookies.Append(CookieConstants.SubstitutionCookie, JsonConvert.SerializeObject(keyValues), new CookieOptions
                {
                    Expires = activeSubstitution.ToDate,
                });

                return this.Ok(ResponseHandler.CreateResponse());
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
        [HttpGet("delete-impersonation-cookie")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        public ActionResult DeleteImpersonationCookie()
        {
            try
            {
                this.Response.Cookies.Delete(CookieConstants.SubstitutionCookie);
                return this.Ok(ResponseHandler.CreateResponse());
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        private async Task<ClaimsIdentity> GenerateClaims(ApplicationUserModel user)
        {
            ClaimsIdentity identity = new (new List<Claim>(), CookieAuthenticationDefaults.AuthenticationScheme);
            identity.AddClaim(new Claim(ClaimTypes.Name, user.Username));
            identity.AddClaim(new Claim(ClaimTypes.GivenName, user.DisplayName));

            var dbUser = await this.TryGetUserOrNull(user.Username);
            if (dbUser != null)
            {
                identity.AddClaim(new Claim(ClaimConstants.UserIdClaimType, dbUser.Id));
                identity.AddClaim(new Claim(ClaimConstants.UserElnClaimType, dbUser.Eln));
                if (dbUser.SchenkerId != null)
                {
                    identity.AddClaim(new Claim(ClaimConstants.UserSchenkerIdClaimType, dbUser.SchenkerId.Value.ToString()));
                }
            }

            IEnumerable<Claim> roleClaims = user.UserRoles?
                .Select(x => new Claim(ClaimTypes.Role, x));

            if (roleClaims != null && roleClaims.Any())
            {
                identity.AddClaims(roleClaims);
            }

            return identity;
        }

        private async Task<SearchUserDataResponseModel> TryGetUserOrNull(string username)
        {
            var dbUser = await this.userManagerService.GetUserByUsernameAsync(username);

            return dbUser ?? null;
        }
    }
}
