﻿namespace EtWS.Services.UserManagerService
{
    using System.Text;

    using EtWS.ApiClients.ETDB;
    using EtWS.Infrastructure.Enumerations;
    using EtWS.Infrastructure.Models.Infrastructure;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.UserModels;
    using EtWS.Services.SubstitutionsService;
    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.Caching.Distributed;
    using Microsoft.Extensions.Options;
    using Net.Ams.Interfaces;
    using Newtonsoft.Json;

    public class UserManagerService : IUserManagerService
    {
        private readonly IMapper mapper;
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly IUserManager userManager;
        private readonly IETDB etDb;
        private readonly ISubstitutionsService substitutionsService;
        private readonly IDistributedCache distributedCache;
        private readonly string subcontractors;
        private readonly string iptuCustomNames;

        public UserManagerService(
            IMapper mapper,
            IHttpContextAccessor httpContextAccessor,
            IUserManager userManager,
            IETDB etDb,
            ISubstitutionsService substitutionsService,
            IDistributedCache distributedCache,
            IOptionsSnapshot<AppSettings> options)
        {
            this.mapper = mapper;
            this.httpContextAccessor = httpContextAccessor;
            this.userManager = userManager;
            this.etDb = etDb;
            this.substitutionsService = substitutionsService;
            this.distributedCache = distributedCache;
            this.subcontractors = options.Value.Subcontractors;
            this.iptuCustomNames = options.Value.IPTUCustomNames;
        }

        public async Task<SearchUserDataResponseModel> GetCurrentUserAsync()
        {
            var currentUserIdentityName = this.httpContextAccessor.HttpContext.User.Identity.Name;
            if (this.httpContextAccessor.HttpContext.Request.Cookies.TryGetValue(CookieConstants.SubstitutionCookie, out string substitutionCookie))
            {
                var substitutionData = DeserializeImpersonatedCookie(substitutionCookie);
                string impersonatedUserAd = substitutionData.ContainsKey(CookieConstants.ImpersonatedUser)
                    ? substitutionData[CookieConstants.ImpersonatedUser]
                    : null;
                string substituteUserAd = substitutionData.ContainsKey(CookieConstants.SubstituteUser)
                    ? substitutionData[CookieConstants.SubstituteUser]
                    : null;

                if (!string.IsNullOrWhiteSpace(impersonatedUserAd) &&
                    !string.IsNullOrWhiteSpace(substituteUserAd) &&
                    string.Equals(substituteUserAd, currentUserIdentityName, StringComparison.CurrentCultureIgnoreCase))
                {
                    return await this.GetUserByUsernameAsync(impersonatedUserAd);
                }
            }

            return await this.GetUserByUsernameAsync(currentUserIdentityName);
        }

        public async Task<SearchUserDataResponseModel> GetUserByUserIdAsync(string userId)
        {
            var cachedUser = await this.distributedCache.GetStringAsync($"{CacheConstants.EtWsUsersCacheKey}{userId}");
            if (cachedUser != null)
            {
                return JsonConvert.DeserializeObject<SearchUserDataResponseModel>(cachedUser);
            }

            var currentUser = await this.etDb.ApiUsersByUserIdGetAsync(userId);
            if (currentUser == null)
            {
                throw new NullReferenceException($"User with id: {userId}, can`t be found in the database!");
            }

            var result = this.mapper.Map<SearchUserDataResponseModel>(currentUser);

            await this.CacheUserByUserId(userId, result);

            return result;
        }

        public async Task<SearchUserDataResponseModel> GetUserByUsernameAsync(string username)
        {
            // TODO: cache user by username
            var currentUser = await this.etDb.ApiUsersUserByUsernameByUsernameGetAsync(username);
            if (currentUser == null)
            {
                throw new NullReferenceException($"Username: {username} can`t be found in the database!");
            }

            return this.mapper.Map<SearchUserDataResponseModel>(currentUser);
        }

        public async Task<string> GetUserADAccountAsync(string userId)
        {
            if (userId == null)
            {
                throw new ArgumentNullException(nameof(userId));
            }

            return await this.etDb.ApiUsersAdAccountByUserIdGetAsync(userId);
        }

        public async Task<UsersListResponseModel> GetUsersSelectListAsync(IEnumerable<string> currentUserRoles)
        {
            var usersSelectList = new Dictionary<string, List<UsersSelectListResponseModel>>();
            var allUsers = new List<UserResponseModel>();
            if (currentUserRoles.Contains(UserRoles.ServiceManager) || currentUserRoles.Contains(UserRoles.Finance))
            {
                List<string> etUsersEln = this.userManager.GetUsersInRole(UserRoles.EtVivacom)
                    .Select(u => u.Value.ELN)
                    .ToList();

                var subcontractorNames = this.subcontractors.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                var subcontractorRoles = subcontractorNames.Select(x => $"{UserRoles.Subcontractor}{x}");
                foreach (string role in subcontractorRoles)
                {
                    etUsersEln.AddRange(this.userManager.GetUsersInRole(role).Select(u => u.Value.ELN));
                }

                allUsers = (await this.etDb.ApiUsersUsersByElnCollectionPostAsync(etUsersEln)).ToList();
            }
            else
            {
                var userId = await this.GetCurrentUserAsync();
                bool isTechnician = currentUserRoles.Contains(UserRoles.Technician);
                string subcontractorRole = currentUserRoles.FirstOrDefault(role => role.StartsWith(UserRoles.Subcontractor));
                allUsers = (await this.GetCurrentUserColleaguesAsync(subcontractorRole))
                    .Where(u => !isTechnician || u.Id == userId.Id)
                    .ToList();
            }

            foreach (var user in allUsers)
            {
                var iptuName = string.IsNullOrEmpty(user.IptuName) ? "Други" : user.IptuName;
                if (!usersSelectList.ContainsKey(iptuName))
                {
                    usersSelectList.Add(iptuName, new List<UsersSelectListResponseModel>()
                    {
                        this.mapper.Map<UsersSelectListResponseModel>(user),
                    });
                }
                else
                {
                    usersSelectList[iptuName].Add(this.mapper.Map<UsersSelectListResponseModel>(user));
                }
            }

            return new UsersListResponseModel
            {
                UsersSelectListResponseModels = usersSelectList,
                IptuListResponseModel = usersSelectList.Select(u => u.Key).Distinct(),
            };
        }

        public async Task<bool> IsUserMolAsync(string userId)
        {
            var isUserMol = await this.etDb.ApiUsersIsUserMolByUserIdGetAsync(userId);
            return isUserMol;
        }

        public async Task<ICollection<UserDataConciseResponseModel>> GetAllMolsAsync()
        {
            var users = await this.etDb.ApiUsersAllMolsGetAsync();
            return this.mapper.Map<ICollection<UserDataConciseResponseModel>>(users);
        }

        public async Task<SearchResponseModel<SearchUserDataResponseModel>> GetUsersAsync(SearchDataRequestModel request, IEnumerable<string> userRoles)
        {
            var dbRequest = this.mapper.Map<SearchUsersRequestModel>(request);

            if (!userRoles.Contains(UserRoles.ServiceManager))
            {
                string companyRole = userRoles.FirstOrDefault(r => r.StartsWith(UserRoles.Subcontractor)) ?? UserRoles.EtVivacom;
                dbRequest.FilteredUsersEln = this.userManager.GetUsersInRole(companyRole).Select(u => u.Value.ELN).ToList();
            }

            var filteredUsers = await this.etDb.ApiUsersSearchPostAsync(body: dbRequest);
            return this.mapper.Map<SearchResponseModel<SearchUserDataResponseModel>>(filteredUsers);
        }

        public async Task<ICollection<UserDataResponseModel>> GetUserMostFrequentTransfersToAsync(string fromUserId)
        {
            var users = await this.etDb.ApiUsersMostFrequentFransfersToGetAsync(fromUserId);
            return this.mapper.Map<ICollection<UserDataResponseModel>>(users);
        }

        public async Task UpdateUserAsync(EditUserRequest request)
        {
            var updateRequest = this.mapper.Map<EditUsersRequestModel>(request);
            await this.etDb.ApiUsersPutAsync(updateRequest);

            if (string.Equals(request.UserType, UserType.MOL.ToString()))
            {
                await this.substitutionsService.UpdateForUserIdWhenMolChanges(request);
            }

            var updatedUser = await this.etDb.ApiUsersByUserIdGetAsync(request.Id);
            await this.distributedCache.RemoveAsync($"{CacheConstants.EtWsUsersCacheKey}{updatedUser.Id}");
        }

        public async Task ActivateSelectedUsersAsync(IEnumerable<string> selectedUsers, string userId)
        {
            await this.etDb.ApiUsersActivateUsersPutAsync(userId, selectedUsers);
            await this.RemoveCachedUsersAsync(selectedUsers);
        }

        public async Task BlockSelectedUsersAsync(IEnumerable<string> selectedUsers, string userId)
        {
            await this.etDb.ApiUsersBlockUsersPutAsync(userId, selectedUsers);
            await this.RemoveCachedUsersAsync(selectedUsers);
        }

        public async Task UpdateUserNotificationAsync(string userId)
        {
            var dbRequest = new UpdateUserNotificationRequestModel()
            {
                UserId = userId,
                NotificationType = GlobalConstants.PendingTransfer,
            };

            await this.etDb.ApiUsersUpdateUserNotificationPatchAsync(body: dbRequest);
        }

        private static Dictionary<string, string> DeserializeImpersonatedCookie(string cookieValue)
        {
            return JsonConvert.DeserializeObject<Dictionary<string, string>>(cookieValue) ?? new Dictionary<string, string>();
        }

        private async Task<List<UserResponseModel>> GetCurrentUserColleaguesAsync(string subcontractorRole)
        {
            var allUsers = new List<UserResponseModel>();
            var currentUser = await this.GetCurrentUserAsync();

            if (subcontractorRole != null)
            {
                IEnumerable<string> subcontractorUsersEln = this.userManager.GetUsersInRole(subcontractorRole).Select(u => u.Value.ELN);
                allUsers = (await this.etDb.ApiUsersUsersByElnCollectionPostAsync(subcontractorUsersEln)).ToList();

                return allUsers;
            }

            var customIptuNames = this.iptuCustomNames.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            allUsers = (await this.etDb.ApiUsersUsersByCustomIptuCollectionAndUserSchenkerIdPostAsync(customIptuNames, currentUser.SchenkerId)).ToList();

            return allUsers;
        }

        private async Task CacheUserByUserId<T>(string userId, T user)
        {
            await this.distributedCache.SetAsync(
                $"{CacheConstants.EtWsUsersCacheKey}{userId}",
                Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(user)),
                new DistributedCacheEntryOptions { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(1) });
        }

        private async Task RemoveCachedUsersAsync(IEnumerable<string> userIds)
        {
            foreach (var userId in userIds)
            {
                await this.distributedCache.RemoveAsync($"{CacheConstants.EtWsUsersCacheKey}{userId}");
            }
        }
    }
}
