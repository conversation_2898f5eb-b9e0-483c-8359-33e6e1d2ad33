﻿using EFCore.BulkExtensions;
using EtDb.ApiClients.EtDb.Context;
using EtDb.ApiClients.EtDb.Models;
using EtDb.DataHandlers.Interfaces;
using EtDb.DataHandlers.Models;
using EtDb.Infrastructure.Enumerations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;

namespace EtDb.DataHandlers
{
    public class QuantityCalculationObjectDataHandler : BaseDataHandler, IQuantityCalculationObjectDataHandler
    {

        private const string DbTableName = "QuantityCalculationObject";
        private const string DbSchemaName = "ET";

        private const int InitialCriticalQuantity = 0;
        private const int InitialDeliveryTimeInDays = 7;

        private readonly Lazy<IEquipmentDataHandler> equipmentDataHandler;
        private readonly Lazy<ISchenkerDataHandler> schenkerDataHandler;
        private readonly Lazy<ISnapshotDateDataHandler> snapshotDateDataHandler;
        private readonly Lazy<IAvailableEquipmentByOPSnapShotDataHandler> availableEquipmentByOPSnapShotDataHandler;
        private readonly Lazy<IEquipmentGroupDataHandler> equipmentGroupDataHandler;
        private readonly Lazy<IEquipmentTypeDataHandler> equipmentTypeDataHandler;
        private readonly ILogger<QuantityCalculationObjectDataHandler> logger;

        public QuantityCalculationObjectDataHandler(
            Lazy<EtDbContext> dbContext,
            Lazy<IEquipmentDataHandler> equipmentDataHandler,
            Lazy<ISchenkerDataHandler> schenkerDataHandler,
            Lazy<ISnapshotDateDataHandler> snapshotDateDataHandler,
            Lazy<IAvailableEquipmentByOPSnapShotDataHandler> availableEquipmentByOPSnapShotDataHandler,
            Lazy<IEquipmentGroupDataHandler> equipmentGroupDataHandler,
            Lazy<IEquipmentTypeDataHandler> equipmentTypeDataHandler,
            ILogger<QuantityCalculationObjectDataHandler> logger)
            : base(dbContext)
        {
            this.equipmentDataHandler = equipmentDataHandler ?? throw new ArgumentNullException(nameof(equipmentDataHandler));
            this.schenkerDataHandler = schenkerDataHandler ?? throw new ArgumentNullException(nameof(schenkerDataHandler));
            this.snapshotDateDataHandler = snapshotDateDataHandler ?? throw new ArgumentNullException(nameof(snapshotDateDataHandler));
            this.availableEquipmentByOPSnapShotDataHandler = availableEquipmentByOPSnapShotDataHandler ?? throw new ArgumentNullException(nameof(availableEquipmentByOPSnapShotDataHandler));
            this.equipmentGroupDataHandler = equipmentGroupDataHandler ?? throw new ArgumentNullException(nameof(equipmentGroupDataHandler)); ;
            this.equipmentTypeDataHandler = equipmentTypeDataHandler ?? throw new ArgumentNullException(nameof(equipmentTypeDataHandler));
            this.logger = logger;
            ;

            if (!this.GetAll().Any())
            {
                this.SeedInitialData();

                // If this method is not used, it can be removed
                logger.LogInformation("Seed Initial Data Triggered");
            }
        }

        public async Task<FilteredDataModel<QuantityCalculationObject>> GetFilteredQuantityCalculationObjectsAsync(string sortBy, string sortDir, int pageNumber, int pageSize, string query)
        {
            var allData = this.GetAllOnlyWithSerialNumber();

            return await this.GetFilteredData(allData, sortBy, sortDir, pageNumber, pageSize, query);
        }

        public IQueryable<QuantityCalculationObject> GetAll()
        {
            return this.dbContext.Value.QuantityCalculationObjects.Include(x => x.Op);
        }

        public async Task<QuantityCalculationObject> GetByIdAsync(int id)
        {
            var result = await this.dbContext.Value.QuantityCalculationObjects
                .Include(x => x.Op)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (result == null)
            {
                throw new InvalidOperationException($"No QuantityCalculationObject found with Id {id}.");
            }

            return result;
        }

        public IQueryable<QuantityCalculationObject> GetAllOnlyWithSerialNumber()
        {
            var equipmentGroups = this.GetEquipmentGroupQuantityCalculationObjects(true);
            var equipmentTypes = this.GetEquipmentTypeQuantityCalculationObjects(true);

            var result = equipmentGroups.Concat(equipmentTypes);

            return result;
        }

        public QuantityCalculationObject Add(QuantityCalculationObject quantityCalculationObject)
        {
            this.dbContext.Value.QuantityCalculationObjects.Add(quantityCalculationObject);

            return quantityCalculationObject;
        }

        public QuantityCalculationObject Update(QuantityCalculationObject quantityCalculationObject)
        {
            this.dbContext.Value.QuantityCalculationObjects.Update(quantityCalculationObject);

            return quantityCalculationObject;
        }



        public void SeedInitialData()
        {
            var initialData = this.GenerateQuantityCalculationObjectsAsync().GetAwaiter().GetResult();
            this.BulkInsertAsync(initialData).GetAwaiter().GetResult();
        }

        public async Task BulkInsertAsync(IEnumerable<QuantityCalculationObject> collectionToBeUpdated)
        {
            if (await this.GetAll().AnyAsync())
            {
                throw new Exception("Already data in table QuantityCalculationObject");
            }

            try
            {
                await this.dbContext.Value.BulkInsertAsync(collectionToBeUpdated.ToList());
            }
            catch (Exception ex)
            {
                throw new Exception("Bulk insert failed", ex);
            }
        }

        public async Task BulkUpdateAsync(IEnumerable<QuantityCalculationObject> collectionToBeUpdated)
        {
            if (collectionToBeUpdated == null)
                throw new ArgumentNullException(nameof(collectionToBeUpdated));

            try
            {
                await this.dbContext.Value.BulkUpdateAsync(collectionToBeUpdated.ToList());
            }
            catch (Exception ex)
            {
                throw new Exception("Bulk update failed", ex);
            }
        }

        public IQueryable<QuantityCalculationObject> GetEquipmentGroupQuantityCalculationObjects(bool onlyWithSerialNumbers = false)
        {
            var equipmentGroups = this.GetAll()
                .Where(x => x.IsGroupWithSubstitutes == true)
                .Where(x => x.EquipmentGroupId != null);

            var serialNumberFlag = (int)SerialNumberRequiredStatus.Required;

            if (onlyWithSerialNumbers)
            {
                equipmentGroups = equipmentGroups
                               .Where(x => x.EquipmentGroup.EquipmentType
                                    .All(eT => eT.SerialNumberRequired == serialNumberFlag));
            }

            return equipmentGroups;
        }

        public IQueryable<QuantityCalculationObject> GetEquipmentTypeQuantityCalculationObjects(bool onlyWithSerialNumbers = false)
        {
            var equipmentTypes = this.GetAll()
                .Where(x => x.IsGroupWithSubstitutes == false)
                .Where(x => x.EquipmentTypeId != null);

            var serialNumberFlag = (int)SerialNumberRequiredStatus.Required;

            if (onlyWithSerialNumbers)
            {
                equipmentTypes = equipmentTypes
                        .Where(x => x.EquipmentType.SerialNumberRequired == serialNumberFlag);
            }

            return equipmentTypes;
        }

        public async Task<IEnumerable<QuantityCalculationObjectAvailableQuantityModel>> GetAvailableQuantitiesByQuantityCalculationObjectFromSnapshotAsync(IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectsCollection, DateTime snapshotDate)
        {
            var equipmentGroupsAvailableQuantity = await this
                .GetAvailableQuantitiesByQuantityCalculationObjectEquipmentGroupsFromSnapshotAsync(quantityCalculationObjectsCollection, snapshotDate);

            var equipmentTypesAvailableQuantity = await this
                .GetAvailableQuantitiesByQuantityCalculationObjectEquipmentTypesFromSnapshotAsync(quantityCalculationObjectsCollection, snapshotDate);

            var result = equipmentGroupsAvailableQuantity
                .Concat(equipmentTypesAvailableQuantity);

            return result;
        }

        public async Task<IEnumerable<QuantityCalculationObjectAvailableQuantityModel>> GetAvailableQuantitiesByQuantityCalculationObjectEquipmentGroupsFromSnapshotAsync(IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectsCollection, DateTime snapshotDate)
        {
            var snapshot = await this.snapshotDateDataHandler.Value.FindAvailableSnapshotForDateAsync(snapshotDate);

            if (snapshot == null)
            {
                throw new ArgumentException("No available data for this date");
            }

            var snapshotId = snapshot.Id;

            var groupsOnlyIdentifiers = this.ExtractGroupsFromQuantityCalculationObjectsCollection(quantityCalculationObjectsCollection);

            var groupsOnlyIdentifiersNames = groupsOnlyIdentifiers
                .Select(x => x.Name)
                .Distinct()
                .ToList();

            Expression<Func<QuantityCalculationObject, bool>> collectionFilter =
                x => groupsOnlyIdentifiersNames.Contains(x.Name);

            var equipmentGroupsQuery = this.GetEquipmentGroupQuantityCalculationObjects()
                .Where(collectionFilter);

            var equipmentGroups = await equipmentGroupsQuery
                .Include(x => x.EquipmentGroup.EquipmentType)
                .Include(x => x.Op)
                .ToListAsync();

            var equipmentGroupsAvailableQuantity = equipmentGroups
                .Select(x =>
                    new EquipmentGroupQuantityModel
                    {
                        OPCode = x.Op.Opcode,
                        OPId = x.Opid,
                        GroupName = x.Name,
                        EquipmentGroupdId = x.EquipmentGroupId.Value,
                        QuantitiesByEquipmentTypes = x.EquipmentGroup.EquipmentType
                            .SelectMany(eT => eT.AvailableEquipmentByOpsnapshots)
                            .Where(sn => sn.SnapshotDateId == snapshotId)
                            .Where(sn => x.Op.Id == sn.Op.Id)
                            .Select(sn => new EquipmentTypeQuantityModel
                            {
                                EquipmentTypeId = sn.EquipmentType.Id,
                                EquipmentTypeName = sn.EquipmentType.Name,
                                EquipmentTypeSAPMaterialNumber = sn.EquipmentType.SapmaterialNum,
                                Quantity = sn.EquipmentQuantity,
                                SendMethod = sn.EquipmentType.SendMethod
                            })
                    })
                .ToList();

            var result = await this.ConvertEquipmentGroupQuantityModelToQuantityCalculationObjectAvailableQuantityModelAsync(
                equipmentGroups.AsQueryable(), equipmentGroupsAvailableQuantity);

            return result;
        }

        public async Task<IEnumerable<QuantityCalculationObjectAvailableQuantityModel>> GetAvailableQuantitiesByQuantityCalculationObjectEquipmentTypesFromSnapshotAsync(IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectsCollection, DateTime snapshotDate)
        {
            var snapshot = await this.snapshotDateDataHandler.Value
                .FindAvailableSnapshotForDateAsync(snapshotDate);

            if (snapshot == null)
            {
                throw new ArgumentException("No available data for this date");
            }

            var snapshotId = snapshot.Id;

            var equipmentTypesOnlyIdentifiers = this.ExtractEquipmentTypesFromQuantityCalculationObjectsCollection(quantityCalculationObjectsCollection);

            var equipmentTypesOnlyIdentifiersNames = equipmentTypesOnlyIdentifiers
                .Select(x => x.Name)
                .Distinct()
                .ToList();

            Expression<Func<QuantityCalculationObject, bool>> collectionFilter =
                x => equipmentTypesOnlyIdentifiersNames.Contains(x.Name);

            var equipmentTypes = await this.GetEquipmentTypeQuantityCalculationObjects()
                .Where(collectionFilter)
                .ToListAsync();

            var equipmentTypeIds = equipmentTypes
                .Select(x => x.EquipmentTypeId)
                .Distinct()
                .ToList();

            var snapshotData = await this.availableEquipmentByOPSnapShotDataHandler.Value.GetAll()
                .Where(x => x.SnapshotDateId == snapshotId)
                .Where(x => equipmentTypeIds.Contains(x.EquipmentTypeId))
                .Select(x => new
                {
                    Name = x.EquipmentType.Name,
                    OPCode = x.Op.Opcode,
                    EquipmentTypeId = x.EquipmentTypeId,
                    OpId = x.Opid,
                    EquipmentQuantity = x.EquipmentQuantity
                })
                .ToListAsync();

            var equipmentTypesAvailableQuantity = snapshotData
                .Select(x => new QuantityCalculationObjectAvailableQuantityModel
                {
                    OPCode = x.OPCode,
                    QuantityCalculationObject = new QuantityCalculationObjectServiceModel<int>(
                        x.Name, false, x.OpId, null, x.EquipmentTypeId),
                    AvailableQuantity = x.EquipmentQuantity
                });

            return equipmentTypesAvailableQuantity;
        }

        public async Task<IEnumerable<QuantityCalculationObjectAvailableQuantityModel>> GetAvailableQuantitiesByQuantityCalculationObjectEquipmentGroupsAsync(IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectsCollection, bool validEquipmentOnly = true)
        {
            var groupsOnlyIdentifiers = this.ExtractGroupsFromQuantityCalculationObjectsCollection(quantityCalculationObjectsCollection);

            var groupsOnlyIdentifiersNames = groupsOnlyIdentifiers
                .Select(x => x.Name)
                .Distinct()
                .ToList();

            Expression<Func<QuantityCalculationObject, bool>> collectionFilter = x => groupsOnlyIdentifiersNames.Contains(x.Name);

            Expression<Func<Item, bool>> itemValidityFilter = validEquipmentOnly
                ? (x => x.EquipmentValidity == "Y")
                : (Expression<Func<Item, bool>>)(x => true);

            var equipmentGroups = this.GetEquipmentGroupQuantityCalculationObjects();

            var equipmentGroupsAvailableQuantity = equipmentGroups
                .Where(collectionFilter)
                .Select(x =>
                    new EquipmentGroupQuantityModel
                    {
                        OPCode = x.Op.Opcode,
                        OPId = x.Opid,
                        GroupName = x.Name,
                        EquipmentGroupdId = x.EquipmentGroupId.Value,
                        QuantitiesByEquipmentTypes = x.EquipmentGroup.EquipmentType
                            .SelectMany(eT => eT.Item)
                            .AsQueryable()
                            .Where(itemValidityFilter)
                            .SelectMany(item => item.AvailableEquipment)
                            .Where(ae => ae.ItemQuantity > 0)
                            .Where(ae => ae.User.Eln != "99999")
                            .Where(ae => ae.User.Schenker.Id == x.Op.Id)
                            .GroupBy(ae => ae.Item.EquipmentType)
                            .Select(g => new EquipmentTypeQuantityModel
                            {
                                EquipmentTypeId = g.Key.Id,
                                EquipmentTypeName = g.Key.Name,
                                EquipmentTypeSAPMaterialNumber = g.Key.SapmaterialNum,
                                Quantity = g.Sum(t => t.ItemQuantity),
                                SendMethod = g.Key.SendMethod
                            })
                            .ToList()
                    })
                .ToList();

            var result = await this.ConvertEquipmentGroupQuantityModelToQuantityCalculationObjectAvailableQuantityModelAsync(
                equipmentGroups.AsQueryable(), equipmentGroupsAvailableQuantity);

            return result;
        }

        public async Task<IEnumerable<QuantityCalculationObjectAvailableQuantityModel>> GetAvailableQuantitiesByQuantityCalculationObjectAsync(IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectsCollection, bool validEquipmentOnly = true)
        {
            var equipmentGroupsAvailableQuantity = await this.GetAvailableQuantitiesByQuantityCalculationObjectEquipmentGroupsAsync(
                quantityCalculationObjectsCollection, validEquipmentOnly);

            var equipmentTypesAvailableQuantity = await this.GetAvailableQuantitiesByQuantityCalculationObjectEquipmentTypesAsync(
                quantityCalculationObjectsCollection, validEquipmentOnly);

            var result = equipmentGroupsAvailableQuantity
                .Concat(equipmentTypesAvailableQuantity);

            return result;
        }

        public async Task<IEnumerable<QuantityCalculationObjectAvailableQuantityModel>> GetAvailableQuantitiesByQuantityCalculationObjectEquipmentTypesAsync(IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectsCollection, bool validEquipmentOnly = true)
        {
            var equipmentTypesOnlyIdentifiers = this.ExtractEquipmentTypesFromQuantityCalculationObjectsCollection(quantityCalculationObjectsCollection);

            var equipmentTypesOnlyIdentifiersNames = equipmentTypesOnlyIdentifiers
                .Select(x => x.Name)
                .Distinct()
                .ToList();

            Expression<Func<QuantityCalculationObject, bool>> collectionFilter =
                x => equipmentTypesOnlyIdentifiersNames.Contains(x.Name);

            var quantityPlaceHolder = 0;

            var equipmentTypes = await this.GetEquipmentTypeQuantityCalculationObjects()
                .Where(collectionFilter)
                .Include(x => x.Op)
                .ToListAsync();

            var equipmentTypesAvailableQuantity = equipmentTypes
                .Select(x =>
                    new QuantityCalculationObjectAvailableQuantityModel
                    {
                        OPCode = x.Op.Opcode,
                        QuantityCalculationObject = new QuantityCalculationObjectServiceModel<int>(
                            x.Name, x.IsGroupWithSubstitutes, x.Opid, x.EquipmentGroupId, x.EquipmentTypeId),
                        AvailableQuantity = quantityPlaceHolder
                    })
                .ToList();

            var equipmentTypeIds = equipmentTypes
                .Select(x => x.EquipmentTypeId)
                .Distinct()
                .ToList();

            Expression<Func<AvailableEquipment, bool>> itemValidityFilter = validEquipmentOnly
                ? (x => x.Item.EquipmentValidity == "Y")
                : (Expression<Func<AvailableEquipment, bool>>)(x => true);

            var availableEquipments = await this.equipmentDataHandler.Value
                .GetNonZeroQuantityAvailableItems()
                .Where(x => x.User.SchenkerId != null)
                .Where(x => x.User.Eln != "99999")
                .Where(itemValidityFilter)
                .Where(x => equipmentTypeIds.Contains(x.Item.EquipmentTypeId))
                .Select(x => new
                {
                    OPCode = x.User.Schenker.Opcode,
                    Name = x.Item.EquipmentType.Name,
                    AvailableQuantity = x.ItemQuantity,
                })
                .ToListAsync();

            foreach (var item in equipmentTypesAvailableQuantity)
            {
                item.AvailableQuantity = availableEquipments
                    .Where(x => x.Name == item.QuantityCalculationObject.Name &&
                                x.OPCode == item.OPCode)
                    .Sum(x => x.AvailableQuantity);
            }

            return equipmentTypesAvailableQuantity;
        }

        public async Task<IDictionary<QuantityCalculationObjectIdentifier, QuantityCalculationObjectServiceModel<int>>> GetQuantityCalculationObjectServiceModelsAsync(IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectIdentifiers)
        {
            var initialQuantity = QuantityCalculationObjectServiceModel<int>.InitialEquipmentTypeQuantity;

            // GROUPS
            var groupsOnlyIdentifiers = this.ExtractGroupsFromQuantityCalculationObjectsCollection(quantityCalculationObjectIdentifiers);

            var groupsOnlyIdentifiersNames = groupsOnlyIdentifiers
                                .Select(x => x.Name)
                                .Distinct()
                                .ToList();

            Expression<Func<QuantityCalculationObject, bool>> collectionFilter = x => groupsOnlyIdentifiersNames.Contains(x.Name);

            var equipmentGroups = this.GetEquipmentGroupQuantityCalculationObjects();

            var equipmentGroupsList = await equipmentGroups
                .Where(collectionFilter)
                .ToListAsync();

            var equipmentGroupsQuantities = equipmentGroupsList
                .Select(x => new EquipmentGroupQuantityModel
                {
                    OPCode = x.Op.Opcode,
                    OPId = x.Opid,
                    GroupName = x.Name,
                    EquipmentGroupdId = x.EquipmentGroupId.Value,
                    QuantitiesByEquipmentTypes = x.EquipmentGroup.EquipmentType
                            .Select(eT => new EquipmentTypeQuantityModel
                            {
                                EquipmentTypeId = eT.Id,
                                EquipmentTypeName = eT.Name,
                                EquipmentTypeSAPMaterialNumber = eT.SapmaterialNum,
                                Quantity = initialQuantity,
                                SendMethod = eT.SendMethod
                            })
                            .ToList()
                })
                .Select(x => new
                {
                    QuantityCalculationObjectIdentifier = new QuantityCalculationObjectIdentifier(x.GroupName, x.OPCode, true),
                    EquipmentGroupQuantityModel = x
                })
                .ToDictionary(x => x.QuantityCalculationObjectIdentifier, x => x.EquipmentGroupQuantityModel);

            var result = new Dictionary<QuantityCalculationObjectIdentifier, QuantityCalculationObjectServiceModel<int>>();

            foreach (var item in equipmentGroupsQuantities)
            {
                var qco = item.Key;
                var equipmentGroupQuantityModel = item.Value;

                var quantityCalculationServiceModel = new QuantityCalculationObjectServiceModel<int>(
                    equipmentGroupQuantityModel.GroupName, true, equipmentGroupQuantityModel.OPId, equipmentGroupQuantityModel.EquipmentGroupdId, null);

                foreach (var eqTypeQuantityModel in equipmentGroupQuantityModel.QuantitiesByEquipmentTypes)
                {
                    var eqType = new EquipmentType
                    {
                        Id = eqTypeQuantityModel.EquipmentTypeId,
                        Name = eqTypeQuantityModel.EquipmentTypeName,
                        SapmaterialNum = eqTypeQuantityModel.EquipmentTypeSAPMaterialNumber,
                        SendMethod = eqTypeQuantityModel.SendMethod
                    };

                    quantityCalculationServiceModel.EquipmentTypeQuantities[eqType] = eqTypeQuantityModel.Quantity;
                }

                result[qco] = quantityCalculationServiceModel;
            }

            // TYPES
            var equipmentTypesOnlyIdentifiers = this.ExtractEquipmentTypesFromQuantityCalculationObjectsCollection(quantityCalculationObjectIdentifiers);

            var equipmentTypesOnlyIdentifiersNames = equipmentTypesOnlyIdentifiers
                                .Select(x => x.Name)
                                .Distinct()
                                .ToList();

            collectionFilter = x => equipmentTypesOnlyIdentifiersNames.Contains(x.Name);

            var equipmentTypes = this.GetEquipmentTypeQuantityCalculationObjects();

            var equipmentTypesList = await equipmentTypes
                .Where(collectionFilter)
                .Include(x => x.Op)
                .ToListAsync();

            var equipmentTypesQuantities = equipmentTypesList
                .Select(x => new
                {
                    QuantityCalculationIdentifier = new QuantityCalculationObjectIdentifier(
                        x.Name, x.Op.Opcode, false),
                    QuantityCalculationObjectServiceModel = new QuantityCalculationObjectServiceModel<int>(
                        x.Name, x.IsGroupWithSubstitutes, x.Opid, x.EquipmentGroupId, x.EquipmentTypeId),
                })
                .ToDictionary(x => x.QuantityCalculationIdentifier, x => x.QuantityCalculationObjectServiceModel);

            foreach (var item in equipmentTypesQuantities)
            {
                result[item.Key] = item.Value;
            }

            return result;
        }

        public async Task<HashSet<QuantityCalculationObjectIdentifier>> GetQuantityCalculationObjectIdentifiersAsync(bool onlyWithSerialNumbers = false)
        {
            var equipmentGroups = this.GetEquipmentGroupQuantityCalculationObjects(onlyWithSerialNumbers);
            var equipmentTypes = this.GetEquipmentTypeQuantityCalculationObjects(onlyWithSerialNumbers);

            var materialized = await equipmentGroups
                                    .Concat(equipmentTypes)
                                    .Include(x => x.Op)
                                    .AsNoTracking()
                                    .ToListAsync();

            var projected = materialized
                                .Select(x => new QuantityCalculationObjectIdentifier(
                                    x.Name, x.Op.Opcode, x.IsGroupWithSubstitutes));

            var result = new HashSet<QuantityCalculationObjectIdentifier>(projected);

            return result;
        }

        private IEnumerable<QuantityCalculationObjectIdentifier> ExtractGroupsFromQuantityCalculationObjectsCollection(
            IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectsCollection)
        {
            var result = quantityCalculationObjectsCollection
                            .Where(x => x.IsGroupWithSubstitutes == true);

            return result;
        }

        private IEnumerable<QuantityCalculationObjectIdentifier> ExtractEquipmentTypesFromQuantityCalculationObjectsCollection(
            IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectsCollection)
        {
            var result = quantityCalculationObjectsCollection
                            .Where(x => x.IsGroupWithSubstitutes == false);

            return result;
        }

        private async Task<IEnumerable<QuantityCalculationObject>> GenerateQuantityCalculationObjectsAsync()
        {
            var equipmentGroups = await this.equipmentGroupDataHandler.Value
                                .GetEquipmentGroups()
                                .ToListAsync();

            var equipmentTypesWithNoSubstitutes = await this.equipmentTypeDataHandler.Value
                                .GetEquipmentTypes()
                                .Where(x => x.EquipmentGroup.ContainsSubstitutes == false)
                                .ToListAsync();

            var opInfos = await this.schenkerDataHandler.Value
                                .GetAll()
                                .Select(x => x.Id)
                                .ToListAsync();

            var result = new List<QuantityCalculationObject>();

            foreach (var opId in opInfos)
            {
                foreach (var eqGroup in equipmentGroups)
                {
                    var newQuantitiesCalculationObject = new QuantityCalculationObject
                    {
                        Opid = opId,
                        CriticalQuantity = InitialCriticalQuantity,
                        DeliveryTimeInDays = InitialCriticalQuantity,
                        IsManuallySet = false,
                        ManualSetDaysValid = null,
                        LastUpdateDate = DateTime.Now,
                        EquipmentTypeId = null,
                        EquipmentGroupId = eqGroup.Id,
                        IsGroupWithSubstitutes = true,
                        Name = eqGroup.Name
                    };

                    result.Add(newQuantitiesCalculationObject);
                }

                foreach (var eqType in equipmentTypesWithNoSubstitutes)
                {
                    var newQuantitiesCalculationObject = new QuantityCalculationObject
                    {
                        Opid = opId,
                        CriticalQuantity = InitialCriticalQuantity,
                        DeliveryTimeInDays = InitialCriticalQuantity,
                        IsManuallySet = false,
                        ManualSetDaysValid = null,
                        LastUpdateDate = DateTime.Now,
                        EquipmentTypeId = eqType.Id,
                        EquipmentGroupId = null,
                        IsGroupWithSubstitutes = false,
                        Name = eqType.Name
                    };

                    result.Add(newQuantitiesCalculationObject);
                }
            }

            return result;
        }

        private async Task<IEnumerable<QuantityCalculationObjectAvailableQuantityModel>> ConvertEquipmentGroupQuantityModelToQuantityCalculationObjectAvailableQuantityModelAsync(IQueryable<QuantityCalculationObject> equipmentGroupsQuery, IEnumerable<EquipmentGroupQuantityModel> equipmentGroupsAvailableQuantity)
        {
            var equipmentGroups = await equipmentGroupsQuery
                .Include(x => x.EquipmentGroup.EquipmentType)
                .ToListAsync();

            var result = new List<QuantityCalculationObjectAvailableQuantityModel>();

            var equipmentTypesForGroup = equipmentGroups
                .GroupBy(x => x.EquipmentGroup)
                .Select(x => new
                {
                    EquipmentGroupdId = x.Key.Id,
                    EquipmentType = x.Key.EquipmentType
                })
                .ToDictionary(x => x.EquipmentGroupdId, x => x.EquipmentType);

            foreach (var item in equipmentGroupsAvailableQuantity)
            {
                var quantityCalculationObjectAvailableQuantityModel = new QuantityCalculationObjectAvailableQuantityModel
                {
                    OPCode = item.OPCode,
                    AvailableQuantity = item.QuantitiesByEquipmentTypes.Sum(x => x.Quantity),
                    QuantityCalculationObject = new QuantityCalculationObjectServiceModel<int>(
                        item.GroupName, true, item.OPId, item.EquipmentGroupdId, null)
                };

                foreach (var eqType in equipmentTypesForGroup[item.EquipmentGroupdId])
                {
                    var equipmentType = new EquipmentType
                    {
                        Id = eqType.Id,
                        SapmaterialNum = eqType.SapmaterialNum,
                        Name = eqType.Name,
                        SendMethod = eqType.SendMethod
                    };

                    quantityCalculationObjectAvailableQuantityModel
                        .QuantityCalculationObject.EquipmentTypeQuantities[equipmentType] =
                        item.QuantitiesByEquipmentTypes.FirstOrDefault(x => x.EquipmentTypeId == eqType.Id)?.Quantity ?? 0;
                }

                result.Add(quantityCalculationObjectAvailableQuantityModel);
            }

            return result;
        }
    }
}

