﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.9.34622.214
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Web", "Web", "{C8868157-1A79-4AF8-8C81-5915465DD27B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EtDb.Api", "EtDb.Api\EtDb.Api.csproj", "{584C37E4-87B6-4491-AC8D-6454E3C0C466}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{7BA7EE3A-073C-41F8-96A7-7CD18B39770C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EtDb.Models", "EtDb.Models\EtDb.Models.csproj", "{8D17A943-C34B-4877-A2B5-7EBDA87E81B9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EtDb.Services", "EtDb.Services\EtDb.Services.csproj", "{385F7FF0-AFA7-46C1-BDF4-F89C80F71C3E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Data", "Data", "{84C872C2-58FE-44D7-9AEF-066AFCD54290}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EtDb.ApiClients", "EtDb.ApiClients\EtDb.ApiClients.csproj", "{572FA72E-8D78-4A61-9FDE-F3CEA7F561FD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{EF960713-E07E-4FCC-BAF7-F6DAD09CE83D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EtDb.Infrastructure", "EtDb.Infrastructure\EtDb.Infrastructure.csproj", "{F0CBD465-C9BC-4703-A258-0D74073DE4BB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{435902B5-871F-41CE-B24E-AD3D98726F00}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EtDb.Services.Tests", "EtDb.Services.Tests\EtDb.Services.Tests.csproj", "{B6BFD090-849E-4314-95AF-8380C3379DEF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EtDb.DataHandlers", "EtDb.DataHandlers\EtDb.DataHandlers.csproj", "{392CEB22-087D-4E6A-B707-0989AC192280}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{584C37E4-87B6-4491-AC8D-6454E3C0C466}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{584C37E4-87B6-4491-AC8D-6454E3C0C466}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{584C37E4-87B6-4491-AC8D-6454E3C0C466}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{584C37E4-87B6-4491-AC8D-6454E3C0C466}.Release|Any CPU.Build.0 = Release|Any CPU
		{8D17A943-C34B-4877-A2B5-7EBDA87E81B9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8D17A943-C34B-4877-A2B5-7EBDA87E81B9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8D17A943-C34B-4877-A2B5-7EBDA87E81B9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8D17A943-C34B-4877-A2B5-7EBDA87E81B9}.Release|Any CPU.Build.0 = Release|Any CPU
		{385F7FF0-AFA7-46C1-BDF4-F89C80F71C3E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{385F7FF0-AFA7-46C1-BDF4-F89C80F71C3E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{385F7FF0-AFA7-46C1-BDF4-F89C80F71C3E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{385F7FF0-AFA7-46C1-BDF4-F89C80F71C3E}.Release|Any CPU.Build.0 = Release|Any CPU
		{572FA72E-8D78-4A61-9FDE-F3CEA7F561FD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{572FA72E-8D78-4A61-9FDE-F3CEA7F561FD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{572FA72E-8D78-4A61-9FDE-F3CEA7F561FD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{572FA72E-8D78-4A61-9FDE-F3CEA7F561FD}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0CBD465-C9BC-4703-A258-0D74073DE4BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0CBD465-C9BC-4703-A258-0D74073DE4BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0CBD465-C9BC-4703-A258-0D74073DE4BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0CBD465-C9BC-4703-A258-0D74073DE4BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{B6BFD090-849E-4314-95AF-8380C3379DEF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B6BFD090-849E-4314-95AF-8380C3379DEF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B6BFD090-849E-4314-95AF-8380C3379DEF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B6BFD090-849E-4314-95AF-8380C3379DEF}.Release|Any CPU.Build.0 = Release|Any CPU
		{392CEB22-087D-4E6A-B707-0989AC192280}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{392CEB22-087D-4E6A-B707-0989AC192280}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{392CEB22-087D-4E6A-B707-0989AC192280}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{392CEB22-087D-4E6A-B707-0989AC192280}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{584C37E4-87B6-4491-AC8D-6454E3C0C466} = {C8868157-1A79-4AF8-8C81-5915465DD27B}
		{8D17A943-C34B-4877-A2B5-7EBDA87E81B9} = {7BA7EE3A-073C-41F8-96A7-7CD18B39770C}
		{385F7FF0-AFA7-46C1-BDF4-F89C80F71C3E} = {7BA7EE3A-073C-41F8-96A7-7CD18B39770C}
		{572FA72E-8D78-4A61-9FDE-F3CEA7F561FD} = {84C872C2-58FE-44D7-9AEF-066AFCD54290}
		{F0CBD465-C9BC-4703-A258-0D74073DE4BB} = {EF960713-E07E-4FCC-BAF7-F6DAD09CE83D}
		{B6BFD090-849E-4314-95AF-8380C3379DEF} = {435902B5-871F-41CE-B24E-AD3D98726F00}
		{392CEB22-087D-4E6A-B707-0989AC192280} = {84C872C2-58FE-44D7-9AEF-066AFCD54290}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {BF5C0068-9E30-44C6-A4E4-7ABEFED9F45A}
	EndGlobalSection
EndGlobal
