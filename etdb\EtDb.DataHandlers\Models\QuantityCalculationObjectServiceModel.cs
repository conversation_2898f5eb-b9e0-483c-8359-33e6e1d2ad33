﻿namespace EtDb.DataHandlers.Models
{
    using EtDb.ApiClients.EtDb.Models;

    public class QuantityCalculationObjectServiceModel<T>
    {
        public const int InitialEquipmentTypeQuantity = 0;

        public QuantityCalculationObjectServiceModel(
            string name,
            bool isGroupWithSubstitutes,
            int opId,
            int? equipmentGroupId,
            int? equipmentTypeId = null)
        {
            this.Name = name ??
                throw new ArgumentException($"{nameof(QuantityCalculationObjectServiceModel<T>)} Constructor Error: {name} is null");

            this.IsGroupWithSubstitutes = isGroupWithSubstitutes;

            this.OPId = opId;
            this.EquipmentGroupId = equipmentGroupId;
            this.EquipmentTypeId = equipmentTypeId;

            this.EquipmentTypeQuantities = new Dictionary<EquipmentType, T>();
        }

        // Gets Equipment group name or equipment type name
        public string Name { get; }

        public bool IsGroupWithSubstitutes { get; }

        public int OPId { get; }

        public int? EquipmentTypeId { get; }

        public int? EquipmentGroupId { get; }

        public Dictionary<EquipmentType, T> EquipmentTypeQuantities { get; set; }

        public QuantityCalculationObjectServiceModel<double> ConvertToЕquipmentTypeProportions()
        {
            if (this.GetType().GenericTypeArguments[0].Name != "Int32")
            {
                throw new Exception("this has to be of type Int32 to be converted to Integer");
            }

            var quantityCalculationObjectServiceModel = new QuantityCalculationObjectServiceModel<double>(
                this.Name, this.IsGroupWithSubstitutes, this.OPId, this.EquipmentGroupId, this.EquipmentTypeId);

            if (!quantityCalculationObjectServiceModel.IsGroupWithSubstitutes)
            {
                return quantityCalculationObjectServiceModel;
            }

            var sumQuantities = 0;

            foreach (var item in this.EquipmentTypeQuantities.Values)
            {
                sumQuantities += (int)(object)item;
            }

            if (sumQuantities > 0)
            {
                return this.ConvertToRateEquipmentTypeQuantities(sumQuantities);
            }

            var commonProportion = 1 / (double)this.EquipmentTypeQuantities.Count;

            foreach (var eqType in this.EquipmentTypeQuantities)
            {
                quantityCalculationObjectServiceModel.EquipmentTypeQuantities[eqType.Key] = commonProportion;
            }

            return quantityCalculationObjectServiceModel;
        }

        public QuantityCalculationObjectServiceModel<double> ConvertToRateEquipmentTypeQuantities(int divisor)
        {
            if (divisor == 0)
            {
                throw new ArgumentException($"{nameof(divisor)} cannot be 0");
            }

            if (this.GetType().GenericTypeArguments[0].Name != "Int32")
            {
                throw new Exception("this has to be of type Int32 to be converted to Integer");
            }

            var quantityCalculationObjectServiceModel = new QuantityCalculationObjectServiceModel<double>(
                this.Name, this.IsGroupWithSubstitutes, this.OPId, this.EquipmentGroupId, this.EquipmentTypeId);

            if (!quantityCalculationObjectServiceModel.IsGroupWithSubstitutes)
            {
                return quantityCalculationObjectServiceModel;
            }

            foreach (var eqType in this.EquipmentTypeQuantities)
            {
                // possible performance overhead
                var eqTypeValue = (int)((object)eqType.Value);

                quantityCalculationObjectServiceModel.EquipmentTypeQuantities[eqType.Key] = (double)eqTypeValue / divisor;
            }

            return quantityCalculationObjectServiceModel;
        }

        public QuantityCalculationObjectServiceModel<int> ConvertToIntegerEquipmentTypeQuantities(int rateMultiplier)
        {
            if (this.GetType().GenericTypeArguments[0].Name != "Double")
            {
                throw new Exception("this has to be of type Double to be converted to Integer");
            }

            var quantityCalculationObjectServiceModel = new QuantityCalculationObjectServiceModel<int>(
                this.Name, this.IsGroupWithSubstitutes, this.OPId, this.EquipmentGroupId, this.EquipmentTypeId);

            if (!quantityCalculationObjectServiceModel.IsGroupWithSubstitutes)
            {
                return quantityCalculationObjectServiceModel;
            }

            foreach (var eqType in this.EquipmentTypeQuantities)
            {
                // possible performance overhead
                var eqTypeValue = (double)((object)eqType.Value);

                quantityCalculationObjectServiceModel.EquipmentTypeQuantities[eqType.Key] =
                    (int)Math.Ceiling(eqTypeValue * rateMultiplier);
            }

            return quantityCalculationObjectServiceModel;
        }
    }
}
