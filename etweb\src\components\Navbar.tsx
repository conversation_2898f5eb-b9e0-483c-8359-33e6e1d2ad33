import { useState, useContext } from "react";
import { useNavigate, NavLink, useLocation } from "react-router";
import { useTranslation } from "react-i18next";
import { Menu, User } from "lucide-react";
import Logo from "/logo_negative.png";
import LanguageSelector from "./LanguageSelector";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ModeToggle } from "./mode-toggle";
import { navigation } from "./utils/navigation";
import { AuthContext } from "@/components/context/AuthContext";
import { logout } from "../data/query";
import type { BaseResponseModel } from "@/data/etws";

export default function Navbar() {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { user, dispatch } = useContext(AuthContext);

  const closeMobileMenu = () => setIsMobileMenuOpen(false);
  const isActivePath = (href: string) => {
    return (
      location.pathname === href || location.pathname.startsWith(href + "/")
    );
  };
  const isActiveSection = (items: Array<{ href: string }>) => {
    return items.some((item) => isActivePath(item.href));
  };

  const getDefaultAccordionValues = () => {
    return navigation
      .filter((section) => isActiveSection(section.items))
      .map((section) => section.name);
  };

  const userLogout = async () => {
    const res = await logout();
    const result = res.data as BaseResponseModel;
    if(result.success) {
      dispatch({ type: "LOGOUT" });
      navigate("/login");
    }
  };

  return (
    <nav className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        {/* Mobile Menu Button - Only show if user is authenticated */}
        {user && (
          <div className="flex items-center lg:hidden">
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="hover:bg-accent">
                  <Menu className="h-6 w-6" />
                  <span className="sr-only">Open navigation menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-80 p-0 flex flex-col">
                {/* Header */}
                <div className="flex h-16 items-center justify-between border-b px-6 bg-background">
                  <img
                    className="h-8 w-auto cursor-pointer transition-transform hover:scale-105"
                    src={Logo}
                    alt="VIVACOM"
                    onClick={() => {
                      navigate("/");
                      closeMobileMenu();
                    }}
                  />
                </div>
                {/* Mobile Navigation */}
                <div className="flex-1 overflow-y-auto px-4 py-6">
                  <Accordion
                    type="multiple"
                    className="w-full"
                    defaultValue={getDefaultAccordionValues()}
                  >
                    {navigation.map((section) => (
                      <AccordionItem
                        key={section.name}
                        value={section.name}
                        className="border-b border-border/40"
                      >
                        <AccordionTrigger
                          className={`hover:no-underline px-2 py-3 ${
                            isActiveSection(section.items)
                              ? "text-primary font-medium"
                              : "text-foreground"
                          }`}
                        >
                          <span className="text-sm font-semibold">
                            {t(section.name)}
                          </span>
                        </AccordionTrigger>
                        <AccordionContent className="px-2 pb-4">
                          <div className="space-y-1">
                            {section.items.map((item) => (
                              <NavLink
                                key={item.name}
                                to={item.href}
                                onClick={closeMobileMenu}
                                className={({ isActive }) =>
                                  `group flex items-center rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                                    isActive || isActivePath(item.href)
                                      ? "bg-primary text-primary-foreground shadow-sm"
                                      : "text-foreground hover:bg-accent hover:text-accent-foreground"
                                  }`
                                }
                              >
                                {({ isActive }) => (
                                  <div className="flex items-center space-x-3">
                                    <div
                                      className={`h-1.5 w-1.5 rounded-full transition-all duration-200 ${
                                        isActive || isActivePath(item.href)
                                          ? "bg-primary-foreground"
                                          : "bg-muted-foreground/40 group-hover:bg-accent-foreground"
                                      }`}
                                    />
                                    <span className="truncate">
                                      {t(item.name)}
                                    </span>
                                  </div>
                                )}
                              </NavLink>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </div>
              {/* Mobile Footer */}
              <div className="mt-auto border-t bg-muted/30 px-4 py-4">
                <div className="flex items-center justify-between">
                    <LanguageSelector />
                    <ModeToggle />
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        )}
        {/* Logo */}
        <div className="flex flex-1 items-center justify-center lg:flex-none lg:justify-start">
          <img
            className="h-10 w-auto cursor-pointer"
            src={Logo}
            alt="VIVACOM"
            onClick={() => navigate("/")}
          />
        </div>

        {/* Mobile actions for non-authenticated users */}
        {!user && (
          <div className="flex items-center space-x-2 sm:hidden">
            <LanguageSelector />
            <ModeToggle />
          </div>
        )}
        {/* Desktop Navigation */}
        {user && (
        <div className="hidden lg:flex lg:flex-1 lg:justify-center">
          <NavigationMenu>
            <NavigationMenuList>
              {navigation.map((section) => (
                <NavigationMenuItem key={section.name}>
                  <NavigationMenuTrigger
                    className={
                      isActiveSection(section.items)
                        ? "bg-accent text-accent-foreground"
                        : ""
                    }
                  >
                    {t(section.name)}
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid w-[400px] gap-2 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                      {section.items.map((item) => (
                        <NavLink
                          key={item.name}
                          to={item.href}
                          className={({ isActive }) =>
                            `group block select-none rounded-lg p-3 leading-none no-underline outline-none transition-all duration-200 hover:shadow-md ${
                              isActive || isActivePath(item.href)
                                ? "bg-primary text-primary-foreground shadow-sm"
                                : "hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                            }`
                          }
                        >
                          <div className="flex items-center space-x-3">
                            <div
                              className={`h-2 w-2 rounded-full transition-all duration-200 ${
                                isActivePath(item.href)
                                  ? "bg-primary-foreground"
                                  : "bg-muted-foreground/40 group-hover:bg-accent-foreground"
                              }`}
                            />
                            <div className="text-sm font-medium leading-none">
                              {t(item.name)}
                            </div>
                          </div>
                        </NavLink>
                      ))}
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>
              ))}
            </NavigationMenuList>
          </NavigationMenu>
        </div>
        )}
        {/* Right Side Actions */}
        <div className="flex items-center space-x-2">
          {/* Show language and theme toggles for non-authenticated users on desktop */}
          {!user && (
            <div className="hidden sm:flex sm:items-center sm:space-x-2">
              <LanguageSelector />
              <ModeToggle />
            </div>
          )}

          {/* Show language and theme toggles for authenticated users on desktop */}
          {user && (
            <div className="hidden lg:flex lg:items-center lg:space-x-2">
              <LanguageSelector />
              <ModeToggle />
            </div>
          )}

          {/* User Menu - Desktop */}
          {user && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center space-x-2 px-3">
                  <User className="h-4 w-4" />
                  <span className="hidden sm:inline-block text-sm font-medium">
                    {user.username}
                  </span>
                </Button>
              </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
                <DropdownMenuItem>
                <a href="#" className="w-full">
                  {t("profile")}
                </a>
                </DropdownMenuItem>
                <DropdownMenuItem>
                <a href="#" className="w-full">
                  {t("settings")}
                </a>
                </DropdownMenuItem>
              <DropdownMenuItem>
                <a href="#" onClick={userLogout} className="w-full">
                  {t("signOut")}
                </a>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Login Button for non-authenticated users */}
          {!user && (
            <Button
              onClick={() => navigate("/login")}
              variant="default"
              size="sm"
              className="ml-2"
            >
              {t("signIn")}
            </Button>
          )}
        </div>
      </div>
    </nav>
  );
}
