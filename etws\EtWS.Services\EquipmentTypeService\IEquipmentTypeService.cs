﻿namespace EtWS.Services.EquipmentTypeService
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;

    public interface IEquipmentTypeService : IService
    {
        Task<EquipmentTypeResponseModel> GetEquipmentTypeByIdAsync(int id);

        Task<IEnumerable<EquipmentTypeResponseModel>> GetEquipmentTypesAsync();

        Task<int> GetEquipmentTypeIdAsync(string sapMaterialNum);

        Task<IEnumerable<EquipmentTypeResponseModel>> GetEquipmentTypesAsync(string sendMethod);

        Task<IEnumerable<EquipmentTypeConciseDto>> GetConciseEquipmentTypesAsync(ConciseEquipmentTypesDataRequestModel request);

        Task<IDictionary<string, int>> GetEquipmentTypesLastMonthQuantitiesAsync();
    }
}
