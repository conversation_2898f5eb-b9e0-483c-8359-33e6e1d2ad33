﻿using EtDb.ApiClients.EtDb.Models;
using EtDb.DataHandlers.Models;

namespace EtDb.DataHandlers.Interfaces
{
    public interface IQuantityCalculationObjectDataHandler
    {
        IQueryable<QuantityCalculationObject> GetAll();

        Task BulkInsertAsync(IEnumerable<QuantityCalculationObject> collectionToBeUpdated);

        void SeedInitialData();

        Task<QuantityCalculationObject> GetByIdAsync(int id);

        IQueryable<QuantityCalculationObject> GetEquipmentGroupQuantityCalculationObjects(bool onlyWithSerialNumbers = false);

        IQueryable<QuantityCalculationObject> GetEquipmentTypeQuantityCalculationObjects(bool onlyWithSerialNumbers = false);

        IQueryable<QuantityCalculationObject> GetAllOnlyWithSerialNumber();

        QuantityCalculationObject Add(QuantityCalculationObject quantityCalculationObject);

        QuantityCalculationObject Update(QuantityCalculationObject quantityCalculationObject);

        Task<IEnumerable<QuantityCalculationObjectAvailableQuantityModel>> GetAvailableQuantitiesByQuantityCalculationObjectEquipmentGroupsFromSnapshotAsync(IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectsCollection, DateTime snapshotDate);

        Task<IEnumerable<QuantityCalculationObjectAvailableQuantityModel>> GetAvailableQuantitiesByQuantityCalculationObjectEquipmentTypesFromSnapshotAsync(IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectsCollection, DateTime snapshotDate);

        Task<IEnumerable<QuantityCalculationObjectAvailableQuantityModel>> GetAvailableQuantitiesByQuantityCalculationObjectFromSnapshotAsync(IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectsCollection, DateTime snapshotDate);

        Task<IEnumerable<QuantityCalculationObjectAvailableQuantityModel>> GetAvailableQuantitiesByQuantityCalculationObjectEquipmentGroupsAsync(IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectsCollection, bool validEquipmentOnly = true);

        Task<IEnumerable<QuantityCalculationObjectAvailableQuantityModel>> GetAvailableQuantitiesByQuantityCalculationObjectEquipmentTypesAsync(IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectsCollection, bool validEquipmentOnly = true);

        Task<IEnumerable<QuantityCalculationObjectAvailableQuantityModel>> GetAvailableQuantitiesByQuantityCalculationObjectAsync(IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectsCollection, bool validEquipmentOnly = true);

        Task<IDictionary<QuantityCalculationObjectIdentifier, QuantityCalculationObjectServiceModel<int>>> GetQuantityCalculationObjectServiceModelsAsync(IEnumerable<QuantityCalculationObjectIdentifier> quantityCalculationObjectIdentifiers);

        Task<HashSet<QuantityCalculationObjectIdentifier>> GetQuantityCalculationObjectIdentifiersAsync(bool onlyWithSerialNumbers = false);

        Task BulkUpdateAsync(IEnumerable<QuantityCalculationObject> collectionToBeUpdated);

        Task<FilteredDataModel<QuantityCalculationObject>> GetFilteredQuantityCalculationObjectsAsync(string sortBy, string sortDir, int pageNumber, int pageSize, string query);
    }
}
