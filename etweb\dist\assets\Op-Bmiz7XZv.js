import{J as g,v as b,w as N,r as y,j as e,L as I,K as u}from"./index-CFpwFZya.js";const E=()=>{var x,m;const{data:t,isLoading:n,error:i}=g(),{t:d}=b(),{register:h,watch:p,setValue:l,formState:{errors:o}}=N(),j=!!o.opId,f=(m=(x=o.opId)==null?void 0:x.message)==null?void 0:m.toString(),v=p("userId"),a=t==null?void 0:t.find(s=>s.id===v);return y.useEffect(()=>{a!=null&&a.opcode&&l("deliveryShop",a.opcode)},[a,l]),e.jsxs("div",{className:"space-y-2",children:[e.jsx(I,{htmlFor:"opId",children:d("selectATechnicianFromTheList")}),e.jsxs("div",{children:[n?e.jsxs("div",{className:"flex items-center space-x-2 py-3",children:[e.jsx("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent"}),e.jsx("span",{className:"text-sm text-gray-500",children:d("loading")})]}):i?e.jsxs("div",{className:"flex items-center rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-sm text-destructive",children:[e.jsx(u,{className:"mr-2 h-5 w-5"}),e.jsx("span",{children:i.message})]}):e.jsxs("select",{id:"userId",...h("userId"),className:"w-full rounded-lg border px-4 py-3 text-sm outline-none transition-all focus:ring-2",children:[e.jsx("option",{value:"",children:d("selectOperation")}),t&&Object.entries(t.reduce((s,c)=>{const r=c.iptuName||"Other";return s[r]||(s[r]=[]),s[r].push(c),s},{})).map(([s,c])=>e.jsx("optgroup",{label:s,children:c.map(r=>e.jsxs("option",{value:r.id||"",children:[r.displayName," - ",r.opcode]},r.id))},s))]}),j&&!n&&!i&&e.jsxs("div",{className:"mt-1.5 flex items-center text-sm text-destructive",children:[e.jsx(u,{className:"mr-1.5 h-4 w-4"}),e.jsx("p",{children:d(f||"")})]})]})]})};export{E as default};
