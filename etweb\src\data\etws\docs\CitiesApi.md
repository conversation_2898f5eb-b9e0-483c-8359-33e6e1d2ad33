# CitiesApi

All URIs are relative to *http://microit9app1.drcenter.btk.bg:26121/et-ws*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**apiCitiesActivatePut**](#apicitiesactivateput) | **PUT** /api/cities/activate | Activates selected cities in the database.|
|[**apiCitiesAddPost**](#apicitiesaddpost) | **POST** /api/cities/add | Adds city to the database.|
|[**apiCitiesBlockPut**](#apicitiesblockput) | **PUT** /api/cities/block | Blocks selected cities in the database.|
|[**apiCitiesByIdGet**](#apicitiesbyidget) | **GET** /api/cities/{id} | Gets city data by its id.|
|[**apiCitiesRegionByIdGet**](#apicitiesregionbyidget) | **GET** /api/cities/region/{id} | Returns the region of the city as string.|
|[**apiCitiesSapCityCodeAndClusterByCityIdGet**](#apicitiessapcitycodeandclusterbycityidget) | **GET** /api/cities/sap-city-code-and-cluster/{cityId} | Gets a city by its id (request parameter) and returns the SAPCityCode and Cluster of the city.|
|[**apiCitiesSearchPost**](#apicitiessearchpost) | **POST** /api/cities/search | Search cities based on criterias.|
|[**apiCitiesSelectListGet**](#apicitiesselectlistget) | **GET** /api/cities/select-list | |
|[**apiCitiesUpdateByIdPut**](#apicitiesupdatebyidput) | **PUT** /api/cities/update/{id} | Updates an existing city in the databse.|

# **apiCitiesActivatePut**
> apiCitiesActivatePut()


### Example

```typescript
import {
    CitiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CitiesApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)
let requestBody: Array<number>; //The ids of the cities, that needs to be activated. (optional)

const { status, data } = await apiInstance.apiCitiesActivatePut(
    inputRequestId,
    inputTimestamp,
    requestBody
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **requestBody** | **Array<number>**| The ids of the cities, that needs to be activated. | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiCitiesAddPost**
> number apiCitiesAddPost(cityRequestModel)


### Example

```typescript
import {
    CitiesApi,
    Configuration,
    CityRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new CitiesApi(configuration);

let cityRequestModel: CityRequestModel; //The city data to save in the database.
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiCitiesAddPost(
    cityRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **cityRequestModel** | **CityRequestModel**| The city data to save in the database. | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**number**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiCitiesBlockPut**
> apiCitiesBlockPut()


### Example

```typescript
import {
    CitiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CitiesApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)
let requestBody: Array<number>; //The ids of the cities, that needs to be blocked. (optional)

const { status, data } = await apiInstance.apiCitiesBlockPut(
    inputRequestId,
    inputTimestamp,
    requestBody
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **requestBody** | **Array<number>**| The ids of the cities, that needs to be blocked. | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiCitiesByIdGet**
> CityResponseModel apiCitiesByIdGet()


### Example

```typescript
import {
    CitiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CitiesApi(configuration);

let id: number; //The id of the city. (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiCitiesByIdGet(
    id,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | The id of the city. | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**CityResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiCitiesRegionByIdGet**
> string apiCitiesRegionByIdGet()


### Example

```typescript
import {
    CitiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CitiesApi(configuration);

let id: number; //The id of the city. (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiCitiesRegionByIdGet(
    id,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | The id of the city. | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**string**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiCitiesSapCityCodeAndClusterByCityIdGet**
> SapCityCodesResponseModel apiCitiesSapCityCodeAndClusterByCityIdGet()


### Example

```typescript
import {
    CitiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CitiesApi(configuration);

let cityId: number; //The id of the city. (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiCitiesSapCityCodeAndClusterByCityIdGet(
    cityId,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **cityId** | [**number**] | The id of the city. | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**SapCityCodesResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiCitiesSearchPost**
> CityResponseModelSearchResponseModel apiCitiesSearchPost(searchDataRequestModel)


### Example

```typescript
import {
    CitiesApi,
    Configuration,
    SearchDataRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new CitiesApi(configuration);

let searchDataRequestModel: SearchDataRequestModel; //The search parameters of the request.
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiCitiesSearchPost(
    searchDataRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **searchDataRequestModel** | **SearchDataRequestModel**| The search parameters of the request. | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**CityResponseModelSearchResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiCitiesSelectListGet**
> Array<CitiesSelectItemResponseModel> apiCitiesSelectListGet()


### Example

```typescript
import {
    CitiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CitiesApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiCitiesSelectListGet(
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**Array<CitiesSelectItemResponseModel>**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiCitiesUpdateByIdPut**
> apiCitiesUpdateByIdPut(cityRequestModel)


### Example

```typescript
import {
    CitiesApi,
    Configuration,
    CityRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new CitiesApi(configuration);

let id: number; //The id of the city, being updated. (default to undefined)
let cityRequestModel: CityRequestModel; //The new city data.
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiCitiesUpdateByIdPut(
    id,
    cityRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **cityRequestModel** | **CityRequestModel**| The new city data. | |
| **id** | [**number**] | The id of the city, being updated. | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

