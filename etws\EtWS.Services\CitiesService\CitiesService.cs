﻿namespace EtWS.Services.CitiesService
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CityModels;
    using EtWS.Models.Responses.CommonModels;

    public class CitiesService : ICitiesService
    {
        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<IETDB> etDb;

        public CitiesService(Lazy<IMapper> mapper, Lazy<IETDB> etDb)
        {
            this.mapper = mapper;
            this.etDb = etDb;
        }

        public async Task<T> GetCityAsync<T>(int id)
        {
            var city = await this.etDb.Value.ApiCitiesGetAsync(id);
            return this.mapper.Value.Map<T>(city);
        }

        public async Task<SearchResponseModel<CityResponseModel>> GetCitiesAsync(SearchDataRequestModel request)
        {
            var dbRequest = this.mapper.Value.Map<SearchRequestModel>(request);
            var filteredCities = await this.etDb.Value.ApiCitiesSearchPostAsync(body: dbRequest);
            var citiesResponse = this.mapper.Value.Map<SearchResponseModel<CityResponseModel>>(filteredCities);

            return citiesResponse;
        }

        public async Task<IEnumerable<CitiesSelectItemResponseModel>> GetCitiesSelectListAsync()
        {
            var allCities = await this.etDb.Value.ApiCitiesSearchPostAsync(body: new SearchRequestModel
            {
                PageSize = 0, // returns all cities
            });
            var citiesResponse = this.mapper.Value.Map<IEnumerable<CitiesSelectItemResponseModel>>(allCities);

            return citiesResponse;
        }

        public async Task<int> AddCityAsync(CityRequestModel request)
        {
            var dbRequest = this.mapper.Value.Map<CitiesRequestModel>(request);
            return await this.etDb.Value.ApiCitiesAddPostAsync(body: dbRequest);
        }

        public async Task UpdateCityAsync(int id, CityRequestModel request)
        {
            var dbRequest = this.mapper.Value.Map<CitiesRequestModel>(request);
            await this.etDb.Value.ApiCitiesUpdatePutAsync(id: id, body: dbRequest);
        }

        public async Task BlockCitiesAsync(List<int> cityIds)
        {
            await this.etDb.Value.ApiCitiesBlockPutAsync(body: cityIds);
        }

        public async Task ActivateCitiesAsync(List<int> cityIds)
        {
            await this.etDb.Value.ApiCitiesActivatePutAsync(body: cityIds);
        }

        public async Task<string> GetCityRegionByIdAsync(int cityId)
        {
            return await this.etDb.Value.ApiCitiesRegionGetAsync(cityId);
        }
    }
}
