import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useValidateServiceId } from "../../data/query";
import { useCallback, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AlertCircle, CheckCircle2, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

const ServiceId = () => {
  const { t } = useTranslation();
  const { mutate, isPending } = useValidateServiceId();
  const [apiError, setApiError] = useState<string | null>(null);
  const [isValid, setIsValid] = useState<boolean | null>(null);

  const {
    register,
    watch,
    formState: { errors },
  } = useFormContext();

  const serviceId = watch("serviceId");

  const validateWithApi = useCallback(
    (value: string) => {
      if (!value || value.trim() === "") {
        setApiError(null);
        setIsValid(null);
        return;
      }

      // Reset states when starting validation
      setApiError(null);
      setIsValid(null);

      mutate(value.trim(), {
        onSuccess: (valid) => {
          if (valid.success === true) {
            setApiError(null);
            setIsValid(true);
          } else {
            setApiError("invalidServiceId");
            setIsValid(false);
          }
        },
        onError: (error: Error) => {
          const anyError = error as  { response?: { data?: { message?: string } }  };
          const errorMsg = anyError?.response?.data?.message || error.message || "validationError";
          setApiError(errorMsg);
          setIsValid(false);
        },
      });
    },
    [mutate]
  );

  useEffect(() => {
    const timer = setTimeout(() => {
      validateWithApi(serviceId);
    }, 500); // Increased debounce time to reduce API calls

    return () => clearTimeout(timer);
  }, [serviceId, validateWithApi]);

  const hasFormError = !!errors.serviceId;
  const hasApiError = !!apiError;
  const hasError = hasFormError || hasApiError;
  const formErrorMessage = errors.serviceId?.message?.toString();
  const showValidationState = serviceId && serviceId.trim() !== "";

  return (
    <div className="space-y-2">
      <Label htmlFor="serviceId">{t("enterTheService")}</Label>
      <div className="relative">
        <Input
          id="serviceId"
          type="text"
          {...register("serviceId")}
          placeholder="TV.123, LN.123, LN-P.123"
          className={cn(
            hasError && "border-destructive focus-visible:ring-destructive/20",
            isValid && !hasError && "border-green-500 focus-visible:ring-green-500/20"
          )}
        />
        {/* Status indicator */}
        {showValidationState && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            {isPending && (
              <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            )}
            {!isPending && isValid && !hasError && (
              <CheckCircle2 className="h-4 w-4 text-green-600" />
            )}
            {!isPending && hasError && (
              <AlertCircle className="h-4 w-4 text-destructive" />
            )}
          </div>
        )}
      </div>

      {/* Error messages */}
      {hasFormError && (
        <div className="flex items-center text-sm text-destructive">
          <AlertCircle className="mr-1.5 h-4 w-4" />
          {t(formErrorMessage || "")}
        </div>
      )}
      
      {hasApiError && !hasFormError && (
        <div className="flex items-center text-sm text-destructive">
          <AlertCircle className="mr-1.5 h-4 w-4" />
          {t(apiError || "")}
        </div>
      )}

      {/* Success message */}
      {!isPending && isValid && !hasError && showValidationState && (
        <div className="flex items-center text-sm text-green-600">
          <CheckCircle2 className="mr-1.5 h-4 w-4" />
          {t("serviceIdValidated")}
        </div>
      )}

      {/* Loading message */}
      {isPending && (
        <div className="flex items-center text-sm text-muted-foreground">
          <Loader2 className="mr-1.5 h-4 w-4 animate-spin" />
          {t("validating")}
        </div>
      )}
    </div>
  );
};

export default ServiceId;
