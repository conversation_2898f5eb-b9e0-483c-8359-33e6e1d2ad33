# AvailableEquipmentDataResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**itemId** | **number** |  | [optional] [default to undefined]
**userId** | **string** |  | [optional] [default to undefined]
**itemQuantity** | **number** |  | [optional] [default to undefined]
**isDaily** | **boolean** |  | [optional] [default to undefined]
**isReserved** | **boolean** |  | [optional] [default to undefined]
**isWaitingForConfirmation** | **boolean** |  | [optional] [default to undefined]
**item** | [**ItemDataResponseModel**](ItemDataResponseModel.md) |  | [optional] [default to undefined]

## Example

```typescript
import { AvailableEquipmentDataResponseModel } from './api';

const instance: AvailableEquipmentDataResponseModel = {
    id,
    itemId,
    userId,
    itemQuantity,
    isDaily,
    isReserved,
    isWaitingForConfirmation,
    item,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
