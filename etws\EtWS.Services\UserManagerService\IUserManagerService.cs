﻿namespace EtWS.Services.UserManagerService
{
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.UserModels;

    public interface IUserManagerService : IService
    {
        Task<SearchUserDataResponseModel> GetCurrentUserAsync();

        Task<SearchUserDataResponseModel> GetUserByUserIdAsync(string userId);

        Task<SearchUserDataResponseModel> GetUserByUsernameAsync(string username);

        Task<string> GetUserADAccountAsync(string userId);

        Task<UsersListResponseModel> GetUsersSelectListAsync(IEnumerable<string> currentUserRoles);

        Task<bool> IsUserMolAsync(string userId);

        Task<ICollection<UserDataConciseResponseModel>> GetAllMolsAsync();

        Task<SearchResponseModel<SearchUserDataResponseModel>> GetUsersAsync(SearchDataRequestModel request, IEnumerable<string> userRoles);

        Task<ICollection<UserDataResponseModel>> GetUserMostFrequentTransfersToAsync(string fromUserId);

        Task UpdateUserAsync(EditUserRequest request);

        Task ActivateSelectedUsersAsync(IEnumerable<string> selectedUsers, string userId);

        Task BlockSelectedUsersAsync(IEnumerable<string> selectedUsers, string userId);

        Task UpdateUserNotificationAsync(string userId);
    }
}
