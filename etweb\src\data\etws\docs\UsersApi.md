# UsersApi

All URIs are relative to *http://microit9app1.drcenter.btk.bg:26121/et-ws*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**apiUsersActivateUsersPut**](#apiusersactivateusersput) | **PUT** /api/users/activate-users | |
|[**apiUsersAdAccountByUserIdGet**](#apiusersadaccountbyuseridget) | **GET** /api/users/ad-account/{userId} | |
|[**apiUsersAllMolsGet**](#apiusersallmolsget) | **GET** /api/users/all-mols | |
|[**apiUsersBlockUsersPut**](#apiusersblockusersput) | **PUT** /api/users/block-users | |
|[**apiUsersByUserIdGet**](#apiusersbyuseridget) | **GET** /api/users/{userId} | |
|[**apiUsersIsUserMolByUserIdGet**](#apiusersisusermolbyuseridget) | **GET** /api/users/is-user-mol/{userId} | |
|[**apiUsersPut**](#apiusersput) | **PUT** /api/users | |
|[**apiUsersSearchPost**](#apiuserssearchpost) | **POST** /api/users/search | |
|[**apiUsersUserByUsernameByUsernameGet**](#apiusersuserbyusernamebyusernameget) | **GET** /api/users/user-by-username/{username} | |
|[**apiUsersUsersSelectListGet**](#apiusersusersselectlistget) | **GET** /api/users/users-select-list | |

# **apiUsersActivateUsersPut**
> apiUsersActivateUsersPut(requestBody)


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let requestBody: Array<string>; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiUsersActivateUsersPut(
    requestBody,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **requestBody** | **Array<string>**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiUsersAdAccountByUserIdGet**
> string apiUsersAdAccountByUserIdGet()


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let userId: string; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiUsersAdAccountByUserIdGet(
    userId,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **userId** | [**string**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**string**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiUsersAllMolsGet**
> Array<UserDataConciseResponseModel> apiUsersAllMolsGet()


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiUsersAllMolsGet(
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**Array<UserDataConciseResponseModel>**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiUsersBlockUsersPut**
> apiUsersBlockUsersPut(requestBody)


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let requestBody: Array<string>; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiUsersBlockUsersPut(
    requestBody,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **requestBody** | **Array<string>**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiUsersByUserIdGet**
> SearchUserDataResponseModel apiUsersByUserIdGet()


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let userId: string; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiUsersByUserIdGet(
    userId,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **userId** | [**string**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**SearchUserDataResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiUsersIsUserMolByUserIdGet**
> boolean apiUsersIsUserMolByUserIdGet()


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let userId: string; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiUsersIsUserMolByUserIdGet(
    userId,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **userId** | [**string**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**boolean**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiUsersPut**
> apiUsersPut(editUserRequest)


### Example

```typescript
import {
    UsersApi,
    Configuration,
    EditUserRequest
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let editUserRequest: EditUserRequest; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiUsersPut(
    editUserRequest,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **editUserRequest** | **EditUserRequest**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiUsersSearchPost**
> SearchUserDataResponseModelSearchResponseModel apiUsersSearchPost(searchDataRequestModel)


### Example

```typescript
import {
    UsersApi,
    Configuration,
    SearchDataRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let searchDataRequestModel: SearchDataRequestModel; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiUsersSearchPost(
    searchDataRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **searchDataRequestModel** | **SearchDataRequestModel**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**SearchUserDataResponseModelSearchResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiUsersUserByUsernameByUsernameGet**
> SearchUserDataResponseModel apiUsersUserByUsernameByUsernameGet()


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let username: string; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiUsersUserByUsernameByUsernameGet(
    username,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **username** | [**string**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**SearchUserDataResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiUsersUsersSelectListGet**
> UsersListResponseModel apiUsersUsersSelectListGet()


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiUsersUsersSelectListGet(
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**UsersListResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

