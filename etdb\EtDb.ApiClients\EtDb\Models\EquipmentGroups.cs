﻿using System;
using System.Collections.Generic;

namespace EtDb.ApiClients.EtDb.Models
{
    public partial class EquipmentGroups
    {
        public EquipmentGroups()
        {
            EquipmentType = new HashSet<EquipmentType>();
            QuantityCalculationObject = new HashSet<QuantityCalculationObject>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public bool ContainsSubstitutes { get; set; }

        public virtual ICollection<EquipmentType> EquipmentType { get; set; }
        public virtual ICollection<QuantityCalculationObject> QuantityCalculationObject { get; set; }
    }
}
