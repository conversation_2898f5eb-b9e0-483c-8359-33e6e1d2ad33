# AvailableEquipmentDataResponseModelSearchResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**count** | **number** |  | [optional] [default to undefined]
**dataCollection** | [**Array&lt;AvailableEquipmentDataResponseModel&gt;**](AvailableEquipmentDataResponseModel.md) |  | [optional] [default to undefined]

## Example

```typescript
import { AvailableEquipmentDataResponseModelSearchResponseModel } from './api';

const instance: AvailableEquipmentDataResponseModelSearchResponseModel = {
    count,
    dataCollection,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
