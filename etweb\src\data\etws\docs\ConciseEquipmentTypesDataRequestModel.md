# ConciseEquipmentTypesDataRequestModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**equipmentTypeIds** | **Array&lt;number&gt;** |  | [default to undefined]
**serialNumbersRequired** | **boolean** |  | [optional] [default to undefined]

## Example

```typescript
import { ConciseEquipmentTypesDataRequestModel } from './api';

const instance: ConciseEquipmentTypesDataRequestModel = {
    equipmentTypeIds,
    serialNumbersRequired,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
