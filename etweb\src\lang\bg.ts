const bg = {
  translation: {    // Common translations
    common: {
      loading: "Зареждане...",
      error: {
        loadingData: "Възникна грешка при зареждане на данните",
        occurred: "Възникна грешка",
        retryButton: "Опитай отново"
      }
    },
    
    equipment: "Дейности с Оборудване",
    reports: "Справки",
    aggregations: "Агрегации",
    administration: "Администриране",
    transferCorrection: "Корекция на трансфер",
    requests: "Заявки",
    
    // Equipment submenu
    availableEquipment: "Налични артикули",
    dailyEquipment: "Дневно оборудване",
    transfer: "Трансфер",
    acceptance: "Приемане",
    cancellationOfTransfer: "Анулиране на трансфер",
    
    // Reports submenu
    equipmentMovement: "Движение на оборудване",
    criticalQuantitiesReport: "Справка за критични количества",
    availableEquipmentByOp: "Налично оборудване по ОП",
    unusedEquipment: "Неизползвано оборудване",
    equipmentTransfers: "Трансфери на оборудване",
    incorrectMovements: "Некоректни движения",
    waitingForConfirmation: "Чакащи потвърждение",
    itemsCount: "Брой артикули",
    
    // Administration submenu
    products: "Продукти",
    users: "Потребители",
    cities: "Градове",
    schenkers: "Шенкери",
    criticalQuantitiesAdministration: "Администриране на критични количества",
    sapElementCodes: "SAP елементни кодове",
    monthlyOrderQuantities: "Месечни поръчкови количества",
    substitutions: "Заместители",

    // Users page
    manageSystemUsers: "Управление на системни потребители и техните права",
    searchUsers: "Търсене на потребители...",
    noUsersFound: "Няма намерени потребители",
    errorLoadingUsers: "Грешка при зареждане на потребители",
    activate: "Активиране",
    block: "Блокиране",
    active: "Активен",
    blocked: "Блокиран",
    edit: "Редактиране",
    eln: "ЕЛН",
    fullName: "Пълно име",
    op: "ОП",
    region: "Регион",
    city: "Град",
    clientNumber: "Клиентски номер",
    status: "Статус",
    displayName: "Показвано име",
    iptuName: "IPTU име",
    adAccount: "AD акаунт",
    schenker: "Шенкер",
    selectSchenker: "Изберете шенкер...",
    selectStatus: "Изберете статус...",
    editUser: "Редактиране на потребител",
    editUserDescription: "Направете промени в информацията за потребителя тук.",
    save: "Запазване",
    saving: "Запазване...",
    cancel: "Отказ",
    showingResults: "Показване на {{start}}-{{end}} от {{total}} резултата",
    pageOfPages: "Страница {{current}} от {{total}}",
    filters: "Филтри",
    columnFilters: "Филтри по колони",
    clearAll: "Изчисти всички",
    firstName: "Име",
    surname: "Фамилия",
    enterEln: "Въведете ЕЛН...",
    enterFirstName: "Въведете име...",
    enterSurname: "Въведете фамилия...",
    sortBy: "Сортиране по",
    sortDirection: "Посока на сортиране",
    ascending: "Възходящо (А-Я)",
    descending: "Низходящо (Я-А)",
    familyName: "Бащино име",
    enterFamilyName: "Въведете бащино име...",
    enterClientNumber: "Въведете клиентски номер...",
    position: "Позиция",
    email: "Имейл",
    opcode: "ОП код",
    schenkerId: "Шенкер ID",
    dataBlocked: "Данни блокирани",
    blockedByUserId: "Блокиран от потребител ID",
    hasPendingTransfer: "Има чакащ трансфер",
    select: "Изберете",
    enter: "Въведете",

    // Search operators
    equals: "Равно на",
    contains: "Съдържа",
    endsWith: "Завършва с",
    startsWith: "Започва с",

    // Pagination
    noResults: "Няма намерени резултати",
    itemsPerPage: "Елементи на страница",
    firstPage: "Първа страница",
    previousPage: "Предишна страница",
    nextPage: "Следваща страница",
    lastPage: "Последна страница",

    // Requests submenu
    newRequest: "Нова заявка",
    allRequests: "Всички заявки",
    monthlyOrderQuantitiesSum: "Сума на месечни поръчкови количества",

    welcome: "Добре дошли в",
    ET: "E.T.",
    profile: "Профил",
    settings: "Настройки",
    signOut: "Изход",
    signIn: "Вход",
    transferTo: "Прехвърляне към",
    serviceId: "Сериен номер на устройството",
    enterSN: "Моля въведете сериен номер",
    selectAnOption: "Изберете опция",
    technician: "Tехник",
    client: "Клиент",
    centralWarehouse: "Централен склад",
    SAPVirtualWarehouse: "SAP виртуален склад",
    enterFirstAndLastName: "Моля въведете име и фамилия на техник",
    firstAndLastName: "Име и фамилия",
    enterTheService: "Моля въведете номер на услуга (например TV.123, LN.123)",    selectATechnicianFromTheList: "Изберете техник от списъка:",
    selectTechnician: "Изберете техник",
    removeTransfer: "Премахване на трансфер по дата",
    selectRemovalDate: "Изберете дата за премахване",
    pleaseSelectADate: "Моля изберете дата",
    serviceIdError: "Серийния номер може да съдържа само букви на латински и цифри",
    transferAll: "Трансфер на всички",
    transferCorrectionFromExel: "Корекция на трансфер от Excel",
    reviewDataFromExcel: "Преглед на данните от Ексел файла:",
    // New keys
    serialNumber: "Сериен номер",
    validating: "Проверка...",
    serialNumberValidated: "Серийният номер е валиден",
    serialNumberError: "Серийният номер може да съдържа само латински букви и цифри",
    serialNumberRequired: "Серийният номер е задължителен",
    transferToRequired: "Моля изберете дестинация за трансфер",
    technicianNameRequired: "Името на техника е задължително",
    serviceIdRequired: "Сервизният номер е задължителен",
    opIdRequired: "Операцията е задължителна",
    removalDateRequired: "Датата на премахване е задължителна",
    selectOperation: "Изберете операция",
    operationId: "ID на операция",
    loading: "Зареждане...",
    submit: "Изпращане",
    processing: "Обработване...",
    transferSuccessful: "Трансферът е изпълнен успешно",
    transferFailed: "Трансферът не бе успешен. Моля, опитайте отново.",
    transferToCentralWarehouse: "Оборудването ще бъде прехвърлено в централния склад.",
    transferSAPVirtualWarehouse: "Оборудването ще бъде прехвърлено в SAP виртуалния склад.",
    technicianName: "Име на техник",
    searchTechnicians: "Търсене на техници",
    loginErrorStatus: "Неправилно потребителско име или парола.",
      
    // HomePage
    getStarted: "Започнете",
    learnMore: "Научете повече",
    keyFeatures: "Основни функции",
    fastProcessing: "Бързо обработване",
    fastProcessingDesc: "Светкавично бързо проследяване на оборудването и актуализации в реално време",
    premiumQuality: "Премиум качество",
    premiumQualityDesc: "Корпоративна надеждност и стандарти за сигурност",
    teamCollaboration: "Екипна работа",
    teamCollaborationDesc: "Безпроблемни инструменти за сътрудничество за целия ви екип",
    readyToStart: "Готови ли сте да започнете?",
    readyToStartDesc: "Присъединете се към хилядите компании, които вече използват нашата система за проследяване на оборудването",
    startYourJourney: "Започнете своето пътешествие",
    advancedEMS: "Усъвършенствана система за проследяване и управление на оборудването",

    // UserEditForm translations
    userEditForm: {
      title: "Редакция на потребител",
      description: "Редактирайте информацията за потребителя",
      fields: {
        eln: "ЕЛН",
        firstName: "Име",
        surname: "Презиме",
        familyName: "Фамилия",
        op: "ОР",
        city: "Град",
        region: "Регион",
        molStatus: "МОЛ на ОП",
        clientNumber: "Клиентски номер",
        phoneNumber: "Мобилен номер"
      },
      placeholders: {
        selectOp: "Изберете ОР",
        selectCity: "Изберете град",
        selectStatus: "Изберете статус"
      },
      options: {
        yes: "Да",
        no: "Не"
      },
      buttons: {
        cancel: "Откажи",
        save: "Запази",
        saving: "Запазване..."
      }
    },
  },
};

export default bg;
