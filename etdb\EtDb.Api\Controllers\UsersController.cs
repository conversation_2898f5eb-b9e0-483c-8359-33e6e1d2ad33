﻿namespace EtDb.Api.Controllers
{
    using System.Collections.Generic;

    using EtDb.Api.Infrastructure.Utils;
    using EtDb.ApiClients.EtDb.Enums;
    using EtDb.Infrastructure.Enumerations;
    using EtDb.Models.Requests;
    using EtDb.Models.Requests.UserModels;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.EquipmentModels;
    using EtDb.Models.Responses.UserModels;
    using EtDb.Services.Interfaces;

    public class UsersController : BaseApiController
    {
        private readonly Lazy<IUserManagerService> userManagerService;

        public UsersController(Lazy<IUserManagerService> userManagerService)
        {
            this.userManagerService = userManagerService;
        }

        /// <summary>
        /// Gets user data by provided user id.
        /// </summary>
        /// <param name="userId">The id of the user.</param>
        /// <returns>User data response model.</returns>
        [HttpGet("{userId}")]
        [ProducesResponseType(typeof(SearchUserResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchUserResponseModel>> GetUserByIdAsync([Required, FromRoute] string userId)
        {
            try
            {
                var user = await this.userManagerService.Value.GetUserByIdAsync(userId);
                return user;
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Gets user ad account name.
        /// </summary>
        /// <param name="userId">The id of the user.</param>
        /// <returns>The user ad account as string.</returns>
        [HttpGet("ad-account/{userId}")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<string>> GetUserAdAccountAsync([Required, FromRoute] string userId)
        {
            try
            {
                var userAdAccount = await this.userManagerService.Value.GetUserAdAccountAsync(userId);
                return userAdAccount;
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Gets user data by provided username.
        /// </summary>
        /// <param name="username">The username of the user.</param>
        /// <returns>User data response model.</returns>
        [HttpGet("user-by-username/{username}")]
        [ProducesResponseType(typeof(SearchUserResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchUserResponseModel>> GetUserByUsernameAsync([Required, FromRoute] string username)
        {
            try
            {
                var user = await this.userManagerService.Value.GetUserByUsernameAsync(username);
                return user;
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Returns all users based on provided collection of elns.
        /// </summary>
        /// <param name="elns">Collection of users elns.</param>
        /// <returns>Users response data.</returns>
        [HttpPost("users-by-eln-collection")]
        [ProducesResponseType(typeof(IEnumerable<UserResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<UserResponseModel>>> GetUsersByUserElnsCollectionAsync([Required, FromBody] IEnumerable<string> elns)
        {
            try
            {
                var users = this.userManagerService.Value.GetUsersByUserElnsCollection(elns);
                return this.Ok(await Task.FromResult(users));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Returns all users based on provided collection of custom iptu names and user's schenker id.
        /// </summary>
        /// <param name="customIptuNames">Collection of custom iptu names.</param>
        /// <param name="userSchenkerId">The user schenker id.</param>
        /// <returns>Users response data.</returns>
        [HttpPost("users-by-custom-iptu-collection-and-user-schenker-id")]
        [ProducesResponseType(typeof(IEnumerable<UserResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<UserResponseModel>>> FilterUsersByIptuAndSchenkerAsync([Required, FromBody] IEnumerable<string> customIptuNames, int? userSchenkerId)
        {
            try
            {
                var users = this.userManagerService.Value.FilterUsersByIptuAndSchenker(customIptuNames, userSchenkerId);
                return this.Ok(await Task.FromResult(users));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Checks if a user is mol.
        /// </summary>
        /// <param name="userId">The id of the user.</param>
        /// <returns>Boolean - true if the user is mol.</returns>
        [HttpGet("is-user-mol/{userId}")]
        [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<bool>> IsUserMolAsync([Required, FromRoute] string userId)
        {
            try
            {
                return await this.userManagerService.Value.IsUserMolAsync(userId);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Gets data for all mols in database.
        /// </summary>
        /// <returns>UserConciseResponseModel data.</returns>
        [HttpGet("all-mols")]
        [ProducesResponseType(typeof(IEnumerable<UserConciseResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<UserConciseResponseModel>>> GetAllMolsAsync()
        {
            try
            {
                return this.Ok(await Task.FromResult(this.userManagerService.Value.GetAllMols()));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Gets most frequest transfers to for a given 'from' user.
        /// </summary>
        /// <param name="fromUserId">The id of the user for which the data is extracted for. The user id is checked against FromUserId column.</param>
        /// <returns>UserResponseModel.</returns>
        [HttpGet("most-frequent-fransfers-to")]
        [ProducesResponseType(typeof(IEnumerable<UserResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<UserResponseModel>>> GetUserMostFrequentTransfersToAsync([Required, FromQuery] string fromUserId)
        {
            try
            {
                return this.Ok(await Task.FromResult(this.userManagerService.Value.GetUserMostFrequentTransfersTo(fromUserId)));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("search")]
        [ProducesResponseType(typeof(SearchResponseModel<SearchUserResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<SearchUserResponseModel>>> SearchUsersAsync([Required] SearchUsersRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.userManagerService.Value.SearchUsers(request);
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Updates user in database.
        /// </summary>
        /// <param name="request">The edit user request model.</param>
        /// <returns>ActionResult.</returns>
        [HttpPut]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateUserAsync([Required, FromBody] EditUsersRequestModel request)
        {
            try
            {
                if (!this.ModelState.IsValid)
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    throw new InvalidOperationException(requestErrorMessage);
                }

                await this.userManagerService.Value.UpdateUserAsync(request);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Activate selected users.
        /// </summary>
        /// <param name="selectedUsers">The ids if the users, selected for activation.</param>
        /// <param name="userId">The id of the user, performing the activation.</param>
        /// <returns>ActionResult.</returns>
        [HttpPut("activate-users")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> ActivateSelectedUsersAsync([Required] IEnumerable<string> selectedUsers, [Required] string userId)
        {
            try
            {
                await this.userManagerService.Value.ActivateSelectedUsersAsync(selectedUsers, userId);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Block selected users.
        /// </summary>
        /// <param name="selectedUsers">The ids if the users, selected for blocking.</param>
        /// <param name="userId">The id of the user, performing the blocking.</param>
        /// <returns>ActionResult.</returns>
        [HttpPut("block-users")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> BlockSelectedUsersAsync([Required] IEnumerable<string> selectedUsers, [Required] string userId)
        {
            try
            {
                await this.userManagerService.Value.BlockSelectedUsersAsync(selectedUsers, userId);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPatch("update-user-notification")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateUserNotificationAsync(UpdateUserNotificationRequestModel request)
        {
            try
            {
                if (!Enum.TryParse(request.NotificationType, true, out NotificationType notificationTypeEnum))
                {
                    throw new ArgumentException("Notification type not valid!");
                }

                await this.userManagerService.Value.UpdateUserNotificationAsync(request.UserId, notificationTypeEnum, request.Text);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPatch("remove-user-notification")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> RemoveUserNotificationAsync(RemoveUserNotificationRequestModel request)
        {
            try
            {
                if (request.NotificationId == null && request.NotificationType == null)
                {
                    throw new ArgumentException("Notification id or notification type must be provided!");
                }

                if (request.NotificationId != null && request.NotificationId > 0)
                {
                    await this.userManagerService.Value.RemoveUserNotificationAsync(request.UserId, request.NotificationId.Value);
                    return this.Ok();
                }

                // else notification type is provided
                if (!Enum.TryParse(request.NotificationType, true, out NotificationType notificationTypeEnum))
                {
                    throw new ArgumentException("Notification type not valid!");
                }

                await this.userManagerService.Value.RemoveUserNotificationAsync(request.UserId, notificationTypeEnum);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
