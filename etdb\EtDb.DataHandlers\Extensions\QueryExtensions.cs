﻿namespace EtDb.DataHandlers.Extensions
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Reflection;
    using EtDb.DataHandlers.Enums;
    using EtDb.Infrastructure.Constants;

    public static class QueryExtensions
    {
        public static IQueryable<T> FilterBy<T>(this IQueryable<T> collection, string query)
        {
            if (!string.IsNullOrEmpty(query))
            {
                collection = GetCollectionByQuery(query, collection.SearchByCompiled);
            }

            return collection;
        }

        public static IQueryable<T> SearchByCompiled<T>(this IQueryable<T> collection, string propertyName, SearchTypes typeOfSearch, string searchValue)
        {
            var serachLambda = BuildQueryLambda<T>(propertyName, typeOfSearch, searchValue);
            return collection.Where(serachLambda.Compile()).AsQueryable();
        }

        public static IOrderedEnumerable<T> OrderBy<T>(this IEnumerable<T> collection, string key, string direction)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return collection.OrderBy(a => 1);
            }

            var sortLambda = BuildSortLambda<T>(key);
            if (direction?.ToLower() == FilterConstants.ASC)
            {
                return collection.OrderBy(sortLambda.Compile());
            }
            else
            {
                return collection.OrderByDescending(sortLambda.Compile());
            }
        }

        public static IQueryable<T> Page<T>(this IQueryable<T> query, int page, int pageDataSize)
        {
            int skip = Math.Max(pageDataSize * (page - 1), 0);
            return query.Skip(skip).Take(pageDataSize);
        }

        private static Expression<Func<T, object>> BuildSortLambda<T>(string key)
        {
            var properties = key.Split('.');
            Type fieldPropertyType = typeof(T);
            ParameterExpression parameterExpression = Expression.Parameter(fieldPropertyType, "t");
            Expression propertyExp = parameterExpression;
            if (properties.Length > 1)
            {
                propertyExp = BuildSortConditionalExpression(properties, propertyExp, fieldPropertyType, out fieldPropertyType);
            }
            else
            {
                BuildPropertyExpressionAndGetPropertyType(properties[0], ref fieldPropertyType, ref propertyExp);
            }

            LambdaExpression sortLambda = SelectPropertyLambdaExpression(parameterExpression, propertyExp, fieldPropertyType);

            return (Expression<Func<T, object>>)sortLambda;
        }

        private static Expression BuildSortConditionalExpression(string[] properties, Expression propertyExp, Type currentPropertyType, out Type fieldPropertyType)
        {
            Expression? conditionalExp = BuildConditionExpression(properties, ref propertyExp, ref currentPropertyType);
            if (conditionalExp == null)
            {
                throw new ArgumentException("BuildSortConditionalExpression: conditionalExp is null");
            }

            propertyExp = Expression.Condition(conditionalExp, propertyExp, Expression.Default(currentPropertyType));
            fieldPropertyType = currentPropertyType;

            return propertyExp;
        }

        private static LambdaExpression SelectPropertyLambdaExpression(ParameterExpression parameterExpression, Expression propertyExp, Type fieldPropertyType)
        {
            if (fieldPropertyType.IsGenericType && fieldPropertyType.GetGenericTypeDefinition() != typeof(Nullable<>))
            {
                Type fieldPropertyGenericType = fieldPropertyType.GetGenericArguments().First();
                ParameterExpression parameterCollectionExpression = Expression.Parameter(fieldPropertyGenericType, "tCollection");
                MemberExpression propertyCollectionExp = Expression.Property(parameterCollectionExpression, "Name");

                LambdaExpression selectLambda = Expression.Lambda(propertyCollectionExp, parameterCollectionExpression);
                MethodCallExpression selectExpression = Expression.Call(typeof(Enumerable), "Select", new[] { fieldPropertyGenericType, typeof(string) }, propertyExp, selectLambda);

                MethodCallExpression firstOrDefaultExpression = Expression.Call(GetGenericMethod("FirstOrDefault", 1, typeof(string)), selectExpression);

                return Expression.Lambda(Expression.Convert(firstOrDefaultExpression, typeof(object)), parameterExpression);
            }
            else
            {
                return Expression.Lambda(Expression.Convert(propertyExp, typeof(object)), parameterExpression);
            }
        }

        private static IQueryable<T> GetCollectionByQuery<T>(string query, Func<string, SearchTypes, string, IQueryable<T>> SearchByDelegate)
        {
            IQueryable<T> collection = null;
            var splitedElementsByComma = query.Split(',');
            foreach (var item in splitedElementsByComma)
            {
                string[] queryElements = item.Split('+');
                string queryField = queryElements[0];
                SearchTypes searchType = (SearchTypes)int.Parse(queryElements[1]);
                string queryText = queryElements[2].ToLower();

                collection = SearchByDelegate(queryField, searchType, queryText);
            }

            return collection;
        }

        private static Expression<Func<T, bool>> BuildQueryLambda<T>(string propertyName, SearchTypes typeOfSearch, string searchValue)
        {
            string[] properties = propertyName.Split('.');
            Type fieldPropertyType = typeof(T);
            ParameterExpression parameterExpression = Expression.Parameter(fieldPropertyType, "t");
            Expression propertyExp = parameterExpression;

            Expression? conditionalExp = null;
            if (properties.Length > 1)
            {
                conditionalExp = BuildQueryConditionExpression(properties, propertyExp, fieldPropertyType, out propertyExp, out fieldPropertyType);
            }
            else
            {
                BuildPropertyExpressionAndGetPropertyType(properties[0], ref fieldPropertyType, ref propertyExp);
            }

            ConstantExpression value = Expression.Constant(searchValue, typeof(string));

            MethodCallExpression toLowerExpression;
            Expression typeOfSearchExpression;
            if (fieldPropertyType.IsGenericType && fieldPropertyType.GetGenericTypeDefinition() != typeof(Nullable<>))
            {
                Type fieldPropertyGenericType = fieldPropertyType.GetGenericArguments().First();
                ParameterExpression parameterCollectionExpression = Expression.Parameter(fieldPropertyGenericType, "tCollection");
                MemberExpression propertyCollectionExp = Expression.Property(parameterCollectionExpression, "Name");

                toLowerExpression = Expression.Call(propertyCollectionExp, typeof(string).GetMethod("ToLower", Type.EmptyTypes) ?? throw new ArgumentException("BuildQueryLambda: ToLower method is missing."));
                typeOfSearchExpression = Expression.Call(toLowerExpression, typeof(string).GetMethod(typeOfSearch.ToString(), new[] { typeof(string) }) ?? throw new ArgumentException($"BuildQueryLambda: {typeOfSearch} method is missing."), value);
                LambdaExpression anyLambda = Expression.Lambda(typeOfSearchExpression, parameterCollectionExpression);
                MethodCallExpression anyExpression = Expression.Call(GetGenericMethod("Any", 2, fieldPropertyGenericType), propertyExp, anyLambda);

                return Expression.Lambda<Func<T, bool>>(anyExpression, parameterExpression);
            }
            else
            {
                if (Nullable.GetUnderlyingType(fieldPropertyType) != null)
                {
                    Type fieldPropertyGenericType = fieldPropertyType.GetGenericArguments().First();
                    object defaultValue = Activator.CreateInstance(fieldPropertyGenericType) ?? throw new ArgumentException($"BuildQueryLambda: Instance creation of type {fieldPropertyGenericType?.Name} failed.");
                    propertyExp = Expression.Coalesce(propertyExp, Expression.Constant(defaultValue));
                }

                if (fieldPropertyType == typeof(int) || fieldPropertyType == typeof(int?))
                {
                    int parsedInt;
                    if (!int.TryParse(searchValue, out parsedInt))
                    {
                        parsedInt = int.MinValue;
                    }

                    value = Expression.Constant(parsedInt, typeof(int));
                    typeOfSearchExpression = Expression.Equal(value, propertyExp);
                }
                else if (fieldPropertyType == typeof(bool) || fieldPropertyType == typeof(bool?))
                {
                    bool currentValue = searchValue == "1" ? true : false;
                    value = Expression.Constant(currentValue, typeof(bool));

                    typeOfSearchExpression = Expression.Equal(value, propertyExp);
                }
                else if (fieldPropertyType == typeof(DateTime) || fieldPropertyType == typeof(DateTime?))
                {
                    ConstantExpression dateFormat = Expression.Constant("dd.MM.yyyy", typeof(string));
                    MethodCallExpression toStringExpression = Expression.Call(propertyExp, typeof(DateTime).GetMethod("ToString", new[] { typeof(string) }) ?? throw new ArgumentException("BuildQueryLambda: ToString method is missing."), dateFormat);
                    typeOfSearchExpression = Expression.Call(toStringExpression, typeof(string).GetMethod(typeOfSearch.ToString(), new[] { typeof(string) }) ?? throw new ArgumentException($"BuildQueryLambda: {typeOfSearch} method is missing"), value);
                }
                else if (fieldPropertyType.IsEnum)
                {
                    value = Expression.Constant(Enum.Parse(fieldPropertyType, searchValue));
                    typeOfSearchExpression = Expression.Equal(value, propertyExp);
                }
                else
                {
                    propertyExp = Expression.Coalesce(propertyExp, Expression.Constant(string.Empty));
                    toLowerExpression = Expression.Call(propertyExp, typeof(string).GetMethod("ToLower", Type.EmptyTypes) ?? throw new ArgumentException("BuildQueryLambda: ToLower method is missing"));
                    typeOfSearchExpression = Expression.Call(toLowerExpression, typeof(string).GetMethod(typeOfSearch.ToString(), new[] { typeof(string) }) ?? throw new ArgumentException($"BuildQueryLambda: {typeOfSearch} method is missing"), value);
                }

                if (conditionalExp != null)
                {
                    typeOfSearchExpression = Expression.Condition(conditionalExp, typeOfSearchExpression, Expression.Default(typeof(bool)));
                }

                return Expression.Lambda<Func<T, bool>>(typeOfSearchExpression, parameterExpression);
            }
        }

        private static Expression BuildQueryConditionExpression(string[] properties, Expression currentPropertyExp, Type currentPropertyType, out Expression propertyExp, out Type fieldPropertyType)
        {
            Expression? conditionalExp = BuildConditionExpression(properties, ref currentPropertyExp, ref currentPropertyType);
            if (conditionalExp == null)
            {
                throw new ArgumentException("BuildQueryConditionExpression: conditionalExp is null");
            }

            propertyExp = currentPropertyExp;
            fieldPropertyType = currentPropertyType;

            return conditionalExp;
        }

        private static void BuildPropertyExpressionAndGetPropertyType(string property, ref Type fieldPropertyType, ref Expression propertyExp)
        {
            PropertyInfo propertyInfo = fieldPropertyType.GetProperty(property) ?? throw new ArgumentException($"BuildPropertyExpressionAndGetPropertyType: {property} property is missing for type {fieldPropertyType?.Name}");
            propertyExp = Expression.Property(propertyExp, propertyInfo);
            fieldPropertyType = propertyInfo.PropertyType;
        }

        private static MethodInfo GetGenericMethod(string name, int parameters, params Type[] typeGenericArguments)
        {
            return typeof(Enumerable).GetMethods()
                .Where(m => m.Name == name)
                .Single(m => m.GetParameters().Length == parameters)
                .MakeGenericMethod(typeGenericArguments);
        }

        private static Expression? BuildConditionExpression(string[] properties, ref Expression propertyExp, ref Type currentPropertyType)
        {
            Expression? conditionalExp = null;
            for (int i = 0; i < properties.Length; i++)
            {
                BuildPropertyExpressionAndGetPropertyType(properties[i], ref currentPropertyType, ref propertyExp);
                if (i != properties.Length - 1)
                {
                    Expression currentConditionalExp = Expression.NotEqual(propertyExp, Expression.Constant(null, currentPropertyType));
                    if (i != 0 && conditionalExp != null)
                    {
                        conditionalExp = Expression.AndAlso(conditionalExp, currentConditionalExp);
                    }
                    else
                    {
                        conditionalExp = currentConditionalExp;
                    }
                }
            }

            return conditionalExp;
        }
    }
}
