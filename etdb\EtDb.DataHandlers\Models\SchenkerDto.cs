﻿namespace EtDb.DataHandlers.Models
{
    public class SchenkerDto
    {
        public int Id { get; set; }

        public string OPCode { get; set; } = null!;

        public int TransportArea { get; set; }

        public int ProcessingTime { get; set; }

        public int ProtectiveTime { get; set; }

        public int CityId { get; set; }

        public string? Address { get; set; }

        public string RegionName { get; set; } = null!;

        public string IPTUName { get; set; } = null!;

        public int? LocalWarehouseId { get; set; }

        public string LocalWarehouseName { get; set; } = null!;
    }
}
