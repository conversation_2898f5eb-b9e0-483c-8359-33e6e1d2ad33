﻿namespace EtDb.DataHandlers
{
    using EtDb.ApiClients.EtDb.Context;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using EtDb.Infrastructure.Enumerations;
    using Microsoft.EntityFrameworkCore;
    using System.Collections.Generic;
    using System.Linq;
    using System.Transactions;

    public class ProductDataHandler : BaseDataHandler, IProductDataHandler
    {
        private readonly Lazy<IEquipmentTypeDataHandler> equipmentTypeDataHandler;

        public ProductDataHandler(Lazy<EtDbContext> dbContext,
            Lazy<IEquipmentTypeDataHandler> equipmentTypeDataHandler)
            : base(dbContext)
        {
            this.equipmentTypeDataHandler = equipmentTypeDataHandler;
        }

        public async Task<FilteredDataModel<AdministrateProductDto>> GetFilteredProducts(string sortBy, string sortDir, int pageNumber, int pageSize, string query)
        {
            IQueryable<AdministrateProductDto> products = await this.GetAllProducts();
            FilteredDataModel<AdministrateProductDto> filteredGridData = await this.GetFilteredData(products, sortBy, sortDir, pageNumber, pageSize, query);

            return filteredGridData;
        }

        public async Task<AdministrateProductDto> GetProductById(int id)
        {
            IQueryable<EquipmentType> equipmentTypes = this.dbContext.Value.EquipmentTypes.Where(e => e.Id == id);
            var product = await this.GetProductsByType(equipmentTypes).Result.FirstOrDefaultAsync();

            if (product == null)
            {
                throw new ArgumentException($"No product with id: {id} was found.");
            }

            return product;
        }

        public async Task<SapmatMapp> GetProductBySapMaterialNum(string sapMaterialNum)
        {
            SapmatMapp? sapmatMapp = await this.dbContext.Value.SapmatMapp.FirstOrDefaultAsync(smm => smm.SapmaterialNum == sapMaterialNum);

            if (sapmatMapp == null)
            {
                throw new ArgumentException($"No product with SAP material number: {sapMaterialNum} was found.");
            }

            return sapmatMapp;
        }

        public async Task<bool> CheckForSameProduct(string sapMaterialNum, int equipmentTypeId = 0, int sapMatMappId = 0)
        {
            bool isSapMaterialNumExist = this.dbContext.Value.EquipmentTypes.Any(p => p.SapmaterialNum == sapMaterialNum && p.Id != equipmentTypeId);
            if (!isSapMaterialNumExist)
            {
                isSapMaterialNumExist = this.dbContext.Value.SapmatMapp.Any(p => p.SapmaterialNum == sapMaterialNum && p.Id != sapMatMappId);
            }

            return await Task.FromResult(isSapMaterialNumExist);
        }

        public async Task<IQueryable<AdministrateProductDto>> GetAllProducts()
        {
            IQueryable<AdministrateProductDto> products =
                dbContext.Value.EquipmentTypes.Join(
                    dbContext.Value.SapmatMapp, eqt => eqt.SapmaterialNum, smm => smm.SapmaterialNum, AdministrateProductDto.FromProducts);

            return await Task.FromResult(products);
        }

        public async Task<IQueryable<AdministrateProductDto>> GetProductsByType(IQueryable<EquipmentType> equipmentTypes)
        {
            IQueryable<AdministrateProductDto> products =
                equipmentTypes.Join(
                    dbContext.Value.SapmatMapp, eqt => eqt.SapmaterialNum, smm => smm.SapmaterialNum, AdministrateProductDto.FromProducts);

            return await Task.FromResult(products);
        }

        public async Task<int> InsertProduct(AdministrateProductDto productToInsert)
        {
            string sapMaterialNum = productToInsert.SAPMaterialNum ?? throw new ArgumentException("SAP material number is null!");
            bool isExistSameProduct = await this.CheckForSameProduct(sapMaterialNum);

            if (isExistSameProduct)
            {
                throw new ArgumentException($"There is another product with serial number: {sapMaterialNum}.");
            }

            EquipmentType equipmentTypeEntry = new EquipmentType
            {
                SapmaterialNum = sapMaterialNum,
                Name = productToInsert.Name,
                SendMethod = productToInsert.SapRequestType,
                SerialNumberRequired = productToInsert.SapRequestType == (int)SendMethod.Transfer ? 1 : 0,
                SapsupplyCode = productToInsert.SapElementCode,
                MinimumQuantity = productToInsert.MinimumQuantity,
                EquipmentGroupId = productToInsert.EquipmentGroupId,
                UnitOfMeasure = productToInsert.UnitOfMeasure,
                IsTransferFromSapAllowed = productToInsert.SapRequestType == (int)SendMethod.Transfer,
                BrprojectCode = productToInsert.BRProjectName
            };

            SapmatMapp sapMatMapp = new SapmatMapp
            {
                SapmaterialNum = sapMaterialNum,
                MatFirst = productToInsert.MatFirst,
                EquipmentNameFirst = productToInsert.EquipmentNameFirst
            };

            IEnumerable<QuantityCalculationObject> quantityCalculationObjects = await this.CreateQuantityCalculationObjects(equipmentTypeEntry);

            using (TransactionScope scope = new TransactionScope(
            TransactionScopeOption.Required,
            new TransactionOptions { IsolationLevel = IsolationLevel.ReadUncommitted }))
            {
                await this.dbContext.Value.EquipmentTypes.AddAsync(equipmentTypeEntry);
                await this.dbContext.Value.SapmatMapp.AddAsync(sapMatMapp);
                await this.dbContext.Value.QuantityCalculationObjects.AddRangeAsync(quantityCalculationObjects);

                await this.dbContext.Value.SaveChangesAsync();
                scope.Complete();
            }

            return equipmentTypeEntry.Id;
        }

        public async Task UpdateProduct(AdministrateProductDto productToUpdate)
        {
            var equipmentType = await this.equipmentTypeDataHandler.Value.GetEquipmentTypeByIdAsync(productToUpdate.Id);
            string sapMaterialNum = productToUpdate.SAPMaterialNum ?? throw new ArgumentException("SAP material number is null!");
            var sapMatMapp = await this.GetProductBySapMaterialNum(sapMaterialNum);

            bool isSameProductExist = await this.CheckForSameProduct(sapMaterialNum, productToUpdate.Id, sapMatMapp.Id);

            if (isSameProductExist)
            {
                throw new ArgumentException($"There is another product with serial number: {sapMaterialNum}.");
            }

            equipmentType.Name = productToUpdate.Name;
            equipmentType.SapmaterialNum = productToUpdate.SAPMaterialNum;
            equipmentType.MinimumQuantity = productToUpdate.MinimumQuantity;
            equipmentType.SendMethod = productToUpdate.SapRequestType;
            equipmentType.EquipmentGroupId = productToUpdate.EquipmentGroupId;
            equipmentType.UnitOfMeasure = productToUpdate.UnitOfMeasure;
            equipmentType.IsTransferFromSapAllowed = productToUpdate.SapRequestType == (int)SendMethod.Transfer;
            equipmentType.BoxCapacity = productToUpdate.BoxCapacity;
            equipmentType.BrprojectCode = productToUpdate.BRProjectName;

            SendMethod sapRequestType = (SendMethod)productToUpdate.SapRequestType;

            equipmentType.SerialNumberRequired = sapRequestType == SendMethod.Transfer ? 1 : 0;
            equipmentType.SapsupplyCode = productToUpdate.SapElementCode;

            sapMatMapp.SapmaterialNum = productToUpdate.SAPMaterialNum;
            sapMatMapp.MatFirst = productToUpdate.MatFirst;
            sapMatMapp.EquipmentNameFirst = productToUpdate.EquipmentNameFirst;

            using (TransactionScope scope = new TransactionScope(
            TransactionScopeOption.Required,
            new TransactionOptions { IsolationLevel = IsolationLevel.ReadUncommitted }))
            {
                this.dbContext.Value.EquipmentTypes.Update(equipmentType);
                this.dbContext.Value.SapmatMapp.Update(sapMatMapp);

                await this.dbContext.Value.SaveChangesAsync();
                scope.Complete();
            }
        }

        private async Task<IEnumerable<QuantityCalculationObject>> CreateQuantityCalculationObjects(EquipmentType equipmentTypeEntry)
        {
            List<QuantityCalculationObject> quantityCalculationObjects = new List<QuantityCalculationObject>();

            if (equipmentTypeEntry.SerialNumberRequired <= 0)
            {
                return quantityCalculationObjects;
            }

            var equipmentGroup = await this.dbContext.Value.EquipmentGroups.FirstOrDefaultAsync(et => et.Id == equipmentTypeEntry.EquipmentGroupId);
            if (equipmentGroup == null)
            {
                throw new ArgumentException($"No Equipment Group with id: {equipmentTypeEntry.EquipmentGroupId} was found.");
            }

            if (!equipmentGroup.ContainsSubstitutes)
            {
                IQueryable<int> opIds = this.dbContext.Value.Schenkers.Select(s => s.Id);
                foreach (int opId in opIds)
                {
                    quantityCalculationObjects.Add(new QuantityCalculationObject
                    {
                        Name = equipmentTypeEntry.Name,
                        CriticalQuantity = 0,
                        DeliveryTimeInDays = 7,
                        IsManuallySet = false,
                        LastUpdateDate = DateTime.Now,
                        Opid = opId,
                        EquipmentTypeId = equipmentTypeEntry.Id
                    });
                }
            }

            return quantityCalculationObjects;
        }
    }
}
