# SearchProductDataResponseModelSearchResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**count** | **number** |  | [optional] [default to undefined]
**dataCollection** | [**Array&lt;SearchProductDataResponseModel&gt;**](SearchProductDataResponseModel.md) |  | [optional] [default to undefined]

## Example

```typescript
import { SearchProductDataResponseModelSearchResponseModel } from './api';

const instance: SearchProductDataResponseModelSearchResponseModel = {
    count,
    dataCollection,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
