[{"ContainingType": "EtWS.Api.Controllers.AccountController", "Method": "DeleteImpersonationCookie", "RelativePath": "api/account/delete-impersonation-cookie", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.AccountController", "Method": "AuthenticateAsync", "RelativePath": "api/account/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.UserLoginRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.UserModels.UserLoginResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.AccountController", "Method": "LogoutAsync", "RelativePath": "api/account/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.AccountController", "Method": "SetImpersonationCookie", "RelativePath": "api/account/set-impersonation-cookie", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.CitiesController", "Method": "GetAsync", "RelativePath": "api/cities/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.CityModels.CityResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.CitiesController", "Method": "ActivateCitiesAsync", "RelativePath": "api/cities/activate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "cityIds", "Type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.CitiesController", "Method": "AddCityAsync", "RelativePath": "api/cities/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.CityRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.CitiesController", "Method": "BlockCitiesAsync", "RelativePath": "api/cities/block", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "cityIds", "Type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.CitiesController", "Method": "GetCityRegionByIdAsync", "RelativePath": "api/cities/region/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.String", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.CitiesController", "Method": "GetSapCityCodes", "RelativePath": "api/cities/sap-city-code-and-cluster/{cityId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "cityId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.CityModels.SapCityCodesResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.CitiesController", "Method": "GetCitiesAsync", "RelativePath": "api/cities/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.SearchDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.CommonModels.SearchResponseModel`1[[EtWS.Models.Responses.CityModels.CityResponseModel, EtWS.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.CitiesController", "Method": "GetCitiesSelectListAsync", "RelativePath": "api/cities/select-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[EtWS.Models.Responses.CityModels.CitiesSelectItemResponseModel, EtWS.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.CitiesController", "Method": "UpdateCityAsync", "RelativePath": "api/cities/update/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "EtWS.Models.Requests.CityRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentGroupController", "Method": "GetEquipmentGroups", "RelativePath": "api/equipment-group/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[EtWS.ApiClients.ETDB.EquipmentGroupResponseModel, EtWS.ApiClients, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentGroupController", "Method": "GetEquipmentGroupsNames", "RelativePath": "api/equipment-group/all/names", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentGroupController", "Method": "GetEquipmentGroupByIdAsync", "RelativePath": "api/equipment-group/by-id/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.ApiClients.ETDB.EquipmentGroupResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentGroupController", "Method": "GetEquipmentGroupNameAsync", "RelativePath": "api/equipment-group/name/{sapMaterialNum}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sapMaterialNum", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentTypeController", "Method": "GetEquipmentTypes", "RelativePath": "api/equipment-type/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[EtWS.ApiClients.ETDB.EquipmentTypeResponseModel, EtWS.ApiClients, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentTypeController", "Method": "GetEquipmentTypeByIdAsync", "RelativePath": "api/equipment-type/by-id/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.ApiClients.ETDB.EquipmentTypeResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentTypeController", "Method": "GetEquipmentTypesAsync", "RelativePath": "api/equipment-type/by-send-method/{sendMethod}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "send<PERSON>ethod", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentTypeController", "Method": "GetConciseEquipmentTypes", "RelativePath": "api/equipment-type/concise-equipment-types", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.ConciseEquipmentTypesDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[EtWS.ApiClients.ETDB.EquipmentTypeConciseDto, EtWS.ApiClients, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentTypeController", "Method": "GetEquipmentTypesLastMonthQuantitiesAsync", "RelativePath": "api/equipment-type/last-month-quantities", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IDictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentTypeController", "Method": "GetEquipmentTypeIdAsync", "RelativePath": "api/equipment-type/type-id-by-sap-material-num/{sapMaterialNum}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sapMaterialNum", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "AcceptItemsAsync", "RelativePath": "api/equipment/accept-items", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.AcceptItemsForTransferRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "CancelItemsAsync", "RelativePath": "api/equipment/cancel-items", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.CancelItemsForTransferRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "DeliverItemsAsync", "RelativePath": "api/equipment/deliver-items", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.DeliverItemsDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "GetAllActivePostOfficesAsync", "RelativePath": "api/equipment/post-offices-select-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[EtWS.ApiClients.ETDB.PostOfficesSelectListResponseModel, EtWS.ApiClients, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "RefuseItemsAsync", "RelativePath": "api/equipment/refuse-items", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.RefuseItemsForTransferRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "RemoveAllItemsFromTransferAsync", "RelativePath": "api/equipment/remove-all-items", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "RemoveItemFromTransferAsync", "RelativePath": "api/equipment/remove-item", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.EquipmentModels.RemoveItemFromTransferResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "RemoveItemsFromTransferAsync", "RelativePath": "api/equipment/remove-items", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "selectedItems", "Type": "System.Collections.Generic.IEnumerable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "ReserveAllItemsForTransferAsync", "RelativePath": "api/equipment/reserve-all-items", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "ReserveItemForTransferAsync", "RelativePath": "api/equipment/reserve-item", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.ReserveItemForTransferDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.ApiClients.ETDB.ReserveItemHistoryResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "CountAllUserReservedItemsForTransfer", "RelativePath": "api/equipment/reserved-items-count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "SearchAvailableEquipmentAsync", "RelativePath": "api/equipment/search-available-equipment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.SearchDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.CommonModels.SearchResponseModel`1[[EtWS.Models.Responses.EquipmentModels.AvailableEquipmentDataResponseModel, EtWS.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "SearchTransferDataAsync", "RelativePath": "api/equipment/search-transfer-data", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.SearchDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.CommonModels.SearchResponseModel`1[[EtWS.Models.Responses.EquipmentModels.ItemDataResponseModel, EtWS.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "SearchUserItemsToAcceptDataAsync", "RelativePath": "api/equipment/search-user-items-to-accept", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.SearchDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.CommonModels.SearchResponseModel`1[[EtWS.ApiClients.ETDB.UserItemsToAcceptResponseModel, EtWS.ApiClients, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.EquipmentController", "Method": "SearchUserItemsToCancelDataAsync", "RelativePath": "api/equipment/search-user-items-to-cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.SearchDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.CommonModels.SearchResponseModel`1[[EtWS.ApiClients.ETDB.UserItemsToCancelResponseModel, EtWS.ApiClients, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.IncorrectEquipmentController", "Method": "DoesSerialNumberExistAsync", "RelativePath": "api/incorrect-equipment/check-serial-number/{equipmentSerialNum}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentSerialNum", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.IncorrectEquipmentController", "Method": "CheckServiceId", "RelativePath": "api/incorrect-equipment/check-service-id/{serviceId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "serviceId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.IncorrectEquipmentController", "Method": "GetHistoriesByEquipmentSerialNumber", "RelativePath": "api/incorrect-equipment/get-histories/{equipmentSerialnumber}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipmentSerialnumber", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[EtWS.Models.Responses.HistoriyModels.HistoriesResponse, EtWS.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.IncorrectEquipmentController", "Method": "GetUserDisplayName", "RelativePath": "api/incorrect-equipment/get-user-display-names/{namePrefix}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "namePrefix", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.ICollection`1[[EtWS.Models.Responses.UserModels.UserDisplayNameResponseModel, EtWS.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.IncorrectEquipmentController", "Method": "TransferCorrectionAsync", "RelativePath": "api/incorrect-equipment/transfer-correction", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.TransferCorrectionRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.IncorrectEquipmentController", "Method": "TransferDeleteByDateAsync", "RelativePath": "api/incorrect-equipment/transfer-delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.DeleteTransferRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.IncorrectEquipmentController", "Method": "GetUsersWithOPCodesAsync", "RelativePath": "api/incorrect-equipment/users-with-opcodes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[EtWS.Models.Responses.UserModels.UsersWithOPCodeResponse, EtWS.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.ProductsController", "Method": "GetAsync", "RelativePath": "api/products/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.UserModels.SearchProductDataResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.ProductsController", "Method": "AddProductAsync", "RelativePath": "api/products/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.ProductDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.ProductsController", "Method": "GetProductsAsync", "RelativePath": "api/products/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.SearchDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.CommonModels.SearchResponseModel`1[[EtWS.Models.Responses.UserModels.SearchProductDataResponseModel, EtWS.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.ProductsController", "Method": "UpdateProductAsync", "RelativePath": "api/products/update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.ProductDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.QuantityCalculationObjectController", "Method": "GetByIdAsync", "RelativePath": "api/quantity-calculation-object/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.ApiClients.ETDB.QuantityCalculationObjectResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.QuantityCalculationObjectController", "Method": "SearchQuantityCalculationObjectsAsync", "RelativePath": "api/quantity-calculation-object/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.SearchDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.ApiClients.ETDB.QuantityCalculationObjectResponseModelSearchResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.QuantityCalculationObjectController", "Method": "UpdateQuantityCalculationObjectAsync", "RelativePath": "api/quantity-calculation-object/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.ApiClients.ETDB.QuantityCalculationObjectRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.SchenkersController", "Method": "GetAsync", "RelativePath": "api/schenkers/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.SchenkerModels.SchenkerResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.SchenkersController", "Method": "AddSchenkerAsync", "RelativePath": "api/schenkers/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.SchenkerRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.SchenkersController", "Method": "GetSchenkersAsync", "RelativePath": "api/schenkers/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.SearchDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.CommonModels.SearchResponseModel`1[[EtWS.Models.Responses.SchenkerModels.SchenkerResponseModel, EtWS.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.SchenkersController", "Method": "GetSchenkersSelectListAsync", "RelativePath": "api/schenkers/select-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[EtWS.Models.Responses.SchenkerModels.SchenkerSelectItemResponseModel, EtWS.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.SchenkersController", "Method": "UpdateSchenkerAsync", "RelativePath": "api/schenkers/update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.SchenkerRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.SchenkersController", "Method": "GetSchenkerUsersAsync", "RelativePath": "api/schenkers/users/{schenkerId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schenkerId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[EtWS.Models.Responses.UserModels.UserDataConciseResponseModel, EtWS.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.SubstitutionsController", "Method": "DeleteSubstitutionAsync", "RelativePath": "api/substitutions", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.SubstitutionsController", "Method": "GetAsync", "RelativePath": "api/substitutions/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.ApiClients.ETDB.SubstitutionsResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "EtWS.Api.Controllers.SubstitutionsController", "Method": "AddSubstitutionAsync", "RelativePath": "api/substitutions/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.SubstitutionDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.SubstitutionsController", "Method": "GetSubstitutionsAsync", "RelativePath": "api/substitutions/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.SearchDataRequestModel", "IsRequired": true}, {"Name": "isCurrentUserMol", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.CommonModels.SearchResponseModel`1[[EtWS.ApiClients.ETDB.SubstitutionsResponseModel, EtWS.ApiClients, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.SubstitutionsController", "Method": "UpdateSubstitutionAsync", "RelativePath": "api/substitutions/update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.SubstitutionDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.UsersController", "Method": "EditUserAsync", "RelativePath": "api/users", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.EditUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.UsersController", "Method": "GetUserByUserIdAsync", "RelativePath": "api/users/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.UserModels.SearchUserDataResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.UsersController", "Method": "ActivateUsersAsync", "RelativePath": "api/users/activate-users", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "selectedUsersRequest", "Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.UsersController", "Method": "GetUserADAccountAsync", "RelativePath": "api/users/ad-account/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.String", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.UsersController", "Method": "GetAllMolsAsync", "RelativePath": "api/users/all-mols", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.ICollection`1[[EtWS.Models.Responses.UserModels.UserDataConciseResponseModel, EtWS.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.UsersController", "Method": "BlockUsersAsync", "RelativePath": "api/users/block-users", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "selectedUsersRequest", "Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.UsersController", "Method": "IsUserMolAsync", "RelativePath": "api/users/is-user-mol/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Boolean", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.UsersController", "Method": "GetUsersAsync", "RelativePath": "api/users/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EtWS.Models.Requests.SearchDataRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "EtWS.ApiClients.ETDB.BaseResponseModel", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.CommonModels.SearchResponseModel`1[[EtWS.Models.Responses.UserModels.SearchUserDataResponseModel, EtWS.Models, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.UsersController", "Method": "GetUserByUsernameAsync", "RelativePath": "api/users/user-by-username/{username}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.UserModels.SearchUserDataResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.UsersController", "Method": "GetUsersSelectListAsync", "RelativePath": "api/users/users-select-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "EtWS.Models.Responses.UserModels.UsersListResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.WarehousesController", "Method": "GetWarehousesSelectListAsync", "RelativePath": "api/warehouses/by-region", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "region", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[EtWS.ApiClients.ETDB.WarehousesResponseModel, EtWS.ApiClients, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "EtWS.Api.Controllers.WarehousesController", "Method": "GetWarehousesSelectListAsync", "RelativePath": "api/warehouses/select-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[EtWS.ApiClients.ETDB.WarehousesResponseModel, EtWS.ApiClients, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}]