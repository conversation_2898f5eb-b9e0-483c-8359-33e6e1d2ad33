# TransferCorrectionRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**equipmentSerialNum** | **string** |  | [default to undefined]
**transferTo** | **string** |  | [default to undefined]
**serviceId** | **string** |  | [optional] [default to undefined]
**nameOfTechnician** | **string** |  | [optional] [default to undefined]
**userId** | **string** |  | [optional] [default to undefined]
**deliveryShop** | **string** |  | [optional] [default to undefined]
**insertDate** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { TransferCorrectionRequest } from './api';

const instance: TransferCorrectionRequest = {
    equipmentSerialNum,
    transferTo,
    serviceId,
    nameOfTechnician,
    userId,
    deliveryShop,
    insertDate,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
