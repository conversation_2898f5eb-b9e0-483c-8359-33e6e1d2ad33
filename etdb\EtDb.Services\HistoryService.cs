﻿namespace EtDb.Services
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using EtDb.Services.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class HistoryService : IHistoryService
    {
        private readonly Lazy<IHistoryDataHandler> historyDataHandler;

        public HistoryService(Lazy<IHistoryDataHandler> historyDataHandler)
        {
            this.historyDataHandler = historyDataHandler;
        }

        public async Task UpdateHistoryTransfersToBlockedUsersAsync(IEnumerable<string> userIds)
        {
            await this.historyDataHandler.Value.UpdateHistoryTransfersToBlockedUsersAsync(userIds);
        }

        public async Task<IDictionary<string, TransferedItemsModel>> CancelHistoryExpiredTransfersAsync(double cancellationDays)
        {
            var result = await this.historyDataHandler.Value.CancelHistoryExpiredTransfersAsync(cancellationDays);

            return result.Value;
        }

        public async Task<IEnumerable<History>> GetReservedItemsByFromUserIdAsync(string userId)
        {
            return await this.historyDataHandler.Value
                .GetHistoryResrvedItemsByFromUserId(userId)
                .ToListAsync();
        }
    }
}
