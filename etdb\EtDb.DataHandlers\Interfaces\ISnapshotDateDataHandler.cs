﻿using EtDb.ApiClients.EtDb.Models;

namespace EtDb.DataHandlers.Interfaces
{
    public interface ISnapshotDateDataHandler
    {
        IQueryable<SnapshotDates> GetAll();
        Task<SnapshotDates> AddAsync(SnapshotDates snapshotDate);
        Task<SnapshotDates?> FindAvailableSnapshotForDateAsync(DateTime date);
        Task<SnapshotDates?> FindLastAvailableSnapshotForDateAsync(DateTime date);
        Task<List<SnapshotDates>> FindAllSnapshotsWithDataBetweenDatesAsync(DateTime fromDate, DateTime toDate, bool includeLastAvailableBeforeFromDate = true);
    }
}
