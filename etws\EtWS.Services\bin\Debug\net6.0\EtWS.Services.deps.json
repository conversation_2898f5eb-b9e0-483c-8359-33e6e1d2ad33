{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"EtWS.Services/1.0.0": {"dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": "11.0.0", "EtWS.ApiClients": "1.0.0", "EtWS.Infrastructure": "1.0.0", "EtWS.Models": "1.0.0", "Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Net.Ams": "0.2.20", "Net.Common.Mvc": "0.2.20", "StyleCop.Analyzers": "1.1.118"}, "runtime": {"EtWS.Services.dll": {}}}, "App.Metrics/4.3.0": {"dependencies": {"App.Metrics.Core": "4.3.0", "App.Metrics.Formatters.Json": "4.3.0"}, "runtime": {"lib/netstandard2.0/App.Metrics.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.Abstractions/4.3.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0"}, "runtime": {"lib/netstandard2.0/App.Metrics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.AspNetCore/4.3.0": {"dependencies": {"App.Metrics": "4.3.0", "App.Metrics.AspNetCore.Endpoints": "4.3.0", "App.Metrics.AspNetCore.Tracking": "4.3.0", "App.Metrics.Extensions.Hosting": "4.3.0"}, "runtime": {"lib/netcoreapp3.1/App.Metrics.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.AspNetCore.Abstractions/4.3.0": {"dependencies": {"App.Metrics.Abstractions": "4.3.0"}, "runtime": {"lib/netcoreapp3.1/App.Metrics.AspNetCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.AspNetCore.All/4.3.0": {"dependencies": {"App.Metrics.AspNetCore": "4.3.0", "App.Metrics.AspNetCore.Endpoints": "4.3.0", "App.Metrics.AspNetCore.Hosting": "4.3.0", "App.Metrics.AspNetCore.Mvc": "4.3.0", "App.Metrics.AspNetCore.Routing": "4.3.0", "App.Metrics.AspNetCore.Tracking": "4.3.0", "App.Metrics.Extensions.Collectors": "4.3.0", "App.Metrics.Extensions.Configuration": "4.3.0", "App.Metrics.Extensions.DependencyInjection": "4.3.0", "App.Metrics.Extensions.HealthChecks": "4.3.0", "App.Metrics.Extensions.Hosting": "4.3.0", "App.Metrics.Formatters.Json": "4.3.0"}, "runtime": {"lib/netcoreapp3.1/App.Metrics.AspNetCore.All.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.AspNetCore.Core/4.3.0": {"dependencies": {"App.Metrics.AspNetCore.Abstractions": "4.3.0", "App.Metrics.Core": "4.3.0", "App.Metrics.Extensions.Configuration": "4.3.0", "App.Metrics.Extensions.DependencyInjection": "4.3.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "3.1.1"}, "runtime": {"lib/netcoreapp3.1/App.Metrics.AspNetCore.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.AspNetCore.Endpoints/4.3.0": {"dependencies": {"App.Metrics.AspNetCore.Hosting": "4.3.0"}, "runtime": {"lib/netcoreapp3.1/App.Metrics.AspNetCore.Endpoints.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.AspNetCore.Hosting/4.3.0": {"dependencies": {"App.Metrics.AspNetCore.Core": "4.3.0"}, "runtime": {"lib/netcoreapp3.1/App.Metrics.AspNetCore.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.AspNetCore.Mvc/4.3.0": {"dependencies": {"App.Metrics.AspNetCore": "4.3.0", "App.Metrics.AspNetCore.Mvc.Core": "4.3.0", "App.Metrics.AspNetCore.Routing": "4.3.0"}, "runtime": {"lib/netcoreapp3.1/App.Metrics.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.AspNetCore.Mvc.Core/4.3.0": {"dependencies": {"App.Metrics.AspNetCore": "4.3.0", "App.Metrics.AspNetCore.Routing": "4.3.0"}, "runtime": {"lib/netcoreapp3.1/App.Metrics.AspNetCore.Mvc.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.AspNetCore.Routing/4.3.0": {"dependencies": {"App.Metrics.AspNetCore.Abstractions": "4.3.0"}, "runtime": {"lib/netcoreapp3.1/App.Metrics.AspNetCore.Routing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.AspNetCore.Tracking/4.3.0": {"dependencies": {"App.Metrics.AspNetCore.Hosting": "4.3.0"}, "runtime": {"lib/netcoreapp3.1/App.Metrics.AspNetCore.Tracking.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.Concurrency/4.3.0": {"runtime": {"lib/netstandard2.0/App.Metrics.Concurrency.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.Core/4.3.0": {"dependencies": {"App.Metrics.Abstractions": "4.3.0", "App.Metrics.Concurrency": "4.3.0", "App.Metrics.Formatters.Ascii": "4.3.0", "Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.0/App.Metrics.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.Extensions.Collectors/4.3.0": {"dependencies": {"App.Metrics": "4.3.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/App.Metrics.Extensions.Collectors.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.Extensions.Configuration/4.3.0": {"dependencies": {"App.Metrics": "4.3.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0"}, "runtime": {"lib/netstandard2.0/App.Metrics.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.Extensions.DependencyInjection/4.3.0": {"dependencies": {"App.Metrics": "4.3.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/App.Metrics.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.Extensions.HealthChecks/4.3.0": {"dependencies": {"App.Metrics.Core": "4.3.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "3.1.18"}, "runtime": {"lib/netstandard2.0/App.Metrics.Extensions.HealthChecks.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.Extensions.Hosting/4.3.0": {"dependencies": {"App.Metrics.Core": "4.3.0"}, "runtime": {"lib/netcoreapp3.1/App.Metrics.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.Formatters.Ascii/4.3.0": {"dependencies": {"App.Metrics.Abstractions": "4.3.0"}, "runtime": {"lib/netstandard2.1/App.Metrics.Formatters.Ascii.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.Formatters.Json/4.3.0": {"dependencies": {"App.Metrics.Abstractions": "4.3.0", "System.Text.Json": "5.0.2"}, "runtime": {"lib/netstandard2.0/App.Metrics.Formatters.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.Formatters.Prometheus/4.3.0": {"dependencies": {"App.Metrics.Core": "4.3.0", "protobuf-net": "2.4.0"}, "runtime": {"lib/netstandard2.1/App.Metrics.Formatters.Prometheus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "App.Metrics.Prometheus/4.3.0": {"dependencies": {"App.Metrics.Formatters.Prometheus": "4.3.0"}, "runtime": {"lib/netstandard2.1/App.Metrics.Prometheus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Autofac/6.3.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "6.0.0"}, "runtime": {"lib/net5.0/Autofac.dll": {"assemblyVersion": "6.3.0.0", "fileVersion": "6.3.0.0"}}}, "AutoMapper/11.0.0": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/11.0.0": {"dependencies": {"AutoMapper": "11.0.0", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Automatonymous/5.1.3": {"dependencies": {"GreenPipes": "4.0.1"}, "runtime": {"lib/netstandard2.0/Automatonymous.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Castle.Core/4.4.1": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Diagnostics.TraceSource": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.5/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Castle.Core.AsyncInterceptor/2.0.0": {"dependencies": {"Castle.Core": "4.4.1"}, "runtime": {"lib/net5.0/Castle.Core.AsyncInterceptor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "GreenPipes/4.0.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/GreenPipes.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Hellang.Middleware.ProblemDetails/6.4.0": {"dependencies": {"System.Reflection.Metadata": "5.0.0"}, "runtime": {"lib/net5.0/Hellang.Middleware.ProblemDetails.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MassTransit/7.3.1": {"dependencies": {"Automatonymous": "5.1.3", "Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "NewId": "3.0.3", "Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Text.Json": "5.0.2", "System.Threading.Channels": "4.7.1", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netstandard2.0/MassTransit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MassTransit.AspNetCore/7.3.1": {"dependencies": {"MassTransit": "7.3.1", "MassTransit.Extensions.DependencyInjection": "7.3.1", "Microsoft.Extensions.Diagnostics.HealthChecks": "3.1.18", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/MassTransit.AspNetCoreIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MassTransit.Extensions.DependencyInjection/7.3.1": {"dependencies": {"MassTransit": "7.3.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/MassTransit.ExtensionsDependencyInjectionIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MassTransit.RabbitMQ/7.3.1": {"dependencies": {"MassTransit": "7.3.1", "RabbitMQ.Client": "6.2.2"}, "runtime": {"lib/netstandard2.0/MassTransit.RabbitMqTransport.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNet.WebApi.Client/5.2.7": {"dependencies": {"Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/netstandard2.0/System.Net.Http.Formatting.dll": {"assemblyVersion": "*******", "fileVersion": "5.2.61128.0"}}}, "Microsoft.AspNetCore.Authentication/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.DataProtection": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.WebEncoders": "2.2.0"}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.AspNetCore.Authentication.Cookies/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.2.0"}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.2.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Win32.Registry": "5.0.0", "System.Security.Cryptography.Xml": "4.5.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.AspNetCore.Http/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.DotNet.PlatformAbstractions/2.0.4": {"dependencies": {"System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Configuration/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.0", "Microsoft.Extensions.FileProviders.Physical": "3.1.0"}}, "Microsoft.Extensions.Configuration.Json/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.0", "Microsoft.Extensions.Configuration.FileExtensions": "3.1.0"}}, "Microsoft.Extensions.Configuration.UserSecrets/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "3.1.0"}}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {}, "Microsoft.Extensions.DependencyModel/2.0.4": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.0.4", "Newtonsoft.Json": "13.0.1", "System.Diagnostics.Debug": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Linq": "4.3.0"}, "runtime": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks/3.1.18": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "3.1.18", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/3.1.18": {}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/3.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileSystemGlobbing": "3.1.0"}}, "Microsoft.Extensions.FileSystemGlobbing/3.1.0": {}, "Microsoft.Extensions.Hosting/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.0", "Microsoft.Extensions.Configuration.CommandLine": "3.1.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "3.1.0", "Microsoft.Extensions.Configuration.UserSecrets": "3.1.0", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "3.1.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Console": "3.1.0", "Microsoft.Extensions.Logging.Debug": "3.1.0", "Microsoft.Extensions.Logging.EventLog": "3.1.0", "Microsoft.Extensions.Logging.EventSource": "3.1.0"}}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Http/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {}, "Microsoft.Extensions.Logging.Configuration/3.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "3.1.1"}}, "Microsoft.Extensions.Logging.Console/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Configuration": "3.1.0"}}, "Microsoft.Extensions.Logging.Debug/3.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "6.0.0"}}, "Microsoft.Extensions.Logging.EventLog/3.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "6.0.0", "System.Diagnostics.EventLog": "4.7.0"}}, "Microsoft.Extensions.Logging.EventSource/3.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "6.0.0"}}, "Microsoft.Extensions.ObjectPool/2.2.0": {}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/3.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.Extensions.WebEncoders/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.IO.RecyclableMemoryStream/1.3.2": {"runtime": {"lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0", "System.Buffers": "4.5.0"}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Net.Ams/0.2.20": {"dependencies": {"Microsoft.AspNet.WebApi.Client": "5.2.7", "Microsoft.AspNetCore.Authentication.Cookies": "2.2.0", "Microsoft.Extensions.Caching.Memory": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Net.Caching": "0.2.20", "Net.Common": "0.2.20"}, "runtime": {"lib/netstandard2.0/Net.Ams.dll": {"assemblyVersion": "*******", "fileVersion": "0.2.20.46335"}}}, "Net.Caching/0.2.20": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "StackExchange.Redis": "2.2.88", "System.Text.Json": "5.0.2"}, "runtime": {"lib/netstandard2.0/Net.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "0.2.20.46335"}}}, "Net.Common/0.2.20": {"dependencies": {"Microsoft.AspNetCore.Http": "2.2.0", "Newtonsoft.Json": "13.0.1", "System.IO.Abstractions": "16.1.7", "System.Text.Json": "5.0.2"}, "runtime": {"lib/netstandard2.0/Net.Common.dll": {"assemblyVersion": "*******", "fileVersion": "0.2.20.46335"}}}, "Net.Common.Mvc/0.2.20": {"dependencies": {"Autofac": "6.3.0", "Castle.Core.AsyncInterceptor": "2.0.0", "Hellang.Middleware.ProblemDetails": "6.4.0", "MassTransit.AspNetCore": "7.3.1", "MassTransit.Extensions.DependencyInjection": "7.3.1", "MassTransit.RabbitMQ": "7.3.1", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.Http": "6.0.0", "Net.Common": "0.2.20", "Net.Logging": "0.2.20", "Net.Metrics": "0.2.20", "OpenTracing": "0.12.1", "Serilog.Extensions.Hosting": "4.2.0", "Serilog.Settings.Configuration": "3.1.0", "Serilog.Sinks.Console": "4.0.1", "Steeltoe.Discovery.Eureka": "3.1.3"}, "runtime": {"lib/net6.0/Net.Common.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "0.2.20.46335"}}}, "Net.Logging/0.2.20": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Http": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "3.1.1", "Microsoft.IO.RecyclableMemoryStream": "1.3.2", "Newtonsoft.Json": "13.0.1", "Serilog": "2.10.0", "Serilog.Extensions.Logging": "3.1.0", "Serilog.Sinks.Async": "1.5.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/netstandard2.0/Net.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "0.2.20.46335"}}}, "Net.Metrics/0.2.20": {"dependencies": {"App.Metrics.AspNetCore.All": "4.3.0", "App.Metrics.AspNetCore.Endpoints": "4.3.0", "App.Metrics.AspNetCore.Mvc": "4.3.0", "App.Metrics.AspNetCore.Tracking": "4.3.0", "App.Metrics.Prometheus": "4.3.0", "Castle.Core": "4.4.1", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Net.Metrics.dll": {"assemblyVersion": "*******", "fileVersion": "0.2.20.46335"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.4", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "NewId/3.0.3": {"runtime": {"lib/netstandard2.0/NewId.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.1.25517"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}, "OpenTracing/0.12.1": {"runtime": {"lib/netstandard2.0/OpenTracing.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Pipelines.Sockets.Unofficial/2.2.0": {"dependencies": {"System.IO.Pipelines": "5.0.0"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.45337"}}}, "protobuf-net/2.4.0": {"dependencies": {"System.ServiceModel.Primitives": "4.5.3"}, "runtime": {"lib/netcoreapp2.1/protobuf-net.dll": {"assemblyVersion": "*******", "fileVersion": "2.4.0.8641"}}}, "RabbitMQ.Client/6.2.2": {"dependencies": {"System.Memory": "4.5.4", "System.Threading.Channels": "4.7.1"}, "runtime": {"lib/netstandard2.0/RabbitMQ.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "Serilog/2.10.0": {"runtime": {"lib/netstandard2.1/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Serilog.Extensions.Hosting/4.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Serilog": "2.10.0", "Serilog.Extensions.Logging": "3.1.0"}, "runtime": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/3.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "6.0.0", "Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/3.1.0": {"dependencies": {"Microsoft.Extensions.DependencyModel": "2.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "3.1.1", "Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Async/1.5.0": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.0/Serilog.Sinks.Async.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/4.0.1": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "StackExchange.Redis/2.2.88": {"dependencies": {"Pipelines.Sockets.Unofficial": "2.2.0", "System.Diagnostics.PerformanceCounter": "5.0.0"}, "runtime": {"lib/net5.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.88.56325"}}}, "Steeltoe.Common/3.1.3": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "3.1.1", "Steeltoe.Common.Abstractions": "3.1.3", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Reflection.MetadataLoadContext": "4.6.0", "System.Text.Json": "5.0.2"}, "runtime": {"lib/netstandard2.0/Steeltoe.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Steeltoe.Common.Abstractions/3.1.3": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "5.0.0"}, "runtime": {"lib/netstandard2.0/Steeltoe.Common.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Steeltoe.Common.Http/3.1.3": {"dependencies": {"Microsoft.Extensions.Http": "6.0.0", "Steeltoe.Common": "3.1.3", "Steeltoe.Discovery.Abstractions": "3.1.3", "System.Text.Json": "5.0.2"}, "runtime": {"lib/netstandard2.0/Steeltoe.Common.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Steeltoe.Connector.Abstractions/3.1.3": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Steeltoe.Common.Abstractions": "3.1.3", "Steeltoe.Extensions.Configuration.Abstractions": "3.1.3"}, "runtime": {"lib/netstandard2.0/Steeltoe.Connector.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Steeltoe.Connector.ConnectorBase/3.1.3": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.Options": "6.0.0", "Steeltoe.Common": "3.1.3", "Steeltoe.Connector.Abstractions": "3.1.3"}, "runtime": {"lib/netstandard2.0/Steeltoe.Connector.ConnectorBase.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Steeltoe.Discovery.Abstractions/3.1.3": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Steeltoe.Common.Abstractions": "3.1.3"}, "runtime": {"lib/netstandard2.0/Steeltoe.Discovery.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Steeltoe.Discovery.ClientBase/3.1.3": {"dependencies": {"Microsoft.Extensions.Hosting": "3.1.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "3.1.1", "Steeltoe.Common.Http": "3.1.3", "Steeltoe.Connector.ConnectorBase": "3.1.3", "Steeltoe.Discovery.Abstractions": "3.1.3"}, "runtime": {"lib/netstandard2.0/Steeltoe.Discovery.ClientBase.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Steeltoe.Discovery.Eureka/3.1.3": {"dependencies": {"Steeltoe.Common.Http": "3.1.3", "Steeltoe.Connector.Abstractions": "3.1.3", "Steeltoe.Discovery.ClientBase": "3.1.3", "System.Net.Http.Json": "3.2.1"}, "runtime": {"lib/netstandard2.0/Steeltoe.Discovery.Eureka.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Steeltoe.Extensions.Configuration.Abstractions/3.1.3": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Steeltoe.Common.Abstractions": "3.1.3"}, "runtime": {"lib/netstandard2.0/Steeltoe.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "StyleCop.Analyzers/1.1.118": {}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.0": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Configuration.ConfigurationManager/5.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "5.0.0", "System.Security.Permissions": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Registry": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Diagnostics.PerformanceCounter/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Registry": "5.0.0", "System.Configuration.ConfigurationManager": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.TraceSource/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Drawing.Common/5.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "5.0.0"}, "runtime": {"lib/netcoreapp3.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}, "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Abstractions/16.1.7": {"runtime": {"lib/net6.0/System.IO.Abstractions.dll": {"assemblyVersion": "1*******", "fileVersion": "16.1.7.43912"}}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Buffers": "4.5.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Pipelines/5.0.0": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.Net.Http/4.3.4": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Http.Json/3.2.1": {"dependencies": {"System.Text.Json": "5.0.2"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.ServiceModel/4.5.3": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Reflection.DispatchProxy": "4.5.0", "System.Security.Principal.Windows": "5.0.0"}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/System.Private.ServiceModel.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}, "runtimes/win/lib/netstandard2.0/System.Private.ServiceModel.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.DispatchProxy/4.5.0": {}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.7.0": {}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/5.0.0": {}, "System.Reflection.MetadataLoadContext/4.6.0": {"runtime": {"lib/netcoreapp3.0/System.Reflection.MetadataLoadContext.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.46214"}}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Pkcs/4.5.0": {"dependencies": {"System.Security.Cryptography.Cng": "4.5.0"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/5.0.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Xml/4.5.0": {"dependencies": {"System.Security.Cryptography.Pkcs": "4.5.0", "System.Security.Permissions": "5.0.0"}}, "System.Security.Permissions/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Windows.Extensions": "5.0.0"}, "runtime": {"lib/net5.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.ServiceModel.Primitives/4.5.3": {"dependencies": {"System.Private.ServiceModel": "4.5.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}, "lib/netstandard2.0/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26720.1"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/4.5.0": {}, "System.Text.Json/5.0.2": {}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels/4.7.1": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.ValueTuple/4.5.0": {}, "System.Windows.Extensions/5.0.0": {"dependencies": {"System.Drawing.Common": "5.0.0"}, "runtime": {"lib/netcoreapp3.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.3.0"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "EtWS.ApiClients/1.0.0": {"dependencies": {"Net.Common": "0.2.20", "Newtonsoft.Json": "13.0.1", "System.ComponentModel.Annotations": "5.0.0", "System.Net.Http": "4.3.4"}, "runtime": {"EtWS.ApiClients.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EtWS.Infrastructure/1.0.0": {"runtime": {"EtWS.Infrastructure.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EtWS.Models/1.0.0": {"dependencies": {"EtWS.ApiClients": "1.0.0", "EtWS.Infrastructure": "1.0.0", "Net.Common": "0.2.20"}, "runtime": {"EtWS.Models.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"EtWS.Services/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "App.Metrics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-D2eDXyfrl+lXigXsQTv/81JCxUPTjgwsazK5neA3NOg87tNmBpFqeVJppI/qLKyC8yklTU2ekZDFX5hKechu6A==", "path": "app.metrics/4.3.0", "hashPath": "app.metrics.4.3.0.nupkg.sha512"}, "App.Metrics.Abstractions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ekSlyVgN6foN6rmwVmRGBr0j5ufgRPsO5f7Md2fc3q44vkBNYpjsRLiUQsIXCSVI3NHorkrZh8aL4eRcLkVDGw==", "path": "app.metrics.abstractions/4.3.0", "hashPath": "app.metrics.abstractions.4.3.0.nupkg.sha512"}, "App.Metrics.AspNetCore/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b9xsSzFRRMTfhZSwPxwA6AgnItIfINXVXJHtnawjWZmELByAVljqk/pt/rqBgmGdi4lm08mYD5Oa+wv//79iiA==", "path": "app.metrics.aspnetcore/4.3.0", "hashPath": "app.metrics.aspnetcore.4.3.0.nupkg.sha512"}, "App.Metrics.AspNetCore.Abstractions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VQRn2A70HXn0KzB0OTzx4C7LjTLa2zARg4G2OkHpdlbqBQaJo7Lt1amKjzUQAdg7zEEOofr9wtzVISpV63UB9A==", "path": "app.metrics.aspnetcore.abstractions/4.3.0", "hashPath": "app.metrics.aspnetcore.abstractions.4.3.0.nupkg.sha512"}, "App.Metrics.AspNetCore.All/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZCc2GSoDdmwxvacu9Rc/2TFtMW33KPWXfRbLF9yemEKalO5CQvDtZbCs9E1dDCEofeeI2Eho0ky86Brm3lXm4g==", "path": "app.metrics.aspnetcore.all/4.3.0", "hashPath": "app.metrics.aspnetcore.all.4.3.0.nupkg.sha512"}, "App.Metrics.AspNetCore.Core/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ddk6q4YeA2P23+07MXo6j4vaJtE+sY81+6jbbLBSboW9CRhO40QKUukYW+OtNfgX+PegQigHWjFLrZGt/X4sWw==", "path": "app.metrics.aspnetcore.core/4.3.0", "hashPath": "app.metrics.aspnetcore.core.4.3.0.nupkg.sha512"}, "App.Metrics.AspNetCore.Endpoints/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ns/66gHqwwujWpSxrgdJH39YcNYfmd23Jon+vb+SE43VOFTBHRxer6zGJQIuFdFhePCFlT7obi5Dz9hde47jIQ==", "path": "app.metrics.aspnetcore.endpoints/4.3.0", "hashPath": "app.metrics.aspnetcore.endpoints.4.3.0.nupkg.sha512"}, "App.Metrics.AspNetCore.Hosting/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BBb4BT6c20pT/in7jzSR0PrKXc1kwGQNLY921BRs5szJcNoNkdPbct7gzYOUed2JWMY7e2GhKNVZT9Ew1fQ9XA==", "path": "app.metrics.aspnetcore.hosting/4.3.0", "hashPath": "app.metrics.aspnetcore.hosting.4.3.0.nupkg.sha512"}, "App.Metrics.AspNetCore.Mvc/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-CvsIUrUFS6sWimxKRl9RChDtOAGY36yW3HSTNXSaUrbFpmF76qL2HKiXu+4vSpO0Xau+fk7TdJvGRiG5RWGj0A==", "path": "app.metrics.aspnetcore.mvc/4.3.0", "hashPath": "app.metrics.aspnetcore.mvc.4.3.0.nupkg.sha512"}, "App.Metrics.AspNetCore.Mvc.Core/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-acAmuq4roemQv19S7xtboDqEA04NAlSsIw9F/mt51fCcjdq338qgdlEFlr3M2OCaorfS8WzMtlBPblY2/VUdWg==", "path": "app.metrics.aspnetcore.mvc.core/4.3.0", "hashPath": "app.metrics.aspnetcore.mvc.core.4.3.0.nupkg.sha512"}, "App.Metrics.AspNetCore.Routing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-s8TMzlkvKM+zBlLYVcpH/Ofk4ftfWBvSDD6T+ehxMiY3k4entz6SVAeJTLrq2PDmO2T5vy7cYI97R0M6Fr6dpA==", "path": "app.metrics.aspnetcore.routing/4.3.0", "hashPath": "app.metrics.aspnetcore.routing.4.3.0.nupkg.sha512"}, "App.Metrics.AspNetCore.Tracking/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NWFmXLKEDXdkdKBN32FZXBd16Qhj6UpzQYSjmUN8XOYb+pjJQxttpTTnO8nWYHQ1xX893jx8vjZTN8vQ40j9AA==", "path": "app.metrics.aspnetcore.tracking/4.3.0", "hashPath": "app.metrics.aspnetcore.tracking.4.3.0.nupkg.sha512"}, "App.Metrics.Concurrency/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-otryWX9AR7wLPD49glbxvbYc16pnDOEezHsAtf5oVjhAa/fD+fjhI11MOgzBOjFpkH7z2FLl/gtZ0lwSdNxSag==", "path": "app.metrics.concurrency/4.3.0", "hashPath": "app.metrics.concurrency.4.3.0.nupkg.sha512"}, "App.Metrics.Core/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HhW4n2fF+WBi6ctCpwsYkKCSeLhG5Y17e31kSkdESNAdPvroI9szlzW3WoY20qsB3bCldrGPPnCN6jXI1t3agA==", "path": "app.metrics.core/4.3.0", "hashPath": "app.metrics.core.4.3.0.nupkg.sha512"}, "App.Metrics.Extensions.Collectors/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-vpWzbLJ2uUnaR6s/bp4F1mZNf5vxMvFA0re+bUbQ8gkop7AEJZ1g3uFdQs7mSeL56josQBGnwbMediVst5zywA==", "path": "app.metrics.extensions.collectors/4.3.0", "hashPath": "app.metrics.extensions.collectors.4.3.0.nupkg.sha512"}, "App.Metrics.Extensions.Configuration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+5eNA58nJEvKNd6eKXXnwjjH8KU0wIN9VnE4015qoU6P/yii0tKARrF5Rbw0OGpI6jJmfZ/UIielU07b9QB8aA==", "path": "app.metrics.extensions.configuration/4.3.0", "hashPath": "app.metrics.extensions.configuration.4.3.0.nupkg.sha512"}, "App.Metrics.Extensions.DependencyInjection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lujWx61MSJPKdX7PiaNPv0aXW6D+UzzqiQe/2EwXv401+bshJyyrltSTVVS2cuyla+iq/ag+W1Vc/xeFR0rrwg==", "path": "app.metrics.extensions.dependencyinjection/4.3.0", "hashPath": "app.metrics.extensions.dependencyinjection.4.3.0.nupkg.sha512"}, "App.Metrics.Extensions.HealthChecks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uLpWgl9flmsDTYuYvIOjjo3tEsn3H951OS3ItS2tqi/wgGGpwAXwRW+HB/meB8W6PBRmISPQCUwNJudRerH5zA==", "path": "app.metrics.extensions.healthchecks/4.3.0", "hashPath": "app.metrics.extensions.healthchecks.4.3.0.nupkg.sha512"}, "App.Metrics.Extensions.Hosting/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uSw1pD6MHoky5NCDHsmGArThHhjIXiILRv+XboZXHGA6M4DbWbPrPMsMr9uCeKKyT2wl63y8cboH8oCkC4s8yg==", "path": "app.metrics.extensions.hosting/4.3.0", "hashPath": "app.metrics.extensions.hosting.4.3.0.nupkg.sha512"}, "App.Metrics.Formatters.Ascii/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PPacBFRji8wTGv8rs13fPmAVlOit7CAvkdPkZ6aYgtUa75e0v4fYzwqPcLxokCqdQXW96PpKPfC0VZZeDkgljg==", "path": "app.metrics.formatters.ascii/4.3.0", "hashPath": "app.metrics.formatters.ascii.4.3.0.nupkg.sha512"}, "App.Metrics.Formatters.Json/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-H+4Q407Xa5nuBagooMeh5UAuGHWfKZRsinpwr9dtyV+LZbhAS5yheAAMPY1Xs/g0zzI3zJQJDRy7iX0totAcYA==", "path": "app.metrics.formatters.json/4.3.0", "hashPath": "app.metrics.formatters.json.4.3.0.nupkg.sha512"}, "App.Metrics.Formatters.Prometheus/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cVJZX5jiMxt+YytjpbMw52reN47LGL3XsCljzNH9Pb+Op9iSTazc4pa+/fX+FdpbhH/Zt+5hjdYiqOLFol0wGg==", "path": "app.metrics.formatters.prometheus/4.3.0", "hashPath": "app.metrics.formatters.prometheus.4.3.0.nupkg.sha512"}, "App.Metrics.Prometheus/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-QhEL8zqnmOuaaSEUfQmWrqBEYt3MI3hb5Qhmlln72wUjyWzFkadA6QgzrQmG7K0lYqsj269BYcg42cL9T7wg6g==", "path": "app.metrics.prometheus/4.3.0", "hashPath": "app.metrics.prometheus.4.3.0.nupkg.sha512"}, "Autofac/6.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-gO4bli0N8tDnBHzbYktcnbXlmN6T+IT5W+FUGgCUaM6pwwHXIxOPoUGvfGum7sZpJJgfQNgjFFv80ZPuARgRdA==", "path": "autofac/6.3.0", "hashPath": "autofac.6.3.0.nupkg.sha512"}, "AutoMapper/11.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+596AnKykYCk9RxXCEF4GYuapSebQtFVvIA1oVG1rrRkCLAC7AkWehJ0brCfYUbdDW3v1H/p0W3hob7JoXGjMw==", "path": "automapper/11.0.0", "hashPath": "automapper.11.0.0.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/11.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0asw5WxdCFh2OTi9Gv+oKyH9SzxwYQSnO8TV5Dd0GggovILzJW4UimP26JAcxc3yB5NnC5urooZ1BBs8ElpiBw==", "path": "automapper.extensions.microsoft.dependencyinjection/11.0.0", "hashPath": "automapper.extensions.microsoft.dependencyinjection.11.0.0.nupkg.sha512"}, "Automatonymous/5.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-7B0upgJUTJ7fIwFjGEIxOMVJTxqBPyjtnua5i+VyClvHJd31t64SPIm4nhYxI7PF4afmvv5a3dtFCz1W1tQc0A==", "path": "automatonymous/5.1.3", "hashPath": "automatonymous.5.1.3.nupkg.sha512"}, "Castle.Core/4.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-zanbjWC0Y05gbx4eGXkzVycOQqVOFVeCjVsDSyuao9P4mtN1w3WxxTo193NGC7j3o2u3AJRswaoC6hEbnGACnQ==", "path": "castle.core/4.4.1", "hashPath": "castle.core.4.4.1.nupkg.sha512"}, "Castle.Core.AsyncInterceptor/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w4zHPQxJ1JYLitLqhIwntOFcWFa2n5VloQ1ZRkUgXlnjizoVaKRliZOvcBHpF6SNx9kg+SJq0AiWIUOML0Xt5A==", "path": "castle.core.asyncinterceptor/2.0.0", "hashPath": "castle.core.asyncinterceptor.2.0.0.nupkg.sha512"}, "GreenPipes/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nc90y7DAhj8isRbqioVQF3/ExztBZSXRrRoplZvEjckNFC5wP1r+ssfsgl8BptWdQrnMdgkOYhQ6EnHetyFW1Q==", "path": "greenpipes/4.0.1", "hashPath": "greenpipes.4.0.1.nupkg.sha512"}, "Hellang.Middleware.ProblemDetails/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-KGz2PJYxzhg7kNPfMAsKj+3H9NR9Ojf2QH2x85N+DZI6iWhGMSqgWu6qmET1wVNZY0JMrGbsO7HX5heq9ZAjwQ==", "path": "hellang.middleware.problemdetails/6.4.0", "hashPath": "hellang.middleware.problemdetails.6.4.0.nupkg.sha512"}, "MassTransit/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-dMOmC1ucSpgpW1M7ewQIw2Jf/d0lC4sx5LdUU5CSuFhlw4L7hssAB/glrbwvYZB9B90ZYvLJB8JLV1K/xhHBMA==", "path": "masstransit/7.3.1", "hashPath": "masstransit.7.3.1.nupkg.sha512"}, "MassTransit.AspNetCore/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-l5zu6mHevPQGP1tfDg4My8dtqH4n2jL22+Y1+GnAs12liQy5gYLcJAwSmCKb1iusjjD9S1tgfdILVkAfShfN8w==", "path": "masstransit.aspnetcore/7.3.1", "hashPath": "masstransit.aspnetcore.7.3.1.nupkg.sha512"}, "MassTransit.Extensions.DependencyInjection/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-s1gnkYUngHNP9m2+Jx1vuUAvoVm4KB3oVbtFJUlXNtBBzhRFq1N3Y0w5iiew60ZBLm0j0RQcSRbI0ahMOkL+og==", "path": "masstransit.extensions.dependencyinjection/7.3.1", "hashPath": "masstransit.extensions.dependencyinjection.7.3.1.nupkg.sha512"}, "MassTransit.RabbitMQ/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-hjoAyWR+PQe7bkTiGWGB8G3N3cmE7ekwrPCrNAoxjTqy/01hB2Cp0z6wWhlChibWEkn04i30tLsOJmSWMcQz7g==", "path": "masstransit.rabbitmq/7.3.1", "hashPath": "masstransit.rabbitmq.7.3.1.nupkg.sha512"}, "Microsoft.AspNet.WebApi.Client/5.2.7": {"type": "package", "serviceable": true, "sha512": "sha512-/76fAHknzvFqbznS6Uj2sOyE9rJB3PltY+f53TH8dX9RiGhk02EhuFCWljSj5nnqKaTsmma8DFR50OGyQ4yJ1g==", "path": "microsoft.aspnet.webapi.client/5.2.7", "hashPath": "microsoft.aspnet.webapi.client.5.2.7.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-b0R9X7L6zMqNsssKDvhYHuNi5x0s4DyHTeXybIAyGaitKiW1Q5aAGKdV2codHPiePv9yHfC9hAMyScXQ/xXhPw==", "path": "microsoft.aspnetcore.authentication/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Cookies/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Iar9VFlBHkZGdSG9ZUTmn6Q8Qg+6CtW5G/TyJI2F8B432TOH+nZlkU7O0W0byow6xsxqOYeTviSHz4cCJ3amfQ==", "path": "microsoft.aspnetcore.authentication.cookies/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.cookies.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GXmMD8/vuTLPLvKzKEPz/4vapC5e0cwx1tUVd83ePRyWF9CCrn/pg4/1I+tGkQqFLPvi3nlI2QtPtC6MQN8Nww==", "path": "microsoft.aspnetcore.cryptography.internal/2.2.0", "hashPath": "microsoft.aspnetcore.cryptography.internal.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-G6dvu5Nd2vjpYbzazZ//qBFbSEf2wmBUbyAR7E4AwO3gWjhoJD5YxpThcGJb7oE3VUcW65SVMXT+cPCiiBg8Sg==", "path": "microsoft.aspnetcore.dataprotection/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-seANFXmp8mb5Y12m1ShiElJ3ZdOT3mBN3wA1GPhHJIvZ/BxOCPyqEOR+810OWsxEZwA5r5fDRNpG/CqiJmQnJg==", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YogBSMotWPAS/X5967pZ+yyWPQkThxhmzAwyCHCSSldzYBkW5W5d6oPfBaPqQOnSHYTpSOSOkpZoAce0vwb6+A==", "path": "microsoft.aspnetcore.http/2.2.0", "hashPath": "microsoft.aspnetcore.http.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-W8<PERSON>QjkMScOMTtJbPwmPyj9c3zYSFGawDW3jwlBOOsnY+EzZFLgNQ/UMkK35JmkNOVPdCyPr2Tw7Vv9N+KA3ZQ==", "path": "microsoft.bcl.asyncinterfaces/5.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-2HjSGp63VCLQaeGadrLYR868g25mJHr+TFF81yWCaClzjUbU2vNDx6km7SUgPnoLVksE/1e7in88eh+oPtc4aQ==", "path": "microsoft.dotnet.platformabstractions/2.0.4", "hashPath": "microsoft.dotnet.platformabstractions.2.0.4.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "path": "microsoft.extensions.caching.abstractions/6.0.0", "hashPath": "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/1qPCleFOkJe0O+xmFqCNLFYQZTJz965sVw8CUB/BQgsApBwzAUsL2BUkDvQW+geRUVTXUS9zLa0pBjC2VJ1gA==", "path": "microsoft.extensions.caching.memory/5.0.0", "hashPath": "microsoft.extensions.caching.memory.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lu41BWNmwhKr6LgyQvcYBOge0pPvmiaK8R5UHXX4//wBhonJyWcT2OK1mqYfEM5G7pTf31fPrpIHOT6sN7EGOA==", "path": "microsoft.extensions.configuration/3.1.0", "hashPath": "microsoft.extensions.configuration.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Of1Irt1+NzWO+yEYkuDh5TpT4On7LKl98Q9iLqCdOZps6XXEWDj3AKtmyvzJPVXZe4apmkJJIiDL7rR1yC+hjQ==", "path": "microsoft.extensions.configuration.binder/5.0.0", "hashPath": "microsoft.extensions.configuration.binder.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-KDjz4HBw549oW4Y1eQAImAugCSYn84KXwgGZtgMqEwctvkdxZo0ObZC5QQI/YvX21U+DArR9+NfWZ3MxD9MhPA==", "path": "microsoft.extensions.configuration.commandline/3.1.0", "hashPath": "microsoft.extensions.configuration.commandline.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-WryksPlAFFRMWIGpFwDDbrVSD/kSO7P7fRRzBHh6vEIrgflsM8tpPCcgIvKszH4fz4vcuapih9RMdiiJ2VS7aw==", "path": "microsoft.extensions.configuration.environmentvariables/3.1.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-OjRJIkVxUFiVkr9a39AqVThft9QHoef4But5pDCydJOXJ4D/SkmzuW1tm6J2IXynxj6qfeAz9QTnzQAvOcGvzg==", "path": "microsoft.extensions.configuration.fileextensions/3.1.0", "hashPath": "microsoft.extensions.configuration.fileextensions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-gBpBE1GoaCf1PKYC7u0Bd4mVZ/eR2bnOvn7u8GBXEy3JGar6sC3UVpVfTB9w+biLPtzcukZynBG9uchSBbLTNQ==", "path": "microsoft.extensions.configuration.json/3.1.0", "hashPath": "microsoft.extensions.configuration.json.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZS71TEcxggal3Koh2qHo6s7aNLhwpW2veNCKaxB6nPJabdZBU/sExhszd6JjuCeYMFxDHL1ygJ/N+ghKSrFNDQ==", "path": "microsoft.extensions.configuration.usersecrets/3.1.0", "hashPath": "microsoft.extensions.configuration.usersecrets.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k6PWQMuoBDGGHOQTtyois2u4AwyVcIwL2LaSLlTZQm2CYcJ1pxbt6jfAnpWmzENA/wfrYRI/X9DTLoUkE4AsLw==", "path": "microsoft.extensions.dependencyinjection/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-jnHAeijsfJFQXdXmnYK/NhQIkgBUeth//RZZkf0ldIKC+jARbf7YxbA9uTrs/EPhuQxHXaDxVuMyscgmL+UqfA==", "path": "microsoft.extensions.dependencymodel/2.0.4", "hashPath": "microsoft.extensions.dependencymodel.2.0.4.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks/3.1.18": {"type": "package", "serviceable": true, "sha512": "sha512-Xan7z1MBhcLu4StnAskguGDMmdqouuuQe0YRz0MEiVPSYK5fw008QalP2Vuq0CMZQD+lOk0DcMGx7A+qI8/aTw==", "path": "microsoft.extensions.diagnostics.healthchecks/3.1.18", "hashPath": "microsoft.extensions.diagnostics.healthchecks.3.1.18.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/3.1.18": {"type": "package", "serviceable": true, "sha512": "sha512-PRj7waNZJ2e5G5VmoZxF0nNa2jexr+VDR5gq9ZV8GbCGaSmKkS1bs5fAiIx/NARs4XvSIE3prHf3/w6SLFeW9Q==", "path": "microsoft.extensions.diagnostics.healthchecks.abstractions/3.1.18", "hashPath": "microsoft.extensions.diagnostics.healthchecks.abstractions.3.1.18.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-KsvgrYp2fhNXoD9gqSu8jPK9Sbvaa7SqNtsLqHugJkCwFmgRvdz76z6Jz2tlFlC7wyMTZxwwtRF8WAorRQWTEA==", "path": "microsoft.extensions.fileproviders.physical/3.1.0", "hashPath": "microsoft.extensions.fileproviders.physical.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tK5HZOmVv0kUYkonMjuSsxR0CBk+Rd/69QU3eOMv9FvODGZ2d0SR+7R+n8XIgBcCCoCHJBSsI4GPRaoN3Le4rA==", "path": "microsoft.extensions.filesystemglobbing/3.1.0", "hashPath": "microsoft.extensions.filesystemglobbing.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-oM5Zd2eI8tbY+5hR/ZusdS/mcYb25XVLMEohl6rZv8NcfeSEqlZG5YVo81DQGnWhB1OB2nHMVgXj6RN3tEqAqQ==", "path": "microsoft.extensions.hosting/3.1.0", "hashPath": "microsoft.extensions.hosting.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GcT5l2CYXL6Sa27KCSh0TixsRfADUgth+ojQSD5EkzisZxmGFh7CwzkcYuGwvmXLjr27uWRNrJ2vuuEjMhU05Q==", "path": "microsoft.extensions.hosting.abstractions/6.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "path": "microsoft.extensions.http/6.0.0", "hashPath": "microsoft.extensions.http.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-yW3nIoNM3T5iZg8bRViiCN4+vIU/02l+mlWSvKqWnr0Fd5Uk1zKdT9jBWKEcJhRIWKVWWSpFWXnM5yWoIAy1Eg==", "path": "microsoft.extensions.logging.configuration/3.1.0", "hashPath": "microsoft.extensions.logging.configuration.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-lkqVlnQqfqr8nmrpy7EnISXy192NEaIb0Xh4zaO8XS/QMiC3RqOAxxeq89JPSmjw+YfecwcikV8UB+NacdgRDg==", "path": "microsoft.extensions.logging.console/3.1.0", "hashPath": "microsoft.extensions.logging.console.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTuUb46wKuZD2nuvzmlHQfbm9sUAiOg1x1pe1fzxDHaocXaZQDjKGYYZifnTLeJk35OSi8ouEIcbuKLVF16yfg==", "path": "microsoft.extensions.logging.debug/3.1.0", "hashPath": "microsoft.extensions.logging.debug.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-9m5orA0YkzN6DGZIMqMi0SYel2LPhQitaUSMhA9Rocu6k6ZTYJvhqaRYACC4uhpymPyOf0fQaAGK13sqPWi9MA==", "path": "microsoft.extensions.logging.eventlog/3.1.0", "hashPath": "microsoft.extensions.logging.eventlog.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-HT5UXDPIQFBZob4rF/jwErm/tawmThqhyDdlxLv+VFEAdsgq7/XwMqWLlF59aPvK+ypI0sneV2GUlB4KfWPuSA==", "path": "microsoft.extensions.logging.eventsource/3.1.0", "hashPath": "microsoft.extensions.logging.eventsource.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-pH7eaZaOxrbGnGlCW0nCOK6jbZCqMjOlTwe7XTl+aXMueiR7dEyuzvpd8cTlGfigGgJU648eraIPEwqI+nn3mA==", "path": "microsoft.extensions.options.configurationextensions/3.1.1", "hashPath": "microsoft.extensions.options.configurationextensions.3.1.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8XcqYcpcdBAxUhLeyYcuKmxu4CtNQA9IphTnARpQGhkop4A93v2XgM3AtaVVJo3H2cDWxWM6aeO8HxkifREqw==", "path": "microsoft.extensions.webencoders/2.2.0", "hashPath": "microsoft.extensions.webencoders.2.2.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xzOM8GAADDZvHWsDhlZQYIAA4hxMHmAUgzbg64XQxT9SQ3iPugJ0jr+KQPqRFAboJRDe4eenz0AwiuhmjC+4gA==", "path": "microsoft.io.recyclablememorystream/1.3.2", "hashPath": "microsoft.io.recyclablememorystream.1.3.2.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bh6blKG8VAKvXiLe2L+sEsn62nc1Ij34MrNxepD2OCrS5cpCwQa9MeLyhVQPQ/R4Wlzwuy6wMK8hLb11QPDRsQ==", "path": "microsoft.win32.systemevents/5.0.0", "hashPath": "microsoft.win32.systemevents.5.0.0.nupkg.sha512"}, "Net.Ams/0.2.20": {"type": "package", "serviceable": true, "sha512": "sha512-7oBTmnYaD3icQBI2/t0yJRMN0WmF+vKZcpF/O+zTYZL0CD5b7obWk4Xu05z6OdBoe0l6fvCkYJM05YspcrWkFQ==", "path": "net.ams/0.2.20", "hashPath": "net.ams.0.2.20.nupkg.sha512"}, "Net.Caching/0.2.20": {"type": "package", "serviceable": true, "sha512": "sha512-33NAJoRxPVHuiSMmnogaKg7attAPnt+enQ4eDubyH2fZh0mtmnYhtFUSk6hYysPqUTC2EoeAyLVtycndQyCSDg==", "path": "net.caching/0.2.20", "hashPath": "net.caching.0.2.20.nupkg.sha512"}, "Net.Common/0.2.20": {"type": "package", "serviceable": true, "sha512": "sha512-y+8Csyd8/bhjTEB1EVRG+WEUJlNSnIvYQ3fxkyAReqEA/uv0HvnBK7dKuxDcKSa8h6u1t8ub0n2WTcecJkcAhg==", "path": "net.common/0.2.20", "hashPath": "net.common.0.2.20.nupkg.sha512"}, "Net.Common.Mvc/0.2.20": {"type": "package", "serviceable": true, "sha512": "sha512-+kERWbSKg+b58yWaCNbRknir6RxF2NOAL/S9itKknZ2lBK4IRqhXf/I2Zy6qqZ3IEziw8ZukwgSu9ruNHlDHIQ==", "path": "net.common.mvc/0.2.20", "hashPath": "net.common.mvc.0.2.20.nupkg.sha512"}, "Net.Logging/0.2.20": {"type": "package", "serviceable": true, "sha512": "sha512-qKhB07hEKuVBUUbyrneT55aAg4Zpr6tWApPwtFd8v37nwfzAdJXO+th8iPlnuAvVzyflz4xeTguZ4sfx/RF84w==", "path": "net.logging/0.2.20", "hashPath": "net.logging.0.2.20.nupkg.sha512"}, "Net.Metrics/0.2.20": {"type": "package", "serviceable": true, "sha512": "sha512-8ymsI2EEuSSvMFXeJkvrdR9ZkRDO/909FiwkurPOPf0tDynbKUrkYJxfwI/wL7RaGRy/fHzrGmJ5se8J82wxXQ==", "path": "net.metrics/0.2.20", "hashPath": "net.metrics.0.2.20.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "NewId/3.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-72cBqYQ+pZpsfuTerhi5Q/ZkfK81s/Dkz2u0WNytirWVLktUm6sy4mRNepM+DL1AB0V61QlYesZHDf0wlmMU5A==", "path": "newid/3.0.3", "hashPath": "newid.3.0.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "OpenTracing/0.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-8i/Vnx/lbWzqqJ6J5lofguT4wBS99rfqKujWrFrTGAclQBZ5h1CgBlzGOTqsNjmMsxSTLpC+Ns6/f1RB0c4O/g==", "path": "opentracing/0.12.1", "hashPath": "opentracing.0.12.1.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7hzHplEIVOGBl5zOQZGX/DiJDHjq+RVRVrYgDiqXb6RriqWAdacXxp+XO9WSrATCEXyNOUOQg9aqQArsjase/A==", "path": "pipelines.sockets.unofficial/2.2.0", "hashPath": "pipelines.sockets.unofficial.2.2.0.nupkg.sha512"}, "protobuf-net/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-j37MD1p1s9NdX8P5+IaY2J9p2382xiL1VP3mxYu0g+G/kf2YM2grFa1jJPO+0WDJNl1XhNPO0Q5yBEcbX77hBQ==", "path": "protobuf-net/2.4.0", "hashPath": "protobuf-net.2.4.0.nupkg.sha512"}, "RabbitMQ.Client/6.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-+tJSgE+rQXgLvOxWvOxdZ0+No1EMa86+puZuNlCb4cNryHw2PY2Xiw/y2dz/WWdeJQp74Y2UnNXPkuLiYpvn+A==", "path": "rabbitmq.client/6.2.2", "hashPath": "rabbitmq.client.6.2.2.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "Serilog/2.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-+QX0hmf37a0/OZLxM3wL7V6/ADvC1XihXN4Kq/p6d8lCPfgkRdiuhbWlMaFjR9Av0dy5F0+MBeDmDdRZN/YwQA==", "path": "serilog/2.10.0", "hashPath": "serilog.2.10.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gT2keceCmPQR9EX0VpXQZvUgELdfE7yqJ7MOxBhm3WLCblcvRgswEOOTgok/DHObbM15A3V/DtF3VdVDQPIZzQ==", "path": "serilog.extensions.hosting/4.2.0", "hashPath": "serilog.extensions.hosting.4.2.0.nupkg.sha512"}, "Serilog.Extensions.Logging/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-IWfem7wfrFbB3iw1OikqPFNPEzfayvDuN4WP7Ue1AVFskalMByeWk3QbtUXQR34SBkv1EbZ3AySHda/ErDgpcg==", "path": "serilog.extensions.logging/3.1.0", "hashPath": "serilog.extensions.logging.3.1.0.nupkg.sha512"}, "Serilog.Settings.Configuration/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BS+G1dhThTHBOYm8R21JNlR+Nh7ETAOlJuL1P6te1rOG98eV1vos5EyWRTGr0AbHgySxsGu1Q/evfFxS9+Gk1Q==", "path": "serilog.settings.configuration/3.1.0", "hashPath": "serilog.settings.configuration.3.1.0.nupkg.sha512"}, "Serilog.Sinks.Async/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-csHYIqAwI4Gy9oAhXYRwxGrQEAtBg3Ep7WaCzsnA1cZuBZjVAU0n7hWaJhItjO7hbLHh/9gRVxALCUB4Dv+gZw==", "path": "serilog.sinks.async/1.5.0", "hashPath": "serilog.sinks.async.1.5.0.nupkg.sha512"}, "Serilog.Sinks.Console/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-apLOvSJQLlIbKlbx+Y2UDHSP05kJsV7mou+fvJoRGs/iR+jC22r8cuFVMjjfVxz/AD4B2UCltFhE1naRLXwKNw==", "path": "serilog.sinks.console/4.0.1", "hashPath": "serilog.sinks.console.4.0.1.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "StackExchange.Redis/2.2.88": {"type": "package", "serviceable": true, "sha512": "sha512-JJi1jcO3/ZiamBhlsC/TR8aZmYf+nqpGzMi0HRRCy5wJkUPmMnRp0kBA6V84uhU8b531FHSdTDaFCAyCUJomjA==", "path": "stackexchange.redis/2.2.88", "hashPath": "stackexchange.redis.2.2.88.nupkg.sha512"}, "Steeltoe.Common/3.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-hmBnwzG8SZbN1nXBMHNJQY4dOslC2OUbVPh5Ddvz0Ny+3RuIssUNc6Hu4EJYZx5i4zp5C5yzmf2IR1MItbIKgQ==", "path": "steeltoe.common/3.1.3", "hashPath": "steeltoe.common.3.1.3.nupkg.sha512"}, "Steeltoe.Common.Abstractions/3.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-6JToDUN2PROME4rnUDn4rinvSNW1sZAUDUCdLW34Abc5zgspSpz1HOQQgp/HNRPKIXjLF3iQZGVoOVy78Gy+fw==", "path": "steeltoe.common.abstractions/3.1.3", "hashPath": "steeltoe.common.abstractions.3.1.3.nupkg.sha512"}, "Steeltoe.Common.Http/3.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-SpavdBKJS7DBOktGxpgxL+B0lChp39OtQUU+R9O03wIr1Mu/orgXmyaLYm5RVSMlZgy6bStbjZnwB5bYFj1oGQ==", "path": "steeltoe.common.http/3.1.3", "hashPath": "steeltoe.common.http.3.1.3.nupkg.sha512"}, "Steeltoe.Connector.Abstractions/3.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-IEZsN+uXOcUfhcD15HgwcUtcP/zSYpK7kdqcxF0saSSvxMISWVra5B2xK200aTayGF5tH4ZNbpMz5vShJFxWzw==", "path": "steeltoe.connector.abstractions/3.1.3", "hashPath": "steeltoe.connector.abstractions.3.1.3.nupkg.sha512"}, "Steeltoe.Connector.ConnectorBase/3.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-fB1o5e9iRP3wH5R0LnXmOJeYq984xMJ0QYc/0G8jBrywku3fFSdCgZU6ZhJTlG4HKPqDHP8ZHCAOq8/oXmXO1A==", "path": "steeltoe.connector.connectorbase/3.1.3", "hashPath": "steeltoe.connector.connectorbase.3.1.3.nupkg.sha512"}, "Steeltoe.Discovery.Abstractions/3.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-tHksjDGbY/OT05Z+D1s0Dl/UPUPdtaRWlsipwEco148OrO101I/PyEMf+i0G/vq7CgmXr2/XZy/jxj7WCremrQ==", "path": "steeltoe.discovery.abstractions/3.1.3", "hashPath": "steeltoe.discovery.abstractions.3.1.3.nupkg.sha512"}, "Steeltoe.Discovery.ClientBase/3.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-BM5st3PHX4N2mSl/0SLvz4g+MP6yPruRssfKp7qzVPzMxbbvH8w0ANtq/XrCe/5ID5fikJ+CZyOF+vTwxXLpPA==", "path": "steeltoe.discovery.clientbase/3.1.3", "hashPath": "steeltoe.discovery.clientbase.3.1.3.nupkg.sha512"}, "Steeltoe.Discovery.Eureka/3.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-8li8UsXn9VMXQkIMkdO5uUvUCPe/sASCV9a+vLngZ+8DQJb5gPvraVY65dyPgIQzPyApUj+TFWLE1Eqdkq4DlA==", "path": "steeltoe.discovery.eureka/3.1.3", "hashPath": "steeltoe.discovery.eureka.3.1.3.nupkg.sha512"}, "Steeltoe.Extensions.Configuration.Abstractions/3.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-fbnoWhwPix7FJpc7aEi7XINY7WSjP6r53HujS7vEJEhbGmFMu8oh2RJqJ+aR1kbV8M1Y1lQ51EkPo46R3lnKlA==", "path": "steeltoe.extensions.configuration.abstractions/3.1.3", "hashPath": "steeltoe.extensions.configuration.abstractions.3.1.3.nupkg.sha512"}, "StyleCop.Analyzers/1.1.118": {"type": "package", "serviceable": true, "sha512": "sha512-Onx6ovGSqXSK07n/0eM3ZusiNdB6cIlJdabQhWGgJp3Vooy9AaLS/tigeybOJAobqbtggTamoWndz72JscZBvw==", "path": "stylecop.analyzers/1.1.118", "hashPath": "stylecop.analyzers.1.1.118.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aM7cbfEfVNlEEOj3DsZP+2g9NRwbkyiAv2isQEzw7pnkDg9ekCU2m1cdJLM02Uq691OaCS91tooaxcEn8d0q5w==", "path": "system.configuration.configurationmanager/5.0.0", "hashPath": "system.configuration.configurationmanager.5.0.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-frQDfv0rl209cKm1lnwTgFPzNigy2EKk1BS3uAvHvlBVKe5cymGyHO+Sj+NLv5VF/AhHsqPIUUwya5oV4CHMUw==", "path": "system.diagnostics.diagnosticsource/6.0.0", "hashPath": "system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-iDoKGQcRwX0qwY+eAEkaJGae0d/lHlxtslO+t8pJWAUxlvY3tqLtVOPnW2UU4cFjP0Y/L1QBqhkZfSyGqVMR2w==", "path": "system.diagnostics.eventlog/4.7.0", "hashPath": "system.diagnostics.eventlog.4.7.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kcQWWtGVC3MWMNXdMDWfrmIlFZZ2OdoeT6pSNVRtk9+Sa7jwdPiMlNwb0ZQcS7NRlT92pCfmjRtkSWUW3RAKwg==", "path": "system.diagnostics.performancecounter/5.0.0", "hashPath": "system.diagnostics.performancecounter.5.0.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.TraceSource/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VnYp1NxGx8Ww731y2LJ1vpfb/DKVNKEZ8Jsh5SgQTZREL/YpWRArgh9pI8CDLmgHspZmLL697CaLvH85qQpRiw==", "path": "system.diagnostics.tracesource/4.3.0", "hashPath": "system.diagnostics.tracesource.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SztFwAnpfKC8+sEKXAFxCBWhKQaEd97EiOL7oZJZP56zbqnLpmxACWA8aGseaUExciuEAUuR9dY8f7HkTRAdnw==", "path": "system.drawing.common/5.0.0", "hashPath": "system.drawing.common.5.0.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Abstractions/16.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-/aIq/+5c6ikSG898YHdf/d0bkFeRIxFtIvwwrstQQaLVBFGnwXjr0vpzbDIKgU97FFiTcb2FLli1egkwDcfx0g==", "path": "system.io.abstractions/16.1.7", "hashPath": "system.io.abstractions.16.1.7.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-irMYm3vhVgRsYvHTU5b2gsT2CwT/SMM6LZFzuJjpIvT5Z4CshxNsaoBC1X/LltwuR3Opp8d6jOS/60WwOb7Q2Q==", "path": "system.io.pipelines/5.0.0", "hashPath": "system.io.pipelines.5.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Net.Http/4.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "path": "system.net.http/4.3.4", "hashPath": "system.net.http.4.3.4.nupkg.sha512"}, "System.Net.Http.Json/3.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KkevRTwX9uMYxuxG2/wSql8FIAItB89XT36zoh6hraQkFhf2yjotDswpAKzeuaEuMhAia6c50oZMkP1PJoYufQ==", "path": "system.net.http.json/3.2.1", "hashPath": "system.net.http.json.3.2.1.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.ServiceModel/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-ancrQgJagx+yC4SZbuE+eShiEAUIF0E1d21TRSoy1C/rTwafAVcBr/fKibkq5TQzyy9uNil2tx2/iaUxsy0S9g==", "path": "system.private.servicemodel/4.5.3", "hashPath": "system.private.servicemodel.4.5.3.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-+UW1hq11TNSeb+16rIk8hRQ02o339NFyzMc4ma/FqmxBzM30l1c2IherBB4ld1MNcenS48fz8tbt50OW4rVULA==", "path": "system.reflection.dispatchproxy/4.5.0", "hashPath": "system.reflection.dispatchproxy.4.5.0.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "path": "system.reflection.emit.lightweight/4.7.0", "hashPath": "system.reflection.emit.lightweight.4.7.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "path": "system.reflection.metadata/5.0.0", "hashPath": "system.reflection.metadata.5.0.0.nupkg.sha512"}, "System.Reflection.MetadataLoadContext/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-TezS9fEP9kzL5U6GYHZY6I/tqz6qiHKNgAzuT6JJXJXuP+wWvNLN03gPxBK2uLP0LrLg/QXEAF++lxBNBSYILA==", "path": "system.reflection.metadataloadcontext/4.6.0", "hashPath": "system.reflection.metadataloadcontext.4.6.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-TGQX51gxpY3K3I6LJlE2LAftVlIMqJf0cBGhz68Y89jjk3LJCB6SrwiD+YN1fkqemBvWGs+GjyMJukl6d6goyQ==", "path": "system.security.cryptography.pkcs/4.5.0", "hashPath": "system.security.cryptography.pkcs.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HGxMSAFAPLNoxBvSfW08vHde0F9uh7BjASwu6JF9JnXuEPhCY3YUqURn0+bQV/4UWeaqymmrHWV+Aw9riQCtCA==", "path": "system.security.cryptography.protecteddata/5.0.0", "hashPath": "system.security.cryptography.protecteddata.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-i2Jn6rGXR63J0zIklImGRkDIJL4b1NfPSEbIVHBlqoIb12lfXIigCbDRpDmIEzwSo/v1U5y/rYJdzZYSyCWxvg==", "path": "system.security.cryptography.xml/4.5.0", "hashPath": "system.security.cryptography.xml.4.5.0.nupkg.sha512"}, "System.Security.Permissions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uE8juAhEkp7KDBCdjDIE3H9R1HJuEHqeqX8nLX9gmYKWwsqk3T5qZlPx8qle5DPKimC/Fy3AFTdV7HamgCh9qQ==", "path": "system.security.permissions/5.0.0", "hashPath": "system.security.permissions.5.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceModel.Primitives/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-Wc9Hgg4Cmqi416zvEgq2sW1YYCGuhwWzspDclJWlFZqY6EGhFUPZU+kVpl5z9kAgrSOQP7/Uiik+PtSQtmq+5A==", "path": "system.servicemodel.primitives/4.5.3", "hashPath": "system.servicemodel.primitives.4.5.3.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xg4G4Indi4dqP1iuAiMSwpiWS54ZghzR644OtsRCm/m/lBMG8dUBhLVN7hLm8NNrNTR+iGbshCPTwrvxZPlm4g==", "path": "system.text.encodings.web/4.5.0", "hashPath": "system.text.encodings.web.4.5.0.nupkg.sha512"}, "System.Text.Json/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-I47dVIGiV6SfAyppphxqupertT/5oZkYLDCX6vC3HpOI4ZLjyoKAreUoem2ie6G0RbRuFrlqz/PcTQjfb2DOfQ==", "path": "system.text.json/5.0.2", "hashPath": "system.text.json.5.0.2.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Channels/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-6akRtHK/wab3246t4p5v3HQrtQk8LboOt5T4dtpNgsp3zvDeM4/Gx8V12t0h+c/W9/enUrilk8n6EQqdQorZAA==", "path": "system.threading.channels/4.7.1", "hashPath": "system.threading.channels.4.7.1.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-npvJkVKl5rKXrtl1Kkm6OhOUaYGEiF9wFbppFRWSMoApKzt2PiPHT2Bb8a5sAWxprvdOAtvaARS9QYMznEUtug==", "path": "system.threading.tasks.extensions/4.3.0", "hashPath": "system.threading.tasks.extensions.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "System.Windows.Extensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c1ho9WU9ZxMZawML+ssPKZfdnrg/OjR3pe0m9v8230z3acqphwvPJqzAkH54xRYm5ntZHGG1EPP3sux9H3qSPg==", "path": "system.windows.extensions/5.0.0", "hashPath": "system.windows.extensions.5.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "EtWS.ApiClients/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EtWS.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "EtWS.Models/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}