﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.9.34622.214
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Web", "Web", "{D39061E2-6ABF-47C2-BD67-6F55449F297A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EtWS.Api", "EtWS.Api\EtWS.Api.csproj", "{DB10356C-F583-46CA-A14F-C69CCA2B7EE7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{FDA3DBDC-F03B-4A29-9F19-79C7483A3DFA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EtWS.Models", "EtWS.Models\EtWS.Models.csproj", "{18698C04-5306-44B6-9277-B08CAD656C82}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EtWS.Services", "EtWS.Services\EtWS.Services.csproj", "{1BE0B03D-AE71-4F03-B6C9-6820AB922A0C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Data", "Data", "{DB222FC6-069E-4CF2-BE24-15BED0CEF2DD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EtWS.ApiClients", "EtWS.ApiClients\EtWS.ApiClients.csproj", "{B238FFF5-1F9A-45F5-B6E2-123AEFCC1EC5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{E0EBDBCF-ECF4-4397-B841-C7FBBD985D34}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EtWS.Infrastructure", "EtWS.Infrastructure\EtWS.Infrastructure.csproj", "{34D242B0-7AD3-411C-B03D-C1A8560D7823}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{14E1FB13-F6D7-47D9-BB38-EAFD0AE912FD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EtWS.Services.Tests", "EtWS.Services.Tests\EtWS.Services.Tests.csproj", "{004E50D1-**************-C7F6ED3423AD}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DB10356C-F583-46CA-A14F-C69CCA2B7EE7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DB10356C-F583-46CA-A14F-C69CCA2B7EE7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DB10356C-F583-46CA-A14F-C69CCA2B7EE7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DB10356C-F583-46CA-A14F-C69CCA2B7EE7}.Release|Any CPU.Build.0 = Release|Any CPU
		{18698C04-5306-44B6-9277-B08CAD656C82}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{18698C04-5306-44B6-9277-B08CAD656C82}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{18698C04-5306-44B6-9277-B08CAD656C82}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{18698C04-5306-44B6-9277-B08CAD656C82}.Release|Any CPU.Build.0 = Release|Any CPU
		{1BE0B03D-AE71-4F03-B6C9-6820AB922A0C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1BE0B03D-AE71-4F03-B6C9-6820AB922A0C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1BE0B03D-AE71-4F03-B6C9-6820AB922A0C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1BE0B03D-AE71-4F03-B6C9-6820AB922A0C}.Release|Any CPU.Build.0 = Release|Any CPU
		{B238FFF5-1F9A-45F5-B6E2-123AEFCC1EC5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B238FFF5-1F9A-45F5-B6E2-123AEFCC1EC5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B238FFF5-1F9A-45F5-B6E2-123AEFCC1EC5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B238FFF5-1F9A-45F5-B6E2-123AEFCC1EC5}.Release|Any CPU.Build.0 = Release|Any CPU
		{34D242B0-7AD3-411C-B03D-C1A8560D7823}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{34D242B0-7AD3-411C-B03D-C1A8560D7823}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{34D242B0-7AD3-411C-B03D-C1A8560D7823}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{34D242B0-7AD3-411C-B03D-C1A8560D7823}.Release|Any CPU.Build.0 = Release|Any CPU
		{004E50D1-**************-C7F6ED3423AD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{004E50D1-**************-C7F6ED3423AD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{004E50D1-**************-C7F6ED3423AD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{004E50D1-**************-C7F6ED3423AD}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{DB10356C-F583-46CA-A14F-C69CCA2B7EE7} = {D39061E2-6ABF-47C2-BD67-6F55449F297A}
		{18698C04-5306-44B6-9277-B08CAD656C82} = {FDA3DBDC-F03B-4A29-9F19-79C7483A3DFA}
		{1BE0B03D-AE71-4F03-B6C9-6820AB922A0C} = {FDA3DBDC-F03B-4A29-9F19-79C7483A3DFA}
		{B238FFF5-1F9A-45F5-B6E2-123AEFCC1EC5} = {DB222FC6-069E-4CF2-BE24-15BED0CEF2DD}
		{34D242B0-7AD3-411C-B03D-C1A8560D7823} = {E0EBDBCF-ECF4-4397-B841-C7FBBD985D34}
		{004E50D1-**************-C7F6ED3423AD} = {14E1FB13-F6D7-47D9-BB38-EAFD0AE912FD}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {75661F40-975B-4948-B581-94D9F9DB887D}
	EndGlobalSection
EndGlobal
