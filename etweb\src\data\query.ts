import { useMutation, useQuery } from "@tanstack/react-query";
import { IncorrectEquipmentApi, AccountApi, type DeleteTransferRequest, type TransferCorrectionRequest, type UserLoginRequestModel } from "./etws";

const incorrectEquipmentApi = new IncorrectEquipmentApi(undefined, "/et-ws", undefined);
const accountApi = new AccountApi(undefined, "/et-ws", undefined);

export const login = async (credentials: UserLoginRequestModel) => {
  const res = await accountApi.apiAccountLoginPost(credentials);
  return res;
};

export const logout = async () => {
  const res = await accountApi.apiAccountLogoutPost();
  return res;
};

export const useValidateSerialNumber = () => {
  return useMutation({
    mutationFn: async (serialNumber: string) => {
      const response = await incorrectEquipmentApi.apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet(serialNumber);
      return response.data;
    },
    onError: (error) => {
      console.error("Error validating serial number:", error);
    },
  });
};

export const useGetOpQuery = () => {
  return useQuery({
    queryKey: ["opOptions"],
    queryFn: async () => {
      const response = await incorrectEquipmentApi.apiIncorrectEquipmentUsersWithOpcodesGet();
      return response.data;
    },
  });
};

export const useGetDatesBySerialNumberQuery = (equipmentSerialnumber: string) => {
  return useQuery({
    queryKey: ["equipmentSerialnumber", equipmentSerialnumber],
    queryFn: async ({ queryKey }) => {
      const equipmentSerialnumber = queryKey[1] as string;
      const response = await incorrectEquipmentApi.apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet(equipmentSerialnumber);
      return response.data;
    },
  });
};

export const useSubmitCorrection = () => {
  return useMutation({
    mutationFn: async (data: TransferCorrectionRequest) => {
      const response = await incorrectEquipmentApi.apiIncorrectEquipmentTransferCorrectionPost(undefined, undefined, data);
      return response.data;
    },
    onError: (error) => {
      console.error("Error submitting correction:", error);
    },
  });
};

export const useDeleteTransfer = () => {
  return useMutation({
    mutationFn: async (data: DeleteTransferRequest) => {
      const response = await incorrectEquipmentApi.apiIncorrectEquipmentTransferDeleteDelete(undefined, undefined, data);
      return response.data;
    },
    onError: (error) => {
      console.error("Error deleting transfer:", error);
    },
  });
};

export const useValidateServiceId = () => {
  return useMutation({
    mutationFn: async (serviceId: string) => {
      const response = await incorrectEquipmentApi.apiIncorrectEquipmentCheckServiceIdByServiceIdGet(serviceId);
      return response.data;
    },
    onError: (error) => {
      console.error("Error validating service ID:", error);
    },
  });
};

export const useGetTechnicianName = (namePrefix: string) => {
  return useQuery({
    queryKey: ["nameOfTechnician", namePrefix],
    queryFn: async ({ queryKey }) => {
      const namePrefix = queryKey[1] as string;
      if (!namePrefix) return null;
      const response = await incorrectEquipmentApi.apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet(namePrefix);
      return response.data;
    },
  });
};
