﻿namespace EtDb.Api.Controllers
{
    using EtDb.Api.Infrastructure.Utils;
    using EtDb.Models.Requests;
    using EtDb.Models.Responses;
    using EtDb.Services.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class CitiesController : BaseApiController
    {
        private readonly Lazy<ICitiesService> citiesService;

        public CitiesController(Lazy<ICitiesService> citiesService)
        {
            this.citiesService = citiesService;
        }

        [HttpGet]
        [ProducesResponseType(typeof(CitiesResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<CitiesResponseModel>> GetAsync(int id)
        {
            try
            {
                var city = await this.citiesService.Value.GetCityAsync(id);
                return this.Ok(city);
            }
            catch (ArgumentException ex)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = ex.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPost]
        [Route("search")]
        [ProducesResponseType(typeof(SearchResponseModel<CitiesResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<CitiesResponseModel>>> GetCitiesAsync(SearchRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.citiesService.Value.GetCitiesAsync(request);
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPost]
        [Route("add")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> AddCityAsync(CitiesRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var id = await this.citiesService.Value.AddCityAsync(request);
                    return this.Ok(id);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (ArgumentException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpPut]
        [Route("update")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateCityAsync([FromQuery] int id, [FromBody] CitiesRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    await this.citiesService.Value.UpdateCityAsync(id, request);
                    return this.Ok();
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (ArgumentException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpPut]
        [Route("block")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> BlockCitiesAsync(List<int> cityIds)
        {
            if (cityIds == null || !cityIds.Any() || cityIds.Any(id => !(id is int)))
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Invalid cityIds. The list must contain only integers and cannot be empty." });
            }

            try
            {
                await this.citiesService.Value.BlockCitiesAsync(cityIds);
                return this.Ok();
            }
            catch (ArgumentException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpPut]
        [Route("activate")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> ActivateCitiesAsync(List<int> cityIds)
        {
            if (cityIds == null || !cityIds.Any() || cityIds.Any(id => !(id is int)))
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Invalid cityIds. The list must contain only integers and cannot be empty." });
            }

            try
            {
                await this.citiesService.Value.ActivateCitiesAsync(cityIds);
                return this.Ok();
            }
            catch (ArgumentException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpGet]
        [Route("region")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<string>> GetCityRegionByIdAsync(int cityId)
        {
            if (cityId <= 0)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Invalid cityId. It must be a positive integer." });
            }

            try
            {
                var result = await this.citiesService.Value.GetCityRegionByIdAsync(cityId);
                return this.Ok(result);
            }
            catch (ArgumentException ex)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = ex?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }
    }
}
