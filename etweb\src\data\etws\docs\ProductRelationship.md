# ProductRelationship


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** |  | [optional] [default to undefined]
**relationshipDomainList** | [**Array&lt;ProductRelationshipDomain&gt;**](ProductRelationshipDomain.md) |  | [optional] [default to undefined]

## Example

```typescript
import { ProductRelationship } from './api';

const instance: ProductRelationship = {
    name,
    relationshipDomainList,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
