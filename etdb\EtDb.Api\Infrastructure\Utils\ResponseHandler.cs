﻿namespace EtDb.Api.Infrastructure.Utils
{
    using Microsoft.AspNetCore.Mvc.ModelBinding;

    public static class ResponseHandler
    {
        public static IEnumerable<string> GetModelErrorMessages(this ModelStateDictionary modelState)
        {
            return modelState?.Where(ms =>
                    ms.Value?.Errors != null && ms.Value.Errors.Any()).SelectMany(ms =>
                        ms.Value?.Errors?.Select(e => e?.ErrorMessage));
        }

        public static string GetFirstModelErrorMessage(this ModelStateDictionary modelState)
        {
            return modelState.GetModelErrorMessages().FirstOrDefault();
        }
    }
}
