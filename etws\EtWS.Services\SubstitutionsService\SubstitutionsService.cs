﻿namespace EtWS.Services.SubstitutionsService
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.SubstitutionModels;

    public class SubstitutionsService : ISubstitutionsService
    {
        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<IETDB> etDb;

        public SubstitutionsService(Lazy<IMapper> mapper, Lazy<IETDB> etDb)
        {
            this.mapper = mapper;
            this.etDb = etDb;
        }

        public async Task<SubstitutionsResponseModel> GetSubstitutionByIdAsync(int id)
        {
            return await this.etDb.Value.ApiSubstitutionsGetAsync(id);
        }

        public async Task<SearchResponseModel<SubstitutionsResponseModel>> GetSubstitutionsAsync(SearchDataRequestModel request, bool isCurrentUserMol)
        {
            var dbRequest = this.mapper.Value.Map<SearchRequestModel>(request);
            var filteredsubstitutions = await this.etDb.Value.ApiSubstitutionsSearchPostAsync(isCurrentUserMol: isCurrentUserMol, body: dbRequest);
            var substitutionsResponse = this.mapper.Value.Map<SearchResponseModel<SubstitutionsResponseModel>>(filteredsubstitutions);

            return substitutionsResponse;
        }

        public async Task AddSubstitutionAsync(SubstitutionDataRequestModel request)
        {
            var dbRequest = this.mapper.Value.Map<SubstitutionsRequestModel>(request);
            await this.etDb.Value.ApiSubstitutionsAddPostAsync(body: dbRequest);
        }

        public async Task UpdateSubstitutionAsync(SubstitutionDataRequestModel request)
        {
            var dbRequest = this.mapper.Value.Map<SubstitutionsRequestModel>(request);
            await this.etDb.Value.ApiSubstitutionsEditPutAsync(id: request.Id, body: dbRequest);
        }

        public async Task DeleteSubstitutionAsync(int id)
        {
            await this.etDb.Value.ApiSubstitutionsDeleteDeleteAsync(id);
        }

        public SubstitutionResponseModel GetActiveSubstitution(string substituteUserId)
        {
            // TODO!
            // var activeSubstitution = this.etDb.GetActiveSubstitutionByUserId(substituteUserId);
            // return activeSubstitution == null ? null : Mapper.Map<SubstitutionViewModel>(activeSubstitution);

            return new SubstitutionResponseModel
            {
                ForUserId = "0043e301-83db-4564-ac9d-fb39f20f1e82",
                SubstituteUserId = substituteUserId,
                ToDate = DateTime.Now.AddDays(1),
            };
        }

        public async Task UpdateForUserIdWhenMolChanges(EditUserRequest request)
        {

        }
    }
}
