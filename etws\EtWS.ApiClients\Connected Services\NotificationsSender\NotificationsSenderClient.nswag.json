﻿{
  "openapi": "3.0.1",
  "info": {
    "title": "notifications-sender",
    "version": "v1"
  },
  "servers": [
    {
      "url": "http://microit9app2.drcenter.btk.bg:31498/notifications-sender"
    }
  ],
  "paths": {
    "/api/email/with-template": {
      "post": {
        "tags": [
          "Email"
        ],
        "operationId": "ApiEmailWithTemplatePost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/EmailTemplateRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/EmailTemplateRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/EmailTemplateRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/NotificationResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/email/with-text": {
      "post": {
        "tags": [
          "Email"
        ],
        "operationId": "ApiEmailWithTextPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/EmailTextRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/EmailTextRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/EmailTextRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/NotificationResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/sms/with-template": {
      "post": {
        "tags": [
          "Sms"
        ],
        "operationId": "ApiSmsWithTemplatePost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SmsTemplateRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SmsTemplateRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SmsTemplateRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/NotificationResponseModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/sms/with-text": {
      "post": {
        "tags": [
          "Sms"
        ],
        "operationId": "ApiSmsWithTextPost",
        "parameters": [
          {
            "name": "Input-Request-Id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Input-Timestamp",
            "in": "header",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SmsTextRequestModel"
                  }
                ]
              }
            },
            "text/json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SmsTextRequestModel"
                  }
                ]
              }
            },
            "application/*+json": {
              "schema": {
                "allOf": [
                  {
                    "$ref": "#/components/schemas/SmsTextRequestModel"
                  }
                ]
              }
            }
          },
          "required": true
        },
        "responses": {
          "400": {
            "description": "Bad Request",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ValidationProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Server Error",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              }
            }
          },
          "200": {
            "description": "Success",
            "headers": {
              "Output-Request-Id": {
                "schema": {
                  "type": "string"
                }
              },
              "Output-Timestamp": {
                "schema": {
                  "type": "string",
                  "format": "date-time"
                }
              }
            },
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/NotificationResponseModel"
                }
              }
            }
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "EmailTemplateRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "recepientsEmailAddresses",
          "senderEmailAddress",
          "templateName"
        ],
        "properties": {
          "senderEmailAddress": {
            "type": "string",
            "format": "email"
          },
          "recepientsEmailAddresses": {
            "type": "array",
            "items": {
              "type": "string"
            }
          },
          "emailSubject": {
            "type": "string",
            "nullable": true
          },
          "ccEmailAddresses": {
            "type": "array",
            "nullable": true,
            "items": {
              "type": "string"
            }
          },
          "attachments": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/FileAttachment"
            }
          },
          "skipSending": {
            "type": "boolean"
          },
          "systemName": {
            "type": "string",
            "nullable": true
          },
          "username": {
            "type": "string",
            "nullable": true
          },
          "templateName": {
            "type": "string"
          },
          "ownerTemplateName": {
            "type": "string",
            "nullable": true
          },
          "parameters": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/StringStringKeyValue"
            }
          }
        }
      },
      "EmailTextRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "recepientsEmailAddresses",
          "senderEmailAddress",
          "text"
        ],
        "properties": {
          "senderEmailAddress": {
            "type": "string",
            "format": "email"
          },
          "recepientsEmailAddresses": {
            "type": "array",
            "items": {
              "type": "string"
            }
          },
          "emailSubject": {
            "type": "string",
            "nullable": true
          },
          "ccEmailAddresses": {
            "type": "array",
            "nullable": true,
            "items": {
              "type": "string"
            }
          },
          "attachments": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/FileAttachment"
            }
          },
          "skipSending": {
            "type": "boolean"
          },
          "systemName": {
            "type": "string",
            "nullable": true
          },
          "username": {
            "type": "string",
            "nullable": true
          },
          "text": {
            "type": "string"
          },
          "templateId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          }
        }
      },
      "FileAttachment": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "fileName": {
            "type": "string",
            "nullable": true
          },
          "content": {
            "type": "string",
            "format": "byte",
            "nullable": true
          }
        }
      },
      "NotificationResponseModel": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "status": {
            "type": "string",
            "nullable": true
          },
          "notificationHistoryId": {
            "type": "integer",
            "format": "int32"
          }
        }
      },
      "ProblemDetails": {
        "type": "object",
        "additionalProperties": {},
        "properties": {
          "type": {
            "type": "string",
            "nullable": true
          },
          "title": {
            "type": "string",
            "nullable": true
          },
          "status": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "detail": {
            "type": "string",
            "nullable": true
          },
          "instance": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "SmsTemplateRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "from",
          "templateName",
          "to"
        ],
        "properties": {
          "from": {
            "type": "string"
          },
          "to": {
            "type": "array",
            "items": {
              "type": "string"
            }
          },
          "skipSending": {
            "type": "boolean"
          },
          "username": {
            "type": "string",
            "nullable": true
          },
          "systemName": {
            "type": "string",
            "nullable": true
          },
          "templateName": {
            "type": "string"
          },
          "ownerTemplateName": {
            "type": "string",
            "nullable": true
          },
          "parameters": {
            "type": "array",
            "nullable": true,
            "items": {
              "$ref": "#/components/schemas/StringStringKeyValue"
            }
          }
        }
      },
      "SmsTextRequestModel": {
        "type": "object",
        "additionalProperties": false,
        "required": [
          "from",
          "text",
          "to"
        ],
        "properties": {
          "from": {
            "type": "string"
          },
          "to": {
            "type": "array",
            "items": {
              "type": "string"
            }
          },
          "skipSending": {
            "type": "boolean"
          },
          "username": {
            "type": "string",
            "nullable": true
          },
          "systemName": {
            "type": "string",
            "nullable": true
          },
          "text": {
            "type": "string"
          }
        }
      },
      "StringStringKeyValue": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "key": {
            "type": "string",
            "nullable": true
          },
          "value": {
            "type": "string",
            "nullable": true
          }
        }
      },
      "ValidationProblemDetails": {
        "type": "object",
        "additionalProperties": {},
        "properties": {
          "type": {
            "type": "string",
            "nullable": true
          },
          "title": {
            "type": "string",
            "nullable": true
          },
          "status": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "detail": {
            "type": "string",
            "nullable": true
          },
          "instance": {
            "type": "string",
            "nullable": true
          },
          "errors": {
            "type": "object",
            "nullable": true,
            "additionalProperties": {
              "type": "array",
              "items": {
                "type": "string"
              }
            }
          }
        }
      }
    }
  }
}