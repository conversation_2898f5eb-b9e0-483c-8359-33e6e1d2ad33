# BaseResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**message** | **string** |  | [optional] [default to undefined]
**success** | **boolean** |  | [optional] [default to undefined]

## Example

```typescript
import { BaseResponseModel } from './api';

const instance: BaseResponseModel = {
    message,
    success,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
