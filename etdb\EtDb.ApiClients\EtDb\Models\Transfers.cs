﻿using System;
using System.Collections.Generic;

namespace EtDb.ApiClients.EtDb.Models
{
    public partial class Transfers
    {
        public Transfers()
        {
            TransferListItems = new HashSet<TransferListItems>();
        }

        public int Id { get; set; }
        public string RequestNumber { get; set; }
        public DateTime DeliveryDate { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime? DeletedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public string ModifiedBy { get; set; }
        public bool IsCritical { get; set; }
        public int Status { get; set; }
        public int SchenkerId { get; set; }
        public bool IsAutomaticallyGenerated { get; set; }
        public bool IsWarehouseTransport { get; set; }
        public bool IsHidden { get; set; }
        public long? TransferNumSerCwtoMs { get; set; }
        public long? TransferNumSerMstoRs { get; set; }
        public long? TransferNumSerRstoOp { get; set; }
        public string BrdeliveryNumbers { get; set; }
        public string BrprojectDeliveryNumbers { get; set; }

        public virtual Schenkers Schenker { get; set; }
        public virtual ICollection<TransferListItems> TransferListItems { get; set; }
    }
}
