﻿namespace EtWS.Services.WarehousesService
{
    using EtWS.ApiClients.ETDB;

    public class WarehousesService : IWarehousesService
    {
        private readonly Lazy<IETDB> etDb;

        public WarehousesService(Lazy<IETDB> etDb)
        {
            this.etDb = etDb;
        }

        public async Task<IEnumerable<WarehousesResponseModel>> GetWarehousesSelectListAsync()
        {
            return await this.etDb.Value.ApiWarehousesSelectListGetAsync();
        }

        public async Task<IEnumerable<WarehousesResponseModel>> GetWarehousesByRegionAsync(int? region)
        {
            return await this.etDb.Value.ApiWarehousesByRegionGetAsync(region);
        }
    }
}
