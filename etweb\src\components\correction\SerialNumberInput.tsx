import React, { useEffect, useState, useCallback } from "react";
import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useValidateSerialNumber } from "../../data/query";
import { AxiosError } from "axios";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CheckCircle2, Loader2, XCircle } from "lucide-react";

interface SerialNumberInputProps {
  label: string;
  name: string;
  onValidationChange?: (isValid: boolean) => void;
}

const SerialNumberInput: React.FC<SerialNumberInputProps> = ({ label, name, onValidationChange }) => {
  const { t } = useTranslation();
  const { mutate, isPending: isValidating } = useValidateSerialNumber();
  const [isValid, setIsValid] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  const {
    register,
    watch,
    formState: { errors },
  } = useFormContext();

  const serialNumber = watch(name);

  // Local format validation
  const validateFormat = useCallback(
    (value: string) => {
      if (!value) return true;
      return /^[a-zA-Z0-9]+$/.test(value) || t("serialNumberError");
    },
    [t]
  );

  // API validation
  const validateWithApi = useCallback(
    (value: string) => {
      if (!value) return;

      mutate(value, {
        onSuccess: (valid) => {
          setIsValid(valid.success === true);
          setValidationError(null);
          onValidationChange?.(valid.success === true);
        },
        onError: (error) => {
          setIsValid(false);
          setValidationError((error as AxiosError<{ message: string }>)?.response?.data?.message || t("serialNumberError"));
          onValidationChange?.(false);
        },
      });
    },
    [mutate, onValidationChange, t]
  );

  // Run validation when serial number changes
  useEffect(() => {
    if (!serialNumber) {
      setIsValid(false);
      setValidationError(null);
      onValidationChange?.(false);
      return;
    }

    // If format is invalid, don't call API
    const formatResult = validateFormat(serialNumber);
    if (formatResult !== true) {
      setIsValid(false);
      setValidationError(formatResult);
      onValidationChange?.(false);
      return;
    }

    // If format is valid, validate with API
    validateWithApi(serialNumber);
  }, [serialNumber, validateFormat, validateWithApi, onValidationChange]);

  const errorMessage = errors[name]?.message?.toString() || validationError;

  return (
    <div className="space-y-2">
      <Label htmlFor={name}>{label}</Label>
      <Input
        type="text"
        id={name}
        autoComplete="off"
        {...register(name)}
        placeholder={t("enterSN")}
      />
      <div className="min-h-6 mt-1.5">
        {isValidating && (
          <div className="flex items-center text-sm text-muted-foreground">
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            {t("validating")}
          </div>
        )}
        {!isValidating && isValid && (
          <div className="flex items-center text-sm text-green-600">
            <CheckCircle2 className="mr-1.5 h-4 w-4" />
            {t("serialNumberValidated")}
          </div>
        )}
        {errorMessage && !isValidating && (
          <div className="flex items-center text-sm text-destructive">
            <XCircle className="mr-1.5 h-4 w-4" />
            {t(errorMessage)}
          </div>
        )}
      </div>
    </div>
  );
};

export default SerialNumberInput;
