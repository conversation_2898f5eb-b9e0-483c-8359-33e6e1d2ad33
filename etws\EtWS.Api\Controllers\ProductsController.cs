﻿namespace EtWS.Api.Controllers
{
    using EtWS.Api.Infrastructure.Utils;
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Services.ProductsService;
    using EtWS.Models.Responses.UserModels;

    public class ProductsController : BaseApiController
    {
        private readonly Lazy<IProductsService> productsService;

        public ProductsController(Lazy<IProductsService> productsService)
        {
            this.productsService = productsService;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(typeof(SearchProductDataResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchProductDataResponseModel>> GetAsync([Required, FromRoute] int id)
        {
            try
            {
                var product = await this.productsService.Value.GetProductByIdAsync(id);
                return this.Ok(product);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("search")]
        [ProducesResponseType(typeof(SearchResponseModel<SearchProductDataResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<SearchProductDataResponseModel>>> GetProductsAsync([Required] SearchDataRequestModel request)
        {
            try
            {
                var products = await this.productsService.Value.GetProductsAsync(request);
                return this.Ok(products);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("add")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> AddProductAsync([Required] ProductDataRequestModel request)
        {
            try
            {
                var newProductId = await this.productsService.Value.AddProductAsync(request);
                return this.Ok(newProductId);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPut("update")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateProductAsync([Required, FromBody] ProductDataRequestModel request)
        {
            try
            {
                await this.productsService.Value.UpdateProductAsync(request);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
