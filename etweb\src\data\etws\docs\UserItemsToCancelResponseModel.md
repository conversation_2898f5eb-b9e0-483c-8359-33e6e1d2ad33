# UserItemsToCancelResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**intDeliveryNum** | **string** |  | [optional] [default to undefined]
**equipmentName** | **string** |  | [optional] [default to undefined]
**toUserId** | **string** |  | [optional] [default to undefined]
**toUserFullName** | **string** |  | [optional] [default to undefined]
**insertDate** | **string** |  | [optional] [default to undefined]
**equipmentSerialNum** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { UserItemsToCancelResponseModel } from './api';

const instance: UserItemsToCancelResponseModel = {
    id,
    intDeliveryNum,
    equipmentName,
    toUserId,
    toUserFullName,
    insertDate,
    equipmentSerialNum,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
