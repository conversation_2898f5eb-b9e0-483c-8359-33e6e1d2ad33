﻿namespace EtWS.Api.Controllers
{
    using EtWS.Api.Infrastructure.Utils;
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CityModels;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Services.CitiesService;

    public class CitiesController : BaseApiController
    {
        private readonly Lazy<ICitiesService> citiesService;

        public CitiesController(Lazy<ICitiesService> citiesService)
        {
            this.citiesService = citiesService;
        }

        /// <summary>
        /// Gets city data by its id.
        /// </summary>
        /// <param name="id">The id of the city.</param>
        /// <returns>City data.</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(CityResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<CityResponseModel>> GetAsync([Required, FromRoute] int id)
        {
            try
            {
                var city = await this.citiesService.Value.GetCityAsync<CityResponseModel>(id);
                return this.Ok(city);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Returns the region of the city as string.
        /// </summary>
        /// <param name="id">The id of the city.</param>
        /// <returns>The region of the city.</returns>
        [HttpGet("region/{id}")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<string>> GetCityRegionByIdAsync([Required, FromRoute] int id)
        {
            try
            {
                var region = await this.citiesService.Value.GetCityRegionByIdAsync(id);
                return this.Ok(region);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Search cities based on criterias.
        /// </summary>
        /// <param name="request">The search parameters of the request.</param>
        /// <returns>List of cities, corresponding to the search criterias.</returns>
        [HttpPost("search")]
        [ProducesResponseType(typeof(SearchResponseModel<CityResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<CityResponseModel>>> GetCitiesAsync([Required] SearchDataRequestModel request)
        {
            try
            {
                var cities = await this.citiesService.Value.GetCitiesAsync(request);
                return this.Ok(cities);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("select-list")]
        [ProducesResponseType(typeof(IEnumerable<CitiesSelectItemResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<CitiesSelectItemResponseModel>>> GetCitiesSelectListAsync()
        {
            try
            {
                var cities = await this.citiesService.Value.GetCitiesSelectListAsync();
                return this.Ok(cities);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Adds city to the database.
        /// </summary>
        /// <param name="request">The city data to save in the database.</param>
        /// <returns>The id of the newly created city.</returns>
        [HttpPost("add")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> AddCityAsync([Required] CityRequestModel request)
        {
            try
            {
                var newCityId = await this.citiesService.Value.AddCityAsync(request);
                return this.Ok(newCityId);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Updates an existing city in the databse.
        /// </summary>
        /// <param name="id">The id of the city, being updated.</param>
        /// <param name="request">The new city data.</param>
        /// <returns>ActionResult.</returns>
        [HttpPut("update/{id}")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateCityAsync([Required, FromRoute] int id, [Required, FromBody] CityRequestModel request)
        {
            try
            {
                await this.citiesService.Value.UpdateCityAsync(id, request);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Blocks selected cities in the database.
        /// </summary>
        /// <param name="cityIds">The ids of the cities, that needs to be blocked.</param>
        /// <returns>ActionResult.</returns>
        [HttpPut("block")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> BlockCitiesAsync(List<int> cityIds)
        {
            try
            {
                await this.citiesService.Value.BlockCitiesAsync(cityIds);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Activates selected cities in the database.
        /// </summary>
        /// <param name="cityIds">The ids of the cities, that needs to be activated.</param>
        /// <returns>ActionResult.</returns>
        [HttpPut("activate")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> ActivateCitiesAsync(List<int> cityIds)
        {
            try
            {
                await this.citiesService.Value.ActivateCitiesAsync(cityIds);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        /// <summary>
        /// Gets a city by its id (request parameter) and returns the SAPCityCode and Cluster of the city.
        /// </summary>
        /// <param name="cityId">The id of the city.</param>
        /// <returns>SAPCityCode and Cluster.</returns>
        [HttpGet("sap-city-code-and-cluster/{cityId}")]
        [ProducesResponseType(typeof(SapCityCodesResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SapCityCodesResponseModel>> GetSapCityCodes(int cityId)
        {
            try
            {
                var response = await this.citiesService.Value.GetCityAsync<SapCityCodesResponseModel>(cityId);
                return this.Ok(response);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
