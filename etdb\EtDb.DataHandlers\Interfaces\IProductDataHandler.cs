﻿using EtDb.ApiClients.EtDb.Models;
using EtDb.DataHandlers.Models;

namespace EtDb.DataHandlers.Interfaces
{
    public interface IProductDataHandler
    {
        Task<FilteredDataModel<AdministrateProductDto>> GetFilteredProducts(string sortBy, string sortDir, int pageNumber, int pageSize, string query);

        Task<AdministrateProductDto> GetProductById(int id);

        Task<SapmatMapp> GetProductBySapMaterialNum(string sapMaterialNum);

        Task<bool> CheckForSameProduct(string sapMaterialNum, int equipmentTypeId = 0, int sapMatMappId = 0);

        Task<IQueryable<AdministrateProductDto>> GetAllProducts();

        Task<IQueryable<AdministrateProductDto>> GetProductsByType(IQueryable<EquipmentType> equipmentTypes);

        Task<int> InsertProduct(AdministrateProductDto productToInsert);

        Task UpdateProduct(AdministrateProductDto productToUpdate);


    }
}
