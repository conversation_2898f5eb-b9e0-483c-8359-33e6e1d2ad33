# ProductResponseModelSearchResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**count** | **number** |  | [optional] [default to undefined]
**dataCollection** | [**Array&lt;ProductResponseModel&gt;**](ProductResponseModel.md) |  | [optional] [default to undefined]

## Example

```typescript
import { ProductResponseModelSearchResponseModel } from './api';

const instance: ProductResponseModelSearchResponseModel = {
    count,
    dataCollection,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
