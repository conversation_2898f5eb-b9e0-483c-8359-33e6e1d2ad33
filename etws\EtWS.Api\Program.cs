﻿#pragma warning disable SA1200 // Using directives should be placed correctly
using System.Reflection;

using Autofac;
using Autofac.Extensions.DependencyInjection;
using EtWS.Api.Infrastructure.Extensions;
using Net.Common.Mvc.Extensions;
using Net.Metrics.Extentions;
using Net.Steeltoe;
using Serilog;
using Steeltoe.Discovery.Client;
using Steeltoe.Discovery.Eureka;
using Steeltoe.Extensions.Configuration.ConfigServer;

#pragma warning restore SA1200 // Using directives should be placed correctly

Log.Logger = WebServiceHelpers
    .CreateBootstrapLogger(Directory.GetCurrentDirectory(), args);

try
{
    var builder = CreateWebApplicationBuilder();

    var app = builder.Build();

    string applicationName = builder
        .Configuration
            .GetValue<string>(Constants.ApplicationNameKey);

    app.UseForwardedHeaders()
       .UsePathBase($"/{applicationName}")
       .UseWebService(builder.Configuration);

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Host terminated unexpectedly");

    throw;
}

WebApplicationBuilder CreateWebApplicationBuilder()
{
    var builder = WebApplication.CreateBuilder(args);

    builder.Host
        .UseServiceProviderFactory(new AutofacServiceProviderFactory())
        .ConfigureContainer<ContainerBuilder>((ctx, builder)
            => builder
                .RegisterAssemblyModules(Assembly.GetExecutingAssembly()))
        .UseSerilog(WebServiceHelpers.ConfigureSerilog)
        .ConfigureProprietaryMetrics(WebServiceHelpers.ConfigureMetricsWithDefaults)
        .AddConfigServer()
        .AddActuators()
        .AddResolvers()
        .AddServiceDiscovery(options => options.UseEureka())
        .RemoveEurekaServerHealthContributor();

    builder.Services
        .AddWebService(builder.Configuration);

    builder.WebHost
        .UseKestrel();

    return builder;
}
