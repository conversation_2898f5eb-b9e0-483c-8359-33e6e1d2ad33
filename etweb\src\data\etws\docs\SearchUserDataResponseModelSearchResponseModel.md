# SearchUserDataResponseModelSearchResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**count** | **number** |  | [optional] [default to undefined]
**dataCollection** | [**Array&lt;SearchUserDataResponseModel&gt;**](SearchUserDataResponseModel.md) |  | [optional] [default to undefined]

## Example

```typescript
import { SearchUserDataResponseModelSearchResponseModel } from './api';

const instance: SearchUserDataResponseModelSearchResponseModel = {
    count,
    dataCollection,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
