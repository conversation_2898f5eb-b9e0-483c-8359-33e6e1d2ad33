﻿namespace EtDb.DataHandlers
{
    using EtDb.ApiClients.EtDb.Context;
    using EtDb.ApiClients.EtDb.Enums;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using EtDb.Infrastructure.Enumerations;
    using Microsoft.EntityFrameworkCore;
    using Net.Ams.Models;

    public class UserDataHandler : BaseDataHandler, IUserDataHandler
    {
        public UserDataHandler(Lazy<EtDbContext> dbContext)
           : base(dbContext)
        {
        }

        public IQueryable<User> GetAll()
        {
            return this.dbContext.Value.Users;
        }

        public async Task<User?> GetUserByIdAsync(string id)
        {
            var user = await dbContext.Value.Users.Include(x => x.City).Include(x => x.Schenker).Where(x => x.Id == id).FirstOrDefaultAsync();

            return user;
        }

        public async Task<User?> GetUserByIdAsync(string id, params string[] includeProperties)
        {
            var query = this.dbContext.Value.Users.AsQueryable();

            query = includeProperties.Aggregate(query, (current, property) => current.Include(property));

            return await query.SingleOrDefaultAsync(u => u.Id == id);
        }

        public async Task<User?> GetUserByADAccountAsync(string adAccount)
        {
            var user = await this.GetAll().SingleOrDefaultAsync(u => u.Adaccount == adAccount);

            return user;
        }

        public async Task<User?> GetMolOfOpAsync(string opCode)
        {
            return await this.dbContext.Value.Users
                .SingleOrDefaultAsync(u => u.Opcode == opCode);
        }

        public IQueryable<User> GetActiveUsers()
        {
            return this.GetAll().Where(u => u.ActiveFl == 0);
        }

        public async Task AddUserAsync(ApplicationUserModel cacheUser)
        {
            var user = new User(cacheUser);
            await this.dbContext.Value.Users.AddAsync(user);
            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task AddUsersAsync(IList<ApplicationUserModel> usersToAdd)
        {
            if (usersToAdd == null || usersToAdd.Count == 0)
                return;

            foreach (var userModel in usersToAdd)
            {
                var user = new User(userModel);
                await this.dbContext.Value.Users.AddAsync(user);
            }

            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task<FilteredDataModel<User>> GetFilteredUsersAsync(string sortBy, string sortDir, int pageNumber, int pageSize, string query, IEnumerable<string> usersEln)
        {
            IQueryable<User> users = this.GetActiveUsers()
                .Include(x => x.City)
                .Include(x => x.Schenker)
                .Where(u => u.Eln != "99999");

            if (usersEln != null)
            {
                users = users.Where(u => usersEln.Contains(u.Eln));
            }

            FilteredDataModel<User> filteredData = await this.GetFilteredData(users, sortBy, sortDir, pageNumber, pageSize, query);

            return filteredData;
        }

        public async Task EditUsersStatusAsync(IEnumerable<string> selectedUsers, UserStatus newStatus, string currentUserId)
        {
            List<User> editedUsers = new List<User>();
            var users = this.GetAll();

            foreach (var userId in selectedUsers)
            {
                var selectedUser = await users.SingleOrDefaultAsync(u => u.Id == userId);

                if (selectedUser?.Opcode != null)
                {
                    throw new ArgumentException("Не се допуска блокиране на МОЛ на склад");
                }

                var updatedUser = this.ChangeUserStatus(selectedUser, newStatus, currentUserId, DateTime.Now);
                editedUsers.Add(updatedUser);
            }

            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task UpdateUserAsync(ApplicationUserModel cacheUser)
        {
            User? user = await this.GetUserByADAccountAsync(cacheUser.Username);

            if (user != null)
            {
                this.UpdateUserData(user, cacheUser);
                await this.dbContext.Value.SaveChangesAsync();
            }
            else
            {
                throw new InvalidOperationException($"User with ADAccount {cacheUser.Username} not found.");
            }
        }

        public async Task UpdateAllUsersAsync(IDictionary<string, ApplicationUserModel> cacheUsers)
        {
            var dbUsers = await this.GetAll().ToDictionaryAsync(u => u.Adaccount, StringComparer.InvariantCultureIgnoreCase);
            bool isUserUpdated = false;

            foreach (var cacheUser in cacheUsers)
            {
                var currentCacheUser = cacheUser.Value;

                if (!dbUsers.TryGetValue(cacheUser.Key, out User? currentDbUser) || !currentDbUser.Equals(currentCacheUser))
                {
                    currentDbUser = UpdateUserData(currentDbUser, currentCacheUser);
                    isUserUpdated = true;
                }
            }

            if (isUserUpdated)
            {
                await this.dbContext.Value.SaveChangesAsync();
            }
        }

        public async Task UpdateDbUsersWithCacheUserDataAsync(IEnumerable<User> users)
        {
            this.dbContext.Value.Users.UpdateRange(users);
            await this.dbContext.Value.SaveChangesAsync();
        }

        public IQueryable<User> GetUserMostFrequentTransfersTo(string userId)
        {
            return this.dbContext.Value.RecipientHistories
                .Where(r => r.FromUserId == userId)
                .OrderByDescending(r => r.Count)
                .Select(r => r.ToUser);
        }

        public async Task<Notification> UpdateUserNotificationAsync(string userId, NotificationType notificationType, string? text = null)
        {
            var user = await this.GetUserByIdAsync(userId);
            if (user == null)
            {
                throw new ArgumentException($"User with an Id: {userId} can not be found!");
            }

            return await this.UpdateUserNotificationAsync(user, notificationType, text);
        }

        public async Task RemoveUserNotificationAsync(string userId, NotificationType notificationType)
        {
            var user = await this.dbContext.Value.Users
                .Include(u => u.Notification)
                .SingleOrDefaultAsync(u => u.Id == userId);

            if (user == null)
            {
                throw new ArgumentException($"User with Id: {userId} cannot be found!");
            }

            var notificationEntry = user.Notification
                .SingleOrDefault(n => n.NotificationType == (int)notificationType);

            if (notificationEntry == null)
            {
                return;
            }

            notificationEntry.IsRead = true;

            this.dbContext.Value.Users.Update(user);
            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task RemoveUserNotificationAsync(string userId, int notificationId)
        {
            var user = await this.dbContext.Value.Users
                .Include(u => u.Notification)
                .SingleOrDefaultAsync(u => u.Id == userId);

            if (user == null)
            {
                throw new ArgumentException($"User with an Id: {userId} cannot be found!");
            }

            var notificationEntry = user.Notification.SingleOrDefault(n => n.Id == notificationId);
            if (notificationEntry == null)
            {
                return;
            }

            user.Notification.Remove(notificationEntry);
            this.dbContext.Value.Users.Update(user);
            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task<RecipientHistory?> GetRecipientHistoryAsync(string fromUserId, string toUserId)
        {
            return await this.dbContext.Value.RecipientHistories
                .SingleOrDefaultAsync(r => r.FromUserId == fromUserId && r.ToUserId == toUserId);
        }

        public async Task UpdateRecipientHistoryAsync(string fromUserId, string toUserId)
        {
            var recipientHistoryEntry = await this.dbContext.Value.RecipientHistories
                .SingleOrDefaultAsync(r => r.FromUserId == fromUserId && r.ToUserId == toUserId);

            if (recipientHistoryEntry == null)
            {
                recipientHistoryEntry = RecipientHistory.CreateEntry(fromUserId, toUserId);
                await this.dbContext.Value.RecipientHistories.AddAsync(recipientHistoryEntry);
            }
            else
            {
                recipientHistoryEntry.Count += 1;
                this.dbContext.Value.RecipientHistories.Update(recipientHistoryEntry);
            }
        }

        public async Task<FilteredDataModel<User>> GetFilteredUsersForEditAsync(string sortBy, string sortDir, int pageNumber, int pageSize, string query, IEnumerable<string> usersEln)
        {
            IQueryable<User> users = this.GetAllUsersForEdit(usersEln);

            FilteredDataModel<User> filteredData = await this.GetFilteredData(users, sortBy, sortDir, pageNumber, pageSize, query);

            return filteredData;
        }

        private IQueryable<User> GetAllUsersForEdit(IEnumerable<string> usersEln)
        {
            IQueryable<User> users = this.GetActiveUsers().Where(u => u.Eln != "99999");

            if (usersEln != null && usersEln.Any())
            {
                users = users.Where(u => usersEln.Contains(u.Eln));
            }

            return users;
        }

        public async Task<User> UpdateUserAsync(User user)
        {
            this.dbContext.Value.Users.Update(user);
            await this.dbContext.Value.SaveChangesAsync();
            return user;
        }

        private User ChangeUserStatus(User user, UserStatus newStatus, string blockedBy, DateTime? dateBlocked)
        {
            user.Blocked = (int)newStatus;
            user.BlockedByUserId = blockedBy;
            user.DataBlocked = dateBlocked;

            this.dbContext.Value.Users.Update(user);

            return user;
        }

        private User UpdateUserData(User? user, ApplicationUserModel cacheUser)
        {
            if (user == null)
            {
                user = new User(cacheUser);
                this.dbContext.Value.Users.Add(user);
            }
            else
            {
                user.UpdateEntry(cacheUser);
                this.dbContext.Value.Users.Update(user);
            }

            return user;
        }

        private async Task<Notification> UpdateUserNotificationAsync(User user, NotificationType notificationType, string? text = null)
        {
            Notification? notificationEntry = null;

            if (notificationType != NotificationType.AcceptanceOfTransfer)
            {
                notificationEntry = user.Notification.SingleOrDefault(n => n.NotificationType == (int)notificationType);
            }

            if (notificationEntry != null)
            {
                notificationEntry.IsRead = false;
                notificationEntry.Text = text;
                this.dbContext.Value.Users.Update(user);
            }
            else
            {
                notificationEntry = Notification.CreateEntry(notificationType, text);
                user.Notification.Add(notificationEntry);
            }

            await this.dbContext.Value.SaveChangesAsync();

            return notificationEntry;
        }
    }

}
