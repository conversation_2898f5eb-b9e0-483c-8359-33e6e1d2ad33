﻿namespace EtDb.Models.Responses.UserModels
{
    using EtDb.Infrastructure.Enumerations;

    public class UserResponseModel
    {
        public string Id { get; set; }

        public string Eln { get; set; }

        public string DisplayName { get; set; }

        public string IPTUName { get; set; }

        public string ADAccount { get; set; }

        public int? SchenkerId { get; set; }

        public UserStatus Blocked { get; set; }

        public DateTime? DataBlocked { get; set; }

        public string BlockedByUserId { get; set; }

        public bool HasPendingTransfer { get; set; }

        // public ICollection<NotificationResponseModel> Notifications { get; set; }
    }
}
