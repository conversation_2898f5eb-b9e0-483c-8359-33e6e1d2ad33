﻿namespace EtWS.Models.Responses.UserModels
{
    using EtWS.Infrastructure.Enumerations;

    public class SearchUserDataResponseModel
    {
        public string Id { get; set; }

        public string Eln { get; set; }

        public string FirstName { get; set; }

        public string Surname { get; set; }

        public string FamilyName { get; set; }

        public string FullName { get; set; }

        public string Op { get; set; }

        public int? RegionId { get; set; }

        public string Region { get; set; }

        public int CityId { get; set; }

        public string City { get; set; }

        public string ClientNumber { get; set; }

        public string PhoneNumber { get; set; }

        public string DisplayName { get; set; }

        public string IPTUName { get; set; }

        public string Position { get; set; }

        public string ADAccount { get; set; }

        public string Email { get; set; }

        public bool IsMolOfOp { get; set; }

        public string Opcode { get; set; }

        public int? SchenkerId { get; set; }

        public UserStatus Blocked { get; set; }

        public DateTime? DataBlocked { get; set; }

        public string BlockedByUserId { get; set; }

        public bool HasPendingTransfer { get; set; }
    }
}
