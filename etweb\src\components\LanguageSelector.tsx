import { useTranslation } from "react-i18next";
import { ChevronDown, Languages } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const LanguageSelector = () => {
  const { i18n } = useTranslation();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
    localStorage.setItem("i18nextLng", lng);
  };

  const currentLanguage = i18n.language === "en" ? "English" : "Български";

  return (
    <div className="mr-3 pl-5">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="inline-flex items-center justify-center gap-2"
          >
            <Languages className="h-4 w-4" />
            {currentLanguage}
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-32">
          <DropdownMenuItem
            onClick={() => changeLanguage("en")}
            className="cursor-pointer"
          >
            English
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => changeLanguage("bg")}
            className="cursor-pointer"
          >
            Български
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default LanguageSelector;
