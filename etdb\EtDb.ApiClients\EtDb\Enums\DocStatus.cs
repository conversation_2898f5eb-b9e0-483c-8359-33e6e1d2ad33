﻿namespace EtDb.ApiClients.EtDb.Enums
{
    using System.ComponentModel.DataAnnotations;

    public enum DocStatus
    {
        [Display(Name = "Начален")]
        Initial = 0,

        [Display(Name = "Резервиран")]
        Reserved = 1,

        [Display(Name = "Очаква потвърждение")]
        WaitingForConfirmation = 2,

        [Display(Name = "Приет")]
        Approved = 3,

        [Display(Name = "Анулиран от техник")]
        Cancelled = 4,

        [Display(Name = "Отказан трансфер")]
        Refused = 5,

        [Display(Name = "Върнат към Централен склад")]
        ReturnedToSAP = 6,

        [Display(Name = "Предоставен на клиент")]
        Lend = 7,

        [Display(Name = "Системно анулиран")]
        CancelledBySystem = 8,

        [Display(Name = "Анулиран при трансфер между складове")]
        CancelledByS2S = 9
    }
}
