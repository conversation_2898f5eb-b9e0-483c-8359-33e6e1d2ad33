# UserDataResponseModelSearchResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**count** | **number** |  | [optional] [default to undefined]
**dataCollection** | [**Array&lt;UserDataResponseModel&gt;**](UserDataResponseModel.md) |  | [optional] [default to undefined]

## Example

```typescript
import { UserDataResponseModelSearchResponseModel } from './api';

const instance: UserDataResponseModelSearchResponseModel = {
    count,
    dataCollection,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
