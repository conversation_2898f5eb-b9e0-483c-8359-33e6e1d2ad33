import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useTranslation } from "react-i18next";

interface LoadingStateProps {
  message?: string;
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function LoadingState({ 
  message, 
  size = "md",
  className 
}: LoadingStateProps) {
  const { t } = useTranslation();
  const defaultMessage = message || t("common.loading");
  
  const sizeClasses = {
    sm: "p-4",
    md: "p-8", 
    lg: "p-12"
  };

  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6"
  };

  return (
    <div className={cn("flex items-center justify-center", sizeClasses[size], className)}>
      <div className="flex items-center gap-3 text-muted-foreground">
        <Loader2 className={cn("animate-spin", iconSizes[size])} />
        <span className="text-sm">{defaultMessage}</span>
      </div>
    </div>
  );
}