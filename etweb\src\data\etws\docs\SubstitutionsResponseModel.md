# SubstitutionsResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**opCode** | **string** |  | [optional] [default to undefined]
**fromDate** | **string** |  | [optional] [default to undefined]
**toDate** | **string** |  | [optional] [default to undefined]
**forUserId** | **string** |  | [optional] [default to undefined]
**forUserFullName** | **string** |  | [optional] [default to undefined]
**substituteUserId** | **string** |  | [optional] [default to undefined]
**substituteUserFullName** | **string** |  | [optional] [default to undefined]
**forUserAdAccount** | **string** |  | [optional] [default to undefined]
**forSubstituteAdAccount** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { SubstitutionsResponseModel } from './api';

const instance: SubstitutionsResponseModel = {
    id,
    opCode,
    fromDate,
    toDate,
    forUserId,
    forUserFullName,
    substituteUserId,
    substituteUserFullName,
    forUserAdAccount,
    forSubstituteAdAccount,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
