﻿namespace EtWS.Services.IncorrectEquipments
{
    using System;
    using System.Text.Json;
    using System.Threading.Tasks;

    using EtWs.ApiClients.EsbWs;
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.HistoriyModels;
    using EtWS.Models.Responses.UserModels;
    using Microsoft.Extensions.Caching.Distributed;

    public class IncorrectEuipmentServices : IIncorrectEquipmentService
    {
        private readonly Lazy<IETDB> etdb;
        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<IEsbWs> esbWs;
        private readonly IDistributedCache cache;

        public IncorrectEuipmentServices(Lazy<IETDB> etdb, Lazy<IMapper> mapper, Lazy<IEsbWs> esbWs, IDistributedCache cache)
        {
            this.etdb = etdb;
            this.mapper = mapper;
            this.esbWs = esbWs;
            this.cache = cache;
        }

        public async Task<BaseResponseModel> TransferCorrectionAsync(TransferCorrectionRequest request)
        {
            var response = new BaseResponseModel { Success = true, Message = "Transfer correction processed successfully." };

            try
            {
                var itemId = await this.etdb.Value.ApiIncorrectEquipmentGetItemIdPostAsync(request.EquipmentSerialNum);

                await this.etdb.Value.ApiIncorrectEquipmentUpdateItemsAdditionDatesPutAsync(body: itemId);

                await this.AddHistoryAsync(itemId, request.TransferTo, request.NameOfTechnician, request.ServiceId, request.UserId, request.DeliveryShop);
                await this.AddStatusHistoryAsync(itemId);
            }
            catch (ApiClients.ETDB.ApiException<string> e)
            {
                response.Success = false;
                response.Message = $"Error: {e.Result}";
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"Unexpected error: {ex.Message}";
            }

            return response;
        }

        public async Task<BaseResponseModel> DeleteTransferByHistoryId(DeleteTransferRequest request)
        {
            var response = new BaseResponseModel { Success = true, Message = "Transfer deletion processed successfully." };

            if (request.Id.ToString() == null)
            {
                response.Success = false;
                response.Message = "The request cannot be null and must contain a valid EquipmentSerialNum.";
                return response;
            }

            await this.etdb.Value.ApiIncorrectEquipmentUpdateItemsAdditionDatesPutAsync(body: request.ItemId);

            try
            {
                var deleteErrorsResponse = await this.etdb.Value.ApiIncorrectEquipmentDeleteErrorsByHistoryByHistoryIdDeleteAsync(request.ItemId);
                if (deleteErrorsResponse == null || !deleteErrorsResponse.Success)
                {
                    response.Success = false;
                    response.Message = $"Failed to delete errors for history ID {request.Id}.";
                }
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"Error deleting errors for history ID {request.Id}: {ex.Message}";
            }

            try
            {
                var deleteStatusResponse = await this.etdb.Value.ApiIncorrectEquipmentDeleteStatusHistoriesByHistoryIdDeleteAsync(request.Id);
                if (deleteStatusResponse == null || !deleteStatusResponse.Success)
                {
                    response.Success = false;
                    response.Message = $"Failed to delete status for history ID {request.Id}.";
                }
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"Error deleting status for history ID {request.Id}: {ex.Message}";
            }

            try
            {
                var updateAvailableResponse = await this.etdb.Value.ApiIncorrectEquipmentUpdateAvailableEquipmentToZeroByItemIdPutAsync(request.ItemId);
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"Error updating available equipment for item ID {request.ItemId}: {ex.Message}";
            }

            try
            {
                var deleteHistoryResponse = await this.etdb.Value.ApiIncorrectEquipmentDeleteHistoryByIdDeleteAsync(request.Id);
                if (deleteHistoryResponse != null && deleteHistoryResponse.Success)
                {
                    response.Success = true;
                    response.Message = "Transfer deletion processed successfully.";
                }
                else
                {
                    response.Success = false;
                    response.Message = $"Failed to delete history for history ID {request.Id}.";
                    return response;
                }
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"Error deleting history for history ID {request.Id}: {ex.Message}";
                return response;
            }

            return response;
        }

        public async Task<IEnumerable<UsersWithOPCodeResponse>> GetUsersWithOPCodesAsync()
        {
            const string cacheKey = "users_with_opcodes";
            IEnumerable<UsersWithOPCodeResponse> result;

            try
            {
                var cachedData = await this.cache.GetStringAsync(cacheKey);

                if (!string.IsNullOrEmpty(cachedData))
                {
                    result = JsonSerializer.Deserialize<IEnumerable<UsersWithOPCodeResponse>>(cachedData);
                }
                else
                {
                    var dbResult = await this.etdb.Value.ApiIncorrectEquipmentGetAllUsersWithOpcodeGetAsync();
                    result = this.mapper.Value.Map<IEnumerable<UsersWithOPCodeResponse>>(dbResult);

                    var serializedData = JsonSerializer.Serialize(result);

                    await this.cache.SetStringAsync(cacheKey, serializedData, new DistributedCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(10),
                    });
                }
            }
            catch (Exception)
            {
                var fallbackResult = await this.etdb.Value.ApiIncorrectEquipmentGetAllUsersWithOpcodeGetAsync();
                result = this.mapper.Value.Map<IEnumerable<UsersWithOPCodeResponse>>(fallbackResult);
            }

            return result;
        }

        public async Task<BaseResponseModel> DoesSerialNumberExistAsync(string equipmentSerialNum)
        {
            var responseModel = new BaseResponseModel { Success = true, Message = "Equipment serial number exists." };

            try
            {
                var result = await this.etdb.Value.ApiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGetAsync(equipmentSerialNum);

                if (result.Success == true)
                {
                    responseModel = this.mapper.Value.Map<BaseResponseModel>(result);
                }
                else
                {
                    responseModel.Success = false;
                    responseModel.Message = "Equipment serial number does not exist.";
                }
            }
            catch (Exception ex)
            {
                responseModel.Success = false;
                responseModel.Message = $"Error checking serial number: {ex.Message}";
            }

            return responseModel;
        }

        public async Task<ICollection<UserDisplayNameResponseModel>> GetUserDisplayName(string namePrefix)
        {
            var cacheKey = "user_display_names";
            var displayNames = new List<UserDisplayNameResponseModel>();

            try
            {
                var cachedData = await this.cache.GetStringAsync(cacheKey);

                if (string.IsNullOrEmpty(cachedData))
                {
                    var result = await this.etdb.Value.ApiIncorrectEquipmentUserAndIptuNamesGetAsync();

                    if (result != null && result.Any())
                    {
                        displayNames = this.mapper.Value.Map<List<UserDisplayNameResponseModel>>(result);

                        var serializedData = JsonSerializer.Serialize(displayNames);

                        if (!string.IsNullOrWhiteSpace(namePrefix))
                        {
                            displayNames = displayNames
                                .Where(u => u.DisplayName.StartsWith(namePrefix, StringComparison.OrdinalIgnoreCase))
                                .Take(10)
                                .ToList();
                        }

                        await this.cache.SetStringAsync(cacheKey, serializedData, new DistributedCacheEntryOptions
                        {
                            AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(30),
                        });
                    }
                }
                else
                {
                    displayNames = JsonSerializer.Deserialize<List<UserDisplayNameResponseModel>>(cachedData);

                    if (!string.IsNullOrWhiteSpace(namePrefix))
                    {
                        displayNames = displayNames
                            .Where(u => u.DisplayName.StartsWith(namePrefix, StringComparison.OrdinalIgnoreCase))
                            .Take(10)
                            .ToList();
                    }
                }
            }
            catch (Exception)
            {
                // Continue
            }

            return displayNames;
        }

        public async Task<BaseResponseModel> CheckServiceId(string serviceId)
        {
            var responseModel = new BaseResponseModel { Success = true, Message = "Service Id exists." };

            try
            {
                var result = await this.esbWs.Value.ApiWs448RetrieveCustomerAssetRootAssetIdByServiceIdGetAsync(serviceId);

                if (string.IsNullOrWhiteSpace(result))
                {
                    responseModel.Success = false;
                    responseModel.Message = "Service Id does not exist.";
                }
            }
            catch (Exception ex)
            {
                responseModel.Success = false;
                responseModel.Message = $"Service Id does not exist. {ex.Message}";
            }

            return responseModel;
        }

        public async Task<IEnumerable<HistoriesResponse>> GetAllHistoriesByEquipmentSerialNumber(string equipmentSerialnumber)
        {
            try
            {
                var itemId = await this.etdb.Value.ApiIncorrectEquipmentGetItemIdPostAsync(equipmentSerialnumber);

                var histories = await this.etdb.Value.ApiIncorrectEquipmentGetHistoriesByItemIdByItemIdGetAsync(itemId);

                var result = histories.Select(h => new HistoriesResponse
                {
                    Id = h.Id,
                    InsertDate = h.InsertDate?.ToString("yyyy-MM-dd HH:mm") ?? "N/A",
                    ItemId = itemId,
                });

                return result;
            }
            catch (Exception ex)
            {
                return new List<HistoriesResponse>
                {
                };
            }
        }

        private async Task<BaseResponseModel> AddHistoryAsync(int itemId, string transferTo, string nameOfTechnician, string serviceIdTo, string userId, string deliveryShop)
        {
            var response = new BaseResponseModel { Success = true, Message = "Status history added successfully." };

            try
            {
                var histories = await this.etdb.Value.ApiIncorrectEquipmentGetHistoriesByItemIdByItemIdGetAsync(itemId);

                var initialHistory = histories.FirstOrDefault();
                var previousHistory = histories.Skip(1).FirstOrDefault();

                var historyRequest = new HistoryRequest()
                {
                    ItemId = itemId,
                    ItemTypeOfUsage = initialHistory.ItemTypeOfUsage,
                    CrmorderId = initialHistory.CrmorderId,
                    FromUserId = initialHistory.ToUserId,
                    IcmIdSgwId = initialHistory.IcmIdSgwId,
                    ItemValidity = initialHistory.ItemValidity,
                    DeliveryItemQty = initialHistory.DeliveryItemQty,
                    SourceSystem = GlobalConstants.SourceSystem,
                    AutoGenerated = GlobalConstants.AutoGeneratedValue,
                };

                var historyWithOp = histories.FirstOrDefault(h => h.DeliveryShop != null && h.DeliveryShop.StartsWith("OP"));

                switch (transferTo)
                {
                    case GlobalConstants.CentralWarehouse:

                        historyRequest.DeliveryType = GlobalConstants.CentralWarehouseDeliveryType;
                        historyRequest.DocStatus = GlobalConstants.CentralWarehouseDocStatus;
                        historyRequest.ToUserId = GlobalConstants.CentralWarehouseToUserId;
                        historyRequest.OperationType = GlobalConstants.CentralWarehouseOperationType;

                        break;

                    case GlobalConstants.Technician:

                        var lastUserId = !string.IsNullOrEmpty(initialHistory.ToUserId)
                            ? initialHistory.ToUserId
                            : initialHistory?.FromUserId;

                        if (lastUserId == null || lastUserId == GlobalConstants.CentralWarehouseToUserId)
                        {
                            lastUserId = !string.IsNullOrEmpty(initialHistory.FromUserId)
                            ? initialHistory.FromUserId
                            : previousHistory?.ToUserId;
                        }

                        var userRequest = new UserRequest()
                        {
                            UserId = lastUserId,
                            ТechnicianName = nameOfTechnician,
                        };

                        var userRequestDto = this.mapper.Value.Map<UserRequestModel>(userRequest);

                        var technicianId = await this.etdb.Value.ApiIncorrectEquipmentGetTechnicianIdPostAsync(body: userRequestDto);

                        historyRequest.DeliveryType = GlobalConstants.TechniciaDeliveryType;
                        historyRequest.DocStatus = GlobalConstants.TechniciaDocStatus;
                        historyRequest.ToUserId = technicianId;
                        historyRequest.OperationType = GlobalConstants.TechniciaOperationType;
                        historyRequest.DeliveryShop = historyWithOp.DeliveryShop;

                        break;

                    case GlobalConstants.SAPVirtual:

                        historyRequest.DeliveryType = GlobalConstants.SAPVirtalDeliveryType;
                        historyRequest.DocStatus = GlobalConstants.SAPVirtalDocStatus;
                        historyRequest.ToUserId = GlobalConstants.CentralWarehouseToUserId;
                        historyRequest.OperationType = GlobalConstants.SAPVirtalOperationType;

                        break;

                    case GlobalConstants.Client:

                        historyRequest.DeliveryType = GlobalConstants.ClientDeliveryType;
                        historyRequest.DocStatus = GlobalConstants.ClientDocStatus;
                        historyRequest.OperationType = GlobalConstants.ClientOperationType;
                        historyRequest.ServiceIdTo = serviceIdTo;

                        break;

                    case GlobalConstants.OP:

                        historyRequest.DeliveryType = GlobalConstants.TechniciaDeliveryType;
                        historyRequest.DocStatus = GlobalConstants.TechniciaDocStatus;
                        historyRequest.OperationType = GlobalConstants.TechniciaOperationType;
                        historyRequest.ToUserId = userId;

                        break;
                }

                var historyDto = this.mapper.Value.Map<HistoryRequestModel>(historyRequest);

                await this.etdb.Value.ApiIncorrectEquipmentAddHistoryPostAsync(body: historyDto);

                histories = await this.etdb.Value.ApiIncorrectEquipmentGetHistoriesByItemIdByItemIdGetAsync(itemId);

                initialHistory = histories.FirstOrDefault();
                previousHistory = histories.Skip(1).FirstOrDefault();

                if (previousHistory.ToUserId != null)
                {
                    if (previousHistory.ToUserId != GlobalConstants.CentralWarehouseToUserId)
                    {
                        await this.etdb.Value.ApiIncorrectEquipmentUpdateAvailableEquipmentToZeroByItemIdPutAsync(itemId);
                    }
                }

                if (transferTo == GlobalConstants.Technician || transferTo == GlobalConstants.OP)
                {
                    var availableEquipmentRequest = new AvailableEqipmentRequest
                    {
                        ItemId = itemId,
                        UserId = initialHistory.ToUserId,
                    };

                    var availableEquipmentDto = this.mapper.Value.Map<AvailableEquipmentRequestModel>(availableEquipmentRequest);

                    int equipmentId = 0;
                    try
                    {
                        equipmentId = await this.etdb.Value.ApiIncorrectEquipmentGetAvailableEquipmentIdPostAsync(body: availableEquipmentDto);
                    }
                    catch (ApiClients.ETDB.ApiException<string> e)
                    {
                        return new BaseResponseModel { Success = false, Message = $"Error while fetching available equipment ID: {e.Result}" };
                    }

                    if (equipmentId == 0)
                    {
                        await this.etdb.Value.ApiIncorrectEquipmentAddAvailableEquipmentPostAsync(body: availableEquipmentDto);
                    }
                    else
                    {
                        await this.UpdateAvailableEquipmentQuantiry(itemId, initialHistory.ToUserId);
                    }
                }
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"Error adding history: {ex.Message}";
            }

            return response;
        }

        private async Task<BaseResponseModel> AddStatusHistoryAsync(int itemId)
        {
            var response = new BaseResponseModel { Success = true, Message = "Status history added successfully." };

            try
            {
                var histories = await this.etdb.Value.ApiIncorrectEquipmentGetHistoriesByItemIdByItemIdGetAsync(itemId);
                var firstHistory = histories.FirstOrDefault();
                var previousHistory = histories.Skip(1).FirstOrDefault();

                var statusHistoryRequest = new StatusHistoryRequest
                {
                    HistoryId = firstHistory.Id,
                    SourceSystem = GlobalConstants.SourceSystem,
                    DocStatusNew = firstHistory.DocStatus,
                    DocStatusOld = previousHistory.DocStatus,
                };

                var statusHistoryRequestDto = this.mapper.Value.Map<StatusHistoryRequestModel>(statusHistoryRequest);
                await this.etdb.Value.ApiIncorrectEquipmentAddStatusHistoryPostAsync(body: statusHistoryRequestDto);
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"Error adding status history: {ex.Message}";
            }

            return response;
        }

        private async Task<BaseResponseModel> UpdateAvailableEquipmentQuantiry(int itemId, string userId)
        {
            var response = new BaseResponseModel { Success = true, Message = "Available equipment quantity updated successfully." };

            try
            {
                var availableEquipmentRequest = new AvailableEqipmentRequest
                {
                    ItemId = itemId,
                    UserId = userId,
                };

                var availableEquipmentDto = this.mapper.Value.Map<AvailableEquipmentRequestModel>(availableEquipmentRequest);
                await this.etdb.Value.ApiIncorrectEquipmentUpdateAvailableEquipmentQuantityPutAsync(body: availableEquipmentDto);
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"Error updating available equipment quantity: {ex.Message}";
            }

            return response;
        }
    }
}
