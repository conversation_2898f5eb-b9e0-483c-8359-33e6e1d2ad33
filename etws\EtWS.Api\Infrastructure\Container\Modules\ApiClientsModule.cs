﻿namespace EtWS.Api.Infrastructure.Container.Modules
{
    using Autofac;

    public class ApiClientsModule : Module
    {
        protected override void Load(ContainerBuilder builder)
        {
#pragma warning disable S125 // Sections of code should not be commented out
            /*
                    builder
                        .RegisterApiWithDefaultSettings<MrcCalculator, IMrcCalculator>(nameof(MrcCalculator));
                    */
        }
#pragma warning restore S125 // Sections of code should not be commented out
    }
}
