﻿namespace EtDb.Services.Interfaces
{
    using EtDb.Models.Requests;

    public interface IAuxApiService : IService
    {
        Task<int> InsertItemAsync(InsertItemRequestModel request);

        Task UpdateItemAsync(int equipmentId, UpdateItemRequestModel request);

        Task<int> InsertHistoryAsync(InsertHistoryRequestModel request);

        Task UpdateHistoryAsync(int equipmentId);

        Task<int> InsertAvailableEquipmentAsync(InsertAvailableEquipmentRequestModel request);

        Task UpdateAvailableEquipmentAsync(int equipmentId, UpdateAvailableEquipmentRequestModel request);

        Task InsertStatusHistoryAsync(int equipmentId);

        Task<int> InsertStatusHistoryAsync(InsertStatusHistoryRequestModel request);

        Task<int> InsertNotificationAsync(string userId);

        Task UpdateNotificationAsync(string userId);
    }
}
