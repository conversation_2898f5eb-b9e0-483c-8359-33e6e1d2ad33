import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useGetDatesBySerialNumberQuery } from "../../data/query";
import { useEffect } from "react";
import { Label } from "@/components/ui/label";
import { XCircle } from "lucide-react";

const Remove = () => {
  const { t } = useTranslation();
  const {
    register,
    watch,
    setValue,
    formState: { errors },
  } = useFormContext();

  const equipmentSerialNum = watch("equipmentSerialNum");
  const { data: dates, isLoading, error } = useGetDatesBySerialNumberQuery(equipmentSerialNum);
  const hasError = !!errors.removalDate;
  const errorMessage = errors.removalDate?.message?.toString();
  const deleteId = watch("deleteId");
  const selectedOption = dates?.find((option) => option.id === Number(deleteId));

  useEffect(() => {
    if (selectedOption?.itemId) {
      setValue("deleteItemId", selectedOption.itemId);
    }
  }, [selectedOption, setValue]);
  return (
    <div className="space-y-2">
      <Label htmlFor="removalDate">{t("selectRemovalDate")}</Label>
      <div>
        {isLoading ? (
          <div className="flex items-center space-x-2 py-3">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent"></div>
            <span className="text-sm text-gray-500">{t("loading")}</span>
          </div>        ) : error ? (
          <div className="flex items-center rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-sm text-destructive">
            <XCircle className="mr-2 h-5 w-5" />
            <span>{(() => {
              const anyError = error as { response?: { data?: { message?: string } } };
              return anyError.response?.data?.message || error.message || t("errorOccurred");
            })()}</span>
          </div>
        ) : (
          <select
            id="deleteId"
            {...register("deleteId")}
            className="w-full rounded-lg border px-4 py-3 text-sm outline-none transition-all focus:ring-2"
          >
            <option value="">{t("pleaseSelectADate")}</option>
            {dates ?
              dates.map((date, index) => (
                <option key={index} value={date.id}>
                  {date.insertDate}
                </option>
              )) : "No dates available"}
          </select>
        )}
        {hasError && !isLoading && !error && (
          <div className="mt-1.5 flex items-center text-sm text-destructive">
            <XCircle className="mr-1.5 h-4 w-4" />
            {t(errorMessage || "")}
          </div>
        )}
      </div>
    </div>
  );
};

export default Remove;
