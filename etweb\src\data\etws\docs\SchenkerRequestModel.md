# SchenkerRequestModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**opCode** | **string** |  | [default to undefined]
**transportArea** | **number** |  | [optional] [default to undefined]
**processingTime** | **number** |  | [optional] [default to undefined]
**protectiveTime** | **number** |  | [optional] [default to undefined]
**cityId** | **number** |  | [optional] [default to undefined]
**address** | **string** |  | [optional] [default to undefined]
**localWarehouseId** | **number** |  | [optional] [default to undefined]

## Example

```typescript
import { SchenkerRequestModel } from './api';

const instance: SchenkerRequestModel = {
    id,
    opCode,
    transportArea,
    processingTime,
    protectiveTime,
    cityId,
    address,
    localWarehouseId,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
