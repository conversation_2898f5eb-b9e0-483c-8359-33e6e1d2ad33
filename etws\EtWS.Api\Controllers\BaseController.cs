﻿namespace EtWS.Api.Controllers
{
    using System.Security.Claims;

    using Microsoft.AspNetCore.Mvc;

    [Route("api/[controller]")]
    [ApiController]
    [ProducesResponseType(typeof(ValidationProblemDetails), 400)]
    [ProducesResponseType(typeof(ProblemDetails), 500)]
    public class BaseController : BaseApiController
    {
        public BaseController()
        {
        }

        protected string GetUserId()
        {
            return this.User.Claims.FirstOrDefault(x => x.Type == ClaimConstants.UserIdClaimType)?.Value;
        }

        protected IEnumerable<string> GetUserRoles()
        {
            return this.User.Claims.Where(c => c.Type == ClaimTypes.Role).Select(c => c.Value).ToList();
        }
    }
}
