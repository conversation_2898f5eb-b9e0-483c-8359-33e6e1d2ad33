# UserDataConciseResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **string** |  | [optional] [default to undefined]
**eln** | **string** |  | [optional] [default to undefined]
**fullName** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { UserDataConciseResponseModel } from './api';

const instance: UserDataConciseResponseModel = {
    id,
    eln,
    fullName,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
