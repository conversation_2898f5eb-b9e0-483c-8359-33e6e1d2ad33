﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <LangVersion>Latest</LangVersion>
    <ImplicitUsings>enable</ImplicitUsings>
    <CodeAnalysisRuleSet>../Rules.ruleset</CodeAnalysisRuleSet>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <AspNetCoreHostingModel>OutOfProcess</AspNetCoreHostingModel>
    <StartupObject></StartupObject>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors></WarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors></WarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Infrastructure\Container\Interceptors\**" />
    <Content Remove="Infrastructure\Container\Interceptors\**" />
    <EmbeddedResource Remove="Infrastructure\Container\Interceptors\**" />
    <None Remove="Infrastructure\Container\Interceptors\**" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="System.ComponentModel.DataAnnotations" />
    <Using Include="Microsoft.AspNetCore.Mvc" />
    <Using Include="AutoMapper" />
    <Using Include="Net.Common" />
    <Using Include="Net.Common.Mvc" />
    <Using Include="EtWS.Infrastructure.Constants" />
    <Using Include="EtWS.Infrastructure.Constants.ServicesConstants" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.Redis" Version="6.0.1" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="7.2.0" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
    <PackageReference Include="Net.Ams" Version="0.2.20" />
    <PackageReference Include="Net.Autorest" Version="0.2.20" />
    <PackageReference Include="Net.Common.Mvc" Version="0.2.20" />
    <PackageReference Include="Net.Middlewares" Version="0.2.20" />
    <PackageReference Include="Net.Steeltoe" Version="0.2.20" />
    <PackageReference Include="Net.Swashbuckle" Version="0.2.20" />
    <PackageReference Include="Net.HealthChecks" Version="0.2.20" />
    <PackageReference Include="Net.Tracing" Version="0.2.20" />
	<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="6.0.25" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="6.0.2" />
    <PackageReference Include="Steeltoe.Connector.ConnectorCore" Version="3.1.3" />
    <PackageReference Include="Steeltoe.Extensions.Configuration.ConfigServerCore" Version="3.1.3" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
    <PackageReference Include="SonarAnalyzer.CSharp" Version="8.35.0.42613">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EtWS.Services\EtWS.Services.csproj" />
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties properties_4launchsettings_1json__JsonSchema="http://json.schemastore.org/lsdlschema" /></VisualStudio></ProjectExtensions>

</Project>
