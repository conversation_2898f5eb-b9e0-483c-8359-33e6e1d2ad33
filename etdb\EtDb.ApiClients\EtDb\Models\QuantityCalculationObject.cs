﻿using System;
using System.Collections.Generic;

namespace EtDb.ApiClients.EtDb.Models
{
    public partial class QuantityCalculationObject
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public int CriticalQuantity { get; set; }
        public bool IsGroupWithSubstitutes { get; set; }
        public bool IsManuallySet { get; set; }
        public int? ManualSetDaysValid { get; set; }
        public DateTime LastUpdateDate { get; set; }
        public int? DeliveryTimeInDays { get; set; }
        public int Opid { get; set; }
        public int? EquipmentGroupId { get; set; }
        public int? EquipmentTypeId { get; set; }

        public virtual EquipmentGroups EquipmentGroup { get; set; }
        public virtual EquipmentType EquipmentType { get; set; }
        public virtual Schenkers Op { get; set; }
    }
}
