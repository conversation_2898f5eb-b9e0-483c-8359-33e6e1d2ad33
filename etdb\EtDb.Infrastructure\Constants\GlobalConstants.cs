﻿namespace EtDb.Infrastructure.Constants
{
    using System.Collections.Immutable;

    public static class GlobalConstants
    {
        public const string Endpoints = "Endpoints";

        public const string ConnectionStringsConfigKey = "connectionStrings";

        public const int BatchSize = 1000;

        public const string IPTUCustomNames = "N3,<PERSON><PERSON><PERSON><PERSON>,Велико Търново, <PERSON><PERSON><PERSON><PERSON><PERSON>, Га<PERSON>рово, Д<PERSON><PERSON><PERSON><PERSON><PERSON>, Провадия, София 2 - З<PERSON><PERSON><PERSON><PERSON><PERSON>,София 3 - Младост,Търговище,Шумен,Благоевград,Бурга<PERSON>,<PERSON>юб<PERSON>м<PERSON><PERSON>,Паз<PERSON>рджик,Пловдив,Сливе<PERSON>,Смо<PERSON>ян,Стара Загора";

        public const string SourceSystemSGW = "SGW";

        public const int DocStatusProvidedToClient = 7;

        public const string Error400Title = "One or more validation errors occurred.";

        public const string Error500Title = "One or more errors occurred.";

        public const string IptuName = "ИПТУ";

        public static readonly ImmutableList<int> DocStatusesList = new List<int>() { 1, 2 }.ToImmutableList();
    }
}
