﻿namespace EtWS.Services.CitiesService
{
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CityModels;
    using EtWS.Models.Responses.CommonModels;

    public interface ICitiesService : IService
    {
        Task<T> GetCityAsync<T>(int id);

        Task<SearchResponseModel<CityResponseModel>> GetCitiesAsync(SearchDataRequestModel request);

        Task<IEnumerable<CitiesSelectItemResponseModel>> GetCitiesSelectListAsync();

        Task<int> AddCityAsync(CityRequestModel request);

        Task UpdateCityAsync(int id, CityRequestModel request);

        Task BlockCitiesAsync(List<int> cityIds);

        Task ActivateCitiesAsync(List<int> cityIds);

        Task<string> GetCityRegionByIdAsync(int cityId);
    }
}
