﻿namespace EtDb.Services
{
    using System.Linq.Expressions;

    using EtDb.ApiClients.EtDb.Enums;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using EtDb.Infrastructure.Enumerations;
    using EtDb.Models.Requests;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.UserModels;
    using EtDb.Services.Interfaces;

    public class UserManagerService : IUserManagerService
    {
        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<IUserDataHandler> userDataHandler;
        private readonly Lazy<ISchenkerDataHandler> schenkerDataHandler;
        private readonly Expression<Func<User, object>> defaultOrderBy = u => u.Iptuname;
        private readonly Expression<Func<User, object>> defaultThenBy = u => u.DisplayName;

        public UserManagerService(Lazy<IMapper> mapper, Lazy<IUserDataHandler> userDataHand<PERSON>, Lazy<ISchenkerDataHandler> schenkerDataHandler)
        {
            this.mapper = mapper;
            this.userDataHandler = userDataHandler;
            this.schenkerDataHandler = schenkerDataHandler;
        }

        public async Task<SearchUserResponseModel> GetUserByIdAsync(string userId)
        {
            var user = await this.userDataHandler.Value.GetUserByIdAsync(userId);

            if (user == null)
            {
                throw new KeyNotFoundException("No user found for the provided user id.");
            }

            return this.mapper.Value.Map<SearchUserResponseModel>(user);
        }

        public async Task<string> GetUserAdAccountAsync(string userId)
        {
            var user = await this.userDataHandler.Value.GetUserByIdAsync(userId);

            if (user == null)
            {
                throw new KeyNotFoundException("No user found for the provided user id.");
            }

            return user.Adaccount;
        }

        public async Task<SearchUserResponseModel> GetUserByUsernameAsync(string username)
        {
            var user = await this.userDataHandler.Value.GetUserByADAccountAsync(username);

            if (user == null)
            {
                throw new KeyNotFoundException("No user found for the provided username.");
            }

            return this.mapper.Value.Map<SearchUserResponseModel>(user);
        }

        public IEnumerable<UserResponseModel> GetUsersByUserElnsCollection(IEnumerable<string> elns)
        {
            Expression<Func<User, bool>> filter = u => elns.Contains(u.Eln);
            return this.GetUsersWithFilter<UserResponseModel>(filter, this.defaultOrderBy, this.defaultThenBy);
        }

        public IEnumerable<UserResponseModel> FilterUsersByIptuAndSchenker(IEnumerable<string> customIptuNames, int? userSchenkerId)
        {
            Expression<Func<User, bool>> filter = u => u.Iptuname.StartsWith(GlobalConstants.IptuName)
                    || customIptuNames.Contains(u.Iptuname.Trim())
                    || u.SchenkerId == userSchenkerId;
            return this.GetUsersWithFilter<UserResponseModel>(filter, this.defaultOrderBy, this.defaultThenBy);
        }

        public async Task<bool> IsUserMolAsync(string userId)
        {
            return await this.schenkerDataHandler.Value.IsUserMol(userId);
        }

        public IEnumerable<UserConciseResponseModel> GetAllMols()
        {
            Expression<Func<User, bool>> filter = u => u.Opcode != null;

            return this.GetUsersWithFilter<UserConciseResponseModel>(filter);
        }

        public IEnumerable<UserResponseModel> GetUserMostFrequentTransfersTo(string fromUserId)
        {
            var toUsers = this.userDataHandler.Value.GetUserMostFrequentTransfersTo(fromUserId).ToList();
            return this.mapper.Value.Map<List<UserResponseModel>>(toUsers);
        }

        public async Task<SearchResponseModel<SearchUserResponseModel>> SearchUsers(SearchUsersRequestModel request)
        {
            var data = await this.userDataHandler.Value.GetFilteredUsersAsync(
                request.SortBy, request.SortDir, request.PageNumber, request.PageSize, request.Query, request.FilteredUsersEln);

            return this.mapper.Value.Map<FilteredDataModel<User>, SearchResponseModel<SearchUserResponseModel>>(data);
        }

        public async Task UpdateUserAsync(EditUsersRequestModel request)
        {
            if (request.SchenkerId == 0)
            {
                throw new ArgumentException("Please provide a valid schenker id.");
            }

            var user = await this.userDataHandler.Value.GetUserByIdAsync(request.Id);
            if (user == null)
            {
                throw new KeyNotFoundException("No user found for the provided user id.");
            }

            await this.ModifyExistingUserAsync(request, user);
        }

        public async Task ActivateSelectedUsersAsync(IEnumerable<string> selectedUsers, string userId)
        {
            await this.userDataHandler.Value.EditUsersStatusAsync(selectedUsers, UserStatus.Active, userId);
        }

        public async Task BlockSelectedUsersAsync(IEnumerable<string> selectedUsers, string userId)
        {
            await this.userDataHandler.Value.EditUsersStatusAsync(selectedUsers, UserStatus.Blocked, userId);
        }

        public async Task UpdateUserNotificationAsync(string userId, NotificationType notificationType, string text = null)
        {
            await this.userDataHandler.Value.UpdateUserNotificationAsync(userId, notificationType, text);
        }

        public async Task RemoveUserNotificationAsync(string userId, int notificationId)
        {
            await this.userDataHandler.Value.RemoveUserNotificationAsync(userId, notificationId);
        }

        public async Task RemoveUserNotificationAsync(string userId, NotificationType notificationType)
        {
            await this.userDataHandler.Value.RemoveUserNotificationAsync(userId, notificationType);
        }

        private List<T> GetUsersWithFilter<T>(
            Expression<Func<User, bool>> filter,
            Expression<Func<User, object>> orderBy = null,
            Expression<Func<User, object>> thenBy = null)
        {
            var users = this.userDataHandler.Value
                .GetAll()
                .Where(filter);

            if (orderBy != null)
            {
                users = users.OrderBy(orderBy);

                if (thenBy != null)
                {
                    users = ((IOrderedQueryable<User>)users).ThenBy(thenBy);
                }
            }

            return this.mapper.Value.Map<List<T>>(users.ToList());
        }

        private async Task ModifyExistingUserAsync(EditUsersRequestModel request, User user)
        {
            // add checks?
            try
            {
                user.ModifiedOn = DateTime.UtcNow;
                user.SchenkerId = request.SchenkerId;
                user.CityId = request.CityId;
                user.ClientNumber = request.ClientNumber;
                user.PhoneNumber = request.PhoneNumber;

                var schenker = await this.schenkerDataHandler.Value.GetSchenkerById(request.SchenkerId);

                if (request.UserType == UserType.MOL)
                {
                    schenker.Molid = request.Id;
                    var schenkerDto = this.mapper.Value.Map<SchenkerDto>(schenker);
                    await this.schenkerDataHandler.Value.UpdateSchenker(schenkerDto);
                    user.Opcode = schenker.Opcode;
                }
                else
                {
                    if (schenker.Molid == request.Id)
                    {
                        schenker.Molid = null;
                        var schenkerDto = this.mapper.Value.Map<SchenkerDto>(schenker);
                        await this.schenkerDataHandler.Value.UpdateSchenker(schenkerDto);
                        user.Opcode = null;
                    }
                }

                await this.userDataHandler.Value.UpdateUserAsync(user);
            }
            catch (Exception)
            {
                throw;
            }
        }
    }
}
