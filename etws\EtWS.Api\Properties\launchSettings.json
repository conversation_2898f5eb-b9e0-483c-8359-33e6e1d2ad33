{
  "$schema": "http://json.schemastore.org/launchsettings.json",
  "profiles": {
    "EtWS.Api": {
      "commandName": "Project",
      "launchBrowser": true,
      "launchUrl": "swagger/index.html",
      "applicationUrl": "https://localhost:44356;http://localhost:56629",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "it9",
        "includeConsoleLogger": "true",
        // Think twice before changing to 'true'!!! Consider yourself warned!!!
        "eureka:client:shouldRegisterWithEureka": "false"
      }
    }
  }
}
