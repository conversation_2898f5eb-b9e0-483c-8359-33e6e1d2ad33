﻿namespace EtDb.DataHandlers.Models
{
    public class QuantityCalculationObjectIdentifier
    {
        public QuantityCalculationObjectIdentifier(
            string name,
            string opCode,
            bool isGroupWithSubstitutes)
        {
            this.Name = name ??
                throw new ArgumentException($"{nameof(QuantityCalculationObjectIdentifier)} Constructor Error: {name} is null");

            this.OpCode = opCode ??
                throw new ArgumentException($"{nameof(QuantityCalculationObjectIdentifier)} Constructor Error: {opCode} is null");

            this.IsGroupWithSubstitutes = isGroupWithSubstitutes;
        }

        public string Name { get; }

        public bool IsGroupWithSubstitutes { get; }

        public string OpCode { get; }

        //http://www.loganfranken.com/blog/687/overriding-equals-in-c-part-1/
        public override bool Equals(object value)
        {
            // Is null?
            if (ReferenceEquals(null, value))
            {
                return false;
            }

            // Is the same object?
            if (ReferenceEquals(this, value))
            {
                return true;
            }

            // Is the same type?
            if (value.GetType() != this.GetType())
            {
                return false;
            }

            return this.IsEqual((QuantityCalculationObjectIdentifier)value);
        }

        public bool Equals(QuantityCalculationObjectIdentifier quantityCalculationObjectServiceModel)
        {
            // Is null?
            if (ReferenceEquals(null, quantityCalculationObjectServiceModel))
            {
                return false;
            }

            // Is the same object?
            if (ReferenceEquals(this, quantityCalculationObjectServiceModel))
            {
                return true;
            }

            return this.IsEqual(quantityCalculationObjectServiceModel);
        }

        //http://www.loganfranken.com/blog/692/overriding-equals-in-c-part-2/
        public override int GetHashCode()
        {
            int hash = 13;
            hash = (hash * 7) + this.OpCode.GetHashCode();
            hash = (hash * 7) + this.Name.GetHashCode();
            return hash;
        }

        private bool IsEqual(QuantityCalculationObjectIdentifier quantityCalculationObjectServiceModel)
        {
            return string.Equals(this.OpCode, quantityCalculationObjectServiceModel.OpCode)
                && string.Equals(this.Name, quantityCalculationObjectServiceModel.Name);
        }
    }
}
