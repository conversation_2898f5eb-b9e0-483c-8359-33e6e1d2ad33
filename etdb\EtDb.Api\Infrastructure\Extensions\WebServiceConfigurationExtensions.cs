﻿namespace EtDb.Api.Infrastructure.Extensions
{
    using System.Reflection;
    using System.Text.Json.Serialization;

    using EtDb.ApiClients.EtDb.Context;
    using EtDb.Infrastructure.Models.Infrastructure;
    using Hellang.Middleware.ProblemDetails;
    using Microsoft.EntityFrameworkCore;
    using Net.Common.Mvc.Extensions;
    using Net.HealthChecks;
    using Net.Logging.Extensions;
    using Net.Middlewares.RequestId;
    using Net.Middlewares.Username;
    using Net.Swashbuckle;
    using Net.Tracing;
    using Steeltoe.Connector.Redis;
    using Steeltoe.Discovery.Eureka;

    public static class WebServiceConfigurationExtensions
    {
        public static IServiceCollection AddWebService(
            this IServiceCollection services,
            IConfiguration configuration)
            => services
                .Configure<ApiBehaviorOptions>(options => options.SuppressModelStateInvalidFilter = true)
                .AddAutoMapper(Assembly.GetExecutingAssembly())
                .AddCors()
                .AddDateTimeProvider()
                .AddDistributedRedisCache(configuration, addSteeltoeHealthChecks: true)
                .AddHealthChecksAndUI(configuration)
                .AddHttpClients(configuration)
                .AddJaegerTracing(options => options.ServiceName = WebServiceHelpers.GetApiName(configuration))
                .AddLogging(configuration)
                .AddDbContext(configuration)
                .AddMvcComponents()
                .AddOptions(configuration)
                .AddProblemDetails(WebServiceHelpers.SetDefaultProblemDetailsOptions)
                .AddRequestId("EQD")
                .AddResponseCaching()
                .AddSwagger(configuration)
                .AddSingleton<IHealthCheckHandler, ScopedEurekaHealthCheckHandler>()
                .AddUsername();

        public static IApplicationBuilder UseWebService(
            this IApplicationBuilder app,
            IConfiguration configuration)
            => app
                .UseInnerRequestResponseLogging()
                .UseProblemDetails()
                .UseResponseCaching()
                .UseAllHealthChecksEndpoints()
                .UseUsername()
                .UseRequestId(proxyRequestId: false)
                .UseRouting()
                .UseCors(WebServiceHelpers.AllowAnyOriginPolicy)
                .UseEndpoints(endpoints => endpoints.MapControllers())
                .UseSwagger(configuration);

        public static IServiceCollection AddMvcComponents(this IServiceCollection services)
        {
            services
                .AddMvc(WebServiceHelpers.SetDefaultMvcOptions)
                .AddControllersAsServices()
                .AddJsonOptions(options => options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter()));

            return services;
        }

        public static IServiceCollection AddHealthChecksAndUI(this IServiceCollection serviceCollection, IConfiguration configuration)
        {
            var redisConnectionString = configuration.GetConnectionString<RedisConnectionInfo>();

            serviceCollection
                .AddHealthChecks()
                .AddRedis(redisConnectionString: redisConnectionString);

            return serviceCollection;
        }

        public static IServiceCollection AddHttpClients(this IServiceCollection services, IConfiguration configuration)
        {
#pragma warning disable S125 // Sections of code should not be commented out
            /*
                      services.AddEurekaHttpClient<IMpssWs, MpssWs>(configuration, nameof(MpssWs));

                      services.AddHttpClient(configuration, Names.TemplatesApi);
                    */

            return services;
#pragma warning restore S125 // Sections of code should not be commented out
        }

        public static IServiceCollection AddOptions(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddOptions();
            services.Configure<AppSettings>(configuration);

            services
                .Configure<Dictionary<string, string>>(
                    Constants.EndpointsConfigKey, configuration.GetSection(Constants.EndpointsConfigKey));

            return services;
        }

        public static IServiceCollection AddDbContext(this IServiceCollection services, IConfiguration configuration)
             => services
                 .AddDbContextPool<EtDbContext>(opts =>
                     opts.UseSqlServer(configuration[$"{GlobalConstants.ConnectionStringsConfigKey}:{Names.EtpDb}"]));
    }
}
