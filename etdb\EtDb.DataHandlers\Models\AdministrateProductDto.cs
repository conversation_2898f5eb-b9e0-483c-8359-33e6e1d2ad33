﻿using EtDb.ApiClients.EtDb.Models;
using System.Linq.Expressions;

namespace EtDb.DataHandlers.Models
{
    public class AdministrateProductDto
    {
        public static Expression<Func<EquipmentType, SapmatMapp, AdministrateProductDto>> FromProducts
        {
            get
            {
                return (eqt, smm) => new AdministrateProductDto
                {
                    Id = eqt.Id,
                    Name = eqt.Name,
                    SAPMaterialNum = eqt.SapmaterialNum,
                    SerialNumberRequired = eqt.SerialNumberRequired,
                    MatFirst = smm.MatFirst ?? string.Empty,
                    EquipmentNameFirst = smm.EquipmentNameFirst ?? string.Empty,
                    MinimumQuantity = eqt.MinimumQuantity,
                    SapRequestType = eqt.SendMethod,
                    SapElementCode = eqt.SapsupplyCode,
                    EquipmentGroupId = eqt.EquipmentGroupId,
                    UnitOfMeasure = eqt.UnitOfMeasure,
                    BoxCapacity = eqt.BoxCapacity,
                    BRProjectName = eqt.BrprojectCode,
                };
            }
        }

        public int Id { get; set; }

        public string Name { get; set; } = null!;

        public string SAPMaterialNum { get; set; } = null!;

        public int SerialNumberRequired { get; set; }

        public string MatFirst { get; set; } = null!;

        public string EquipmentNameFirst { get; set; } = null!;

        public int MinimumQuantity { get; set; }

        public int SapRequestType { get; set; }

        public string SapElementCode { get; set; } = null!;

        public int EquipmentGroupId { get; set; }

        public int UnitOfMeasure { get; set; }

        public int? BoxCapacity { get; set; }

        public string BRProjectName { get; set; } = null!;
    }
}
