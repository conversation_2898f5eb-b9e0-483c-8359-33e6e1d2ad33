﻿namespace EtDb.Api.Controllers
{
    using EtDb.Api.Infrastructure.Utils;
    using EtDb.DataHandlers.Models;
    using EtDb.Infrastructure.Enumerations;
    using EtDb.Models.Requests.EquipmentModels;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.EquipmentModels;
    using EtDb.Services.Interfaces;

    public class EquipmentTypeController : BaseApiController
    {
        private readonly Lazy<IEquipmentTypeService> equipmentTypeService;

        public EquipmentTypeController(Lazy<IEquipmentTypeService> equipmentTypeService)
        {
            this.equipmentTypeService = equipmentTypeService;
        }

        [HttpGet("all")]
        [ProducesResponseType(typeof(IEnumerable<EquipmentTypeResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<EquipmentTypeResponseModel>>> GetEquipmentTypes()
        {
            try
            {
                return this.Ok(await this.equipmentTypeService.Value.GetEquipmentTypesAsync());
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("by-id/{id}")]
        [ProducesResponseType(typeof(EquipmentTypeResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<EquipmentTypeResponseModel>> GetEquipmentTypeByIdAsync(int id)
        {
            try
            {
                return await this.equipmentTypeService.Value.GetEquipmentTypeByIdAsync(id);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("type-id-by-sap-material-num/{sapMaterialNum}")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> GetEquipmentTypeIdAsync(string sapMaterialNum)
        {
            try
            {
                return this.Ok(await this.equipmentTypeService.Value.GetEquipmentTypeIdAsync(sapMaterialNum));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("by-send-method/{sendMethod}")]
        [ProducesResponseType(typeof(IEnumerable<EquipmentTypeResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<EquipmentTypeResponseModel>>> GetEquipmentTypesAsync(string sendMethod)
        {
            try
            {
                if (!Enum.TryParse(sendMethod, true, out SendMethod sendMethodEnum))
                {
                    throw new ArgumentException("Send method not valid!");
                }

                return this.Ok(await this.equipmentTypeService.Value.GetEquipmentTypesAsync(sendMethodEnum));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("concise-equipment-types")]
        [ProducesResponseType(typeof(IEnumerable<EquipmentTypeConciseDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<EquipmentTypeConciseDto>>> GetConciseEquipmentTypes([Required] ConciseEquipmentTypesRequestModel request)
        {
            try
            {
                return this.Ok(await this.equipmentTypeService.Value.GetConciseEquipmentTypesAsync(request.EquipmentTypeIds, request.SerialNumbersRequired));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("last-month-quantities")]
        [ProducesResponseType(typeof(IDictionary<string, int>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IDictionary<string, int>>> GetEquipmentTypesLastMonthQuantitiesAsync()
        {
            try
            {
                return this.Ok(await this.equipmentTypeService.Value.GetEquipmentTypesLastMonthQuantitiesAsync());
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
