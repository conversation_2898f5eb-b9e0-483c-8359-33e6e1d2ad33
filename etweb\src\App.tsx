import Navbar from "./components/Navbar";
import HomePage from "./pages/HomePage";
import Login from "./pages/Login";
import Correction from "./components/correction/Correction";
import UsersPage from "./pages/administration/UsersPage";
import RequireAuth from "./components/auth/RequireAuth";
import { Route, Routes } from "react-router";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "react-hot-toast";

function App() {
  return (
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      <Toaster position="top-right" reverseOrder={false} />
      <Navbar />
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route element={<RequireAuth />}>
          <Route path="/" element={<HomePage />} />
          <Route path="/transfer-correction" element={<Correction />} />
          <Route path="/administration/users" element={<UsersPage />} />
        </Route>
      </Routes>
    </ThemeProvider>
  );
}

export default App;
