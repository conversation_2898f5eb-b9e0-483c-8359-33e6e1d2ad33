﻿namespace EtWS.Models.Requests
{
    public class SchenkerRequestModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "OPCodeRequired")]
        [MinLength(4, ErrorMessage = "OPCodeMinLengthInvalid")]
        [MaxLength(5, ErrorMessage = "OPCodeMaxLengthInvalid")]
        public string OPCode { get; set; }

        public int TransportArea { get; set; }

        public int ProcessingTime { get; set; }

        public int ProtectiveTime { get; set; }

        public int CityId { get; set; }

        public string Address { get; set; }

        public int? LocalWarehouseId { get; set; }
    }
}
