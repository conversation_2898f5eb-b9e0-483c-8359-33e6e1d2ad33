﻿namespace EtDb.Services
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using EtDb.Models.Requests;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.UserModels;
    using EtDb.Services.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class SchenkerService : ISchenkerService
    {
        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<ISchenkerDataHandler> schenkerDataHandler;

        public SchenkerService(Lazy<ISchenkerDataHandler> schenkerDataHandler, Lazy<IMapper> mapper)
        {
            this.schenkerDataHandler = schenkerDataHandler;
            this.mapper = mapper;
        }

        public async Task<SchenkersResponseModel> GetSchenkerById(int id)
        {
            Schenkers schenker = await this.schenkerDataHandler.Value.GetSchenkerById(id, q => q.Include(s => s.City).Include(s => s.Warehouse));
            return this.mapper.Value.Map<Schenkers, SchenkersResponseModel>(schenker);
        }

        public async Task<IEnumerable<UserConciseResponseModel>> GetSchenkerUsers(int schenkerId)
        {
            var schenker = await this.schenkerDataHandler.Value.GetSchenkerById(schenkerId, nameof(Schenkers.User));

            return this.mapper.Value.Map<ICollection<User>, IEnumerable<UserConciseResponseModel>>(schenker.User);
        }

        public async Task<List<SchenkerOpCodeResponseModel>> GetSchenkerOpCodesList()
        {
            var schenkersWithUsers = await this.schenkerDataHandler.Value.GetSchenkersWithUsers();
            return this.mapper.Value.Map<List<Schenkers>, List<SchenkerOpCodeResponseModel>>(await schenkersWithUsers.ToListAsync());
        }

        public async Task<SearchResponseModel<SchenkersResponseModel>> SearchSchenkers(SearchRequestModel request)
        {
            var data = await this.schenkerDataHandler.Value.GetFilteredSchenkers(
                request.SortBy, request.SortDir, request.PageNumber, request.PageSize, request.Query);

            return this.mapper.Value.Map<FilteredDataModel<Schenkers>, SearchResponseModel<SchenkersResponseModel>>(data);
        }

        public async Task<int> InsertSchenker(SchenkersRequestModel request)
        {
            var data = this.mapper.Value.Map<SchenkersRequestModel, SchenkerDto>(request);
            return await this.schenkerDataHandler.Value.InsertSchenker(data);
        }

        public async Task UpdateSchenker(SchenkersRequestModel request)
        {
            var data = this.mapper.Value.Map<SchenkersRequestModel, SchenkerDto>(request);
            await this.schenkerDataHandler.Value.UpdateSchenker(data);
        }
    }
}
