﻿namespace EtWS.Api.Controllers
{
    using EtWS.Api.Infrastructure.Utils;
    using EtWS.ApiClients.ETDB;
    using EtWS.Services.WarehousesService;
    using Microsoft.AspNetCore.Authentication.Cookies;
    using Microsoft.AspNetCore.Authorization;

    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]

    public class WarehousesController : BaseApiController
    {
        private readonly Lazy<IWarehousesService> warehousesService;

        public WarehousesController(Lazy<IWarehousesService> warehousesService)
        {
            this.warehousesService = warehousesService;
        }

        [HttpGet("select-list")]
        [ProducesResponseType(typeof(IEnumerable<WarehousesResponseModel>), StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<WarehousesResponseModel>>> GetWarehousesSelectListAsync()
        {
            try
            {
                return this.Ok(await this.warehousesService.Value.GetWarehousesSelectListAsync());
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("by-region")]
        [ProducesResponseType(typeof(IEnumerable<WarehousesResponseModel>), StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<WarehousesResponseModel>>> GetWarehousesSelectListAsync([FromQuery] int? region)
        {
            try
            {
                return this.Ok(await this.warehousesService.Value.GetWarehousesByRegionAsync(region));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
