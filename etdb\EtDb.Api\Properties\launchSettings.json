{
  "$schema": "http://json.schemastore.org/launchsettings.json",
  "profiles": {
    "EtDb.Api": {
      "commandName": "Project",
      "launchBrowser": true,
      "launchUrl": "swagger/index.html",
      "applicationUrl": "https://localhost:44357;http://localhost:56628",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "dev",
        "includeConsoleLogger": "true",
        // Think twice before changing to 'true'!!! Consider yourself warned!!!
        "eureka:client:shouldRegisterWithEureka": "false"
      }
    }
  }
}
