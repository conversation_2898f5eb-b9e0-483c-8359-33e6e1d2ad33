﻿using EtDb.ApiClients.EtDb.Models;
using EtDb.Models.Responses;
using EtDb.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace EtDb.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SnapshotController : ControllerBase
    {
        private readonly Lazy<ISnapshotService> snapshotService;

        public SnapshotController(Lazy<ISnapshotService> snapshotService)
        {
            this.snapshotService = snapshotService;
        }

        [HttpGet]
        [Route("lastAvailableSnapshotForDate")]
        [ProducesResponseType(typeof(SnapshotDates), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SnapshotDates>> FindLastAvailableSnapshotForDate([Required] DateTime date)
        {
            try
            {
                var result = await this.snapshotService.Value.FindLastAvailableSnapshotForDate(date);
                return this.Ok(result);
            }
            catch (ArgumentException arg)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpGet]
        [Route("allSnapshotsWithDataBetweenDates")]
        [ProducesResponseType(typeof(List<SnapshotDates>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<List<SnapshotDates>>> FindAllSnapshotsWithDataBetweenDates([Required] DateTime fromDate, [Required] DateTime toDate, bool includeLastAvailableBeforeFromDate = true)
        {
            try
            {
                var result = await this.snapshotService.Value.FindAllSnapshotsWithDataBetweenDatesAsync(fromDate, toDate, includeLastAvailableBeforeFromDate);
                return this.Ok(result);
            }
            catch (ArgumentException arg)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }
    }
}
