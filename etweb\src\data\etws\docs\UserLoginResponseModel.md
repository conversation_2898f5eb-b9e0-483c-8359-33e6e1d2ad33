# UserLoginResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**userRoles** | **Array&lt;string&gt;** |  | [optional] [default to undefined]
**username** | **string** |  | [optional] [default to undefined]
**profilePicture** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { UserLoginResponseModel } from './api';

const instance: UserLoginResponseModel = {
    userRoles,
    username,
    profilePicture,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
