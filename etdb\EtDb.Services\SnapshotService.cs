﻿using EtDb.ApiClients.EtDb.Models;
using EtDb.DataHandlers.Interfaces;
using EtDb.Services.Interfaces;

namespace EtDb.Services
{
    public class SnapshotService : ISnapshotService
    {

        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<ISnapshotDateDataHandler> snapshotDateDataHandler;

        public SnapshotService(Lazy<IMapper> mapper, Lazy<ISnapshotDateDataHandler> snapshotDateDataHandler)
        {
            this.mapper = mapper;
            this.snapshotDateDataHandler = snapshotDateDataHandler;
        }

        public async Task<List<SnapshotDates>> FindAllSnapshotsWithDataBetweenDatesAsync(DateTime fromDate, DateTime toDate, bool includeLastAvailableBeforeFromDate = true)
        {
            var allSnapshotsWithData = await this.snapshotDateDataHandler.Value
                .FindAllSnapshotsWithDataBetweenDatesAsync(fromDate, toDate, includeLastAvailableBeforeFromDate);
            if (allSnapshotsWithData == null || allSnapshotsWithData.Count == 0)
            {
                throw new ArgumentException($"No snapshots found between {fromDate} and {toDate}");
            }

            return allSnapshotsWithData;
        }

        public async Task<SnapshotDates> FindLastAvailableSnapshotForDate(DateTime date)
        {
            var foundSnapShot = await this.snapshotDateDataHandler.Value.FindLastAvailableSnapshotForDateAsync(date);

            if (foundSnapShot == null)
            {
                throw new ArgumentException($"No snapshot found for date {date}");
            }

            return foundSnapShot;
        }
    }
}
