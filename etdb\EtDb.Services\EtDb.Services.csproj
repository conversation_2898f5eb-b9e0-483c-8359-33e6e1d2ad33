﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <CodeAnalysisRuleSet>../Rules.ruleset</CodeAnalysisRuleSet>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>
	<ItemGroup>
		<AdditionalFiles Include="..\Rules.ruleset" />
		<AdditionalFiles Include="..\stylecop.json" />
	</ItemGroup>
  <ItemGroup>
    <Using Include="AutoMapper" />
    <Using Include="Net.Common" />
    <Using Include="EtDb.Infrastructure.Constants" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
    <PackageReference Include="Net.Ams" Version="0.2.20" />
    <PackageReference Include="Net.Common.Mvc" Version="0.2.14" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="6.0.0" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EtDb.ApiClients\EtDb.ApiClients.csproj" />
    <ProjectReference Include="..\EtDb.DataHandlers\EtDb.DataHandlers.csproj" />
    <ProjectReference Include="..\EtDb.Infrastructure\EtDb.Infrastructure.csproj" />
    <ProjectReference Include="..\EtDb.Models\EtDb.Models.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Microsoft.Extensions.Options">
      <HintPath>C:\Program Files\dotnet\sdk\NuGetFallbackFolder\microsoft.extensions.options\2.2.0\lib\netstandard2.0\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
