﻿namespace ET.Database.Enums
{
    using System.ComponentModel.DataAnnotations;

    public enum TransferStatus
    {
        //[Display(Name = "Нов")]
        //New = 1,

        [Display(Name = "Изпратен за одобрение")]
        SentForApproval = 2,

        [Display(Name = "Одобрен от ОУ")]
        ApprovedByServiceManagement = 3,

        [Display(Name = "Одобрен от Inv.Control")]
        ApprovedByInvControl = 4,

        [Display(Name = "Одобрен от Impl.Control")]
        ApprovedByImplControl = 5,

        [Display(Name = "Финализиран")]
        Finalized = 6,

        [Display(Name = "Автоматично генериран")]
        AutomaticallyGenerated = 7,

        [Display(Name = "Автоматичен трансфер одобрен от ОУ")]
        AutomaticApprovedByServiceManagement = 8,

        [Display(Name = "Автоматичен трансфер одобрен от ПУ")]
        AutomaticApprovedByProductsAndServices = 9,

        [Display(Name = "Автоматичен трансфер преразпределен")]
        AutomaticRearranged = 10,

        [Display(Name = "Автоматичен трансфер одобрен от Inv.Control")]
        AutomaticApprovedByInvControl = 11,

        [Display(Name = "Очаква одобрение от ПУ")]
        PendingAutomaticApprovalByProductsAndServices = 12
    }
}
