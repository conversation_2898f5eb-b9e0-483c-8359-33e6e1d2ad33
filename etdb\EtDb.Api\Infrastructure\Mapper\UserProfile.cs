﻿namespace EtDb.Api.Infrastructure.Mapper
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Models;
    using EtDb.Infrastructure.Enumerations;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.UserModels;

    public class UserProfile : Profile
    {
        public UserProfile()
        {
            this.CreateMap<User, UserResponseModel>();

            this.CreateMap<User, SearchUserResponseModel>()
                .ForMember(m => m.Op, opts => opts.MapFrom(u => u.SchenkerId != null ? u.Schenker.Opcode : string.Empty))
                .ForMember(m => m.RegionId, opt => opt.MapFrom(u => u.CityId != null ? u.City.Region : (int?)null))
                .ForMember(m => m.Region, opt => opt.MapFrom(u => u.CityId != null ? ((Regions)u.City.Region).ToString() : null))
                .ForMember(m => m.CityId, opt => opt.MapFrom(u => u.CityId))
                .ForMember(m => m.City, opt => opt.MapFrom(u => u.CityId != null ? u.City.Name : null))
                .ForMember(m => m.ClientNumber, opt => opt.MapFrom(u => u.ClientNumber ?? string.Empty));

            this.CreateMap<User, UsersWithOpCodeResponseModel>();

            this.CreateMap<User, UserConciseResponseModel>();

            this.CreateMap<FilteredDataModel<User>, SearchResponseModel<SearchUserResponseModel>>()
                .ForMember(m => m.DataCollection, opts => opts.MapFrom(x => x.DataRows.ToList()))
                .ForMember(m => m.Count, opts => opts.MapFrom(x => x.AllDataRowsCount));
        }
    }
}
