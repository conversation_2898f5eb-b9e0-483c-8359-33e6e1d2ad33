﻿namespace EtDb.Services.Interfaces
{
    using EtDb.Models.Requests;
    using EtDb.Models.Requests.QuantityCalculationObjectModels;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.QuantityCalculationObjectModels;

    public interface ICriticalQuantitiesService : IService
    {
        Task<SearchResponseModel<QuantityCalculationObjectResponseModel>> SearchQuantityCalculationObjectsAsync(SearchRequestModel request);

        Task<QuantityCalculationObjectResponseModel> GetQuantityCalculationObjectByIdAsync(int id);

        Task UpdateQuantityCalculationObjectAsync(QuantityCalculationObjectRequestModel request);
    }
}
