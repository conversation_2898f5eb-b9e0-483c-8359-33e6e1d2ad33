﻿namespace EtWS.Api.Controllers
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.HistoriyModels;
    using EtWS.Models.Responses.UserModels;
    using EtWS.Services.IncorrectEquipments;

    public class IncorrectEquipmentController : BaseApiController
    {
        private readonly Lazy<IIncorrectEquipmentService> incorrectEquipmentService;

        public IncorrectEquipmentController(Lazy<IIncorrectEquipmentService> incorrectEquipmentService)
        {
            this.incorrectEquipmentService = incorrectEquipmentService;
        }

        [HttpPost("transfer-correction")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]

        public async Task<ActionResult> TransferCorrectionAsync([FromBody] TransferCorrectionRequest request)
        {
            if (request == null || string.IsNullOrEmpty(request.EquipmentSerialNum.ToString()))
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "The request cannot be null and must contain a valid EquipmentSerialNum." });
            }

            try
            {
                var response = await this.incorrectEquipmentService.Value.TransferCorrectionAsync(request);

                if (!response.Success)
                {
                    return this.BadRequest(new BaseResponseModel { Success = false, Message = response.Message });
                }

                return this.Ok(new BaseResponseModel { Success = true, Message = response.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpDelete("transfer-delete")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> TransferDeleteByDateAsync(DeleteTransferRequest request)
        {
            if (request == null || string.IsNullOrEmpty(request.Id.ToString()) || string.IsNullOrEmpty(request.ItemId.ToString()))
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "The request cannot be null and must contain a valid Id." });
            }

            try
            {
                var response = await this.incorrectEquipmentService.Value.DeleteTransferByHistoryId(request);

                if (!response.Success)
                {
                    return this.BadRequest(new BaseResponseModel { Success = false, Message = response.Message });
                }

                return this.Ok(new BaseResponseModel { Success = true, Message = response.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = $"An error occurred: {ex.Message}" });
            }
        }

        [HttpGet("users-with-opcodes")]
        [ProducesResponseType(typeof(IEnumerable<UsersWithOPCodeResponse>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<BaseResponseModel>> GetUsersWithOPCodesAsync()
        {
            try
            {
                var usersWithOpcodes = await this.incorrectEquipmentService.Value.GetUsersWithOPCodesAsync();

                if (usersWithOpcodes == null || !usersWithOpcodes.Any())
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = "No users with OPCodes were found." });
                }

                return this.Ok(usersWithOpcodes);
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = $"An error occurred: {ex.Message}" });
            }
        }

        [HttpGet("check-serial-number/{equipmentSerialNum}")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<BaseResponseModel>> DoesSerialNumberExistAsync(string equipmentSerialNum)
        {
            if (string.IsNullOrWhiteSpace(equipmentSerialNum))
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Equipment serial number cannot be empty." });
            }

            try
            {
                var response = await this.incorrectEquipmentService.Value.DoesSerialNumberExistAsync(equipmentSerialNum);

                if (!response.Success)
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = "Equipment serial number not exist" });
                }

                return this.Ok(response);
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = $"Error checking serial number: {ex.Message}" });
            }
        }

        [HttpGet("get-user-display-names/{namePrefix}")]
        [ProducesResponseType(typeof(ICollection<UserDisplayNameResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ICollection<UserDisplayNameResponseModel>>> GetUserDisplayName(string namePrefix)
        {
            if (string.IsNullOrWhiteSpace(namePrefix))
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Name prefix cannot be empty." });
            }

            try
            {
                var result = await this.incorrectEquipmentService.Value.GetUserDisplayName(namePrefix);

                if (!result.Any())
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = "No users found with the given prefix." });
                }

                return this.Ok(result);
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = $"Error fetching user display names: {ex.Message}" });
            }
        }

        [HttpGet("check-service-id/{serviceId}")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<BaseResponseModel>> CheckServiceId(string serviceId)
        {
            if (string.IsNullOrWhiteSpace(serviceId))
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Service Id cannot be null or empty." });
            }

            try
            {
                var response = await this.incorrectEquipmentService.Value.CheckServiceId(serviceId);

                if (!response.Success)
                {
                    return this.NotFound(response);
                }

                return this.Ok(response);
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = $"Unexpected error occurred: {ex.Message}" });
            }
        }

        [HttpGet("get-histories/{equipmentSerialnumber}")]
        [ProducesResponseType(typeof(IEnumerable<HistoriesResponse>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<HistoriesResponse>>> GetHistoriesByEquipmentSerialNumber(string equipmentSerialnumber)
        {
            if (string.IsNullOrWhiteSpace(equipmentSerialnumber))
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Equipment serial number cannot be empty." });
            }

            try
            {
                var histories = await this.incorrectEquipmentService.Value.GetAllHistoriesByEquipmentSerialNumber(equipmentSerialnumber);

                if (histories == null || !histories.Any())
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = "No histories found for the provided equipment serial number." });
                }

                return this.Ok(histories);
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = $"An error occurred while fetching histories: {ex.Message}" });
            }
        }
    }
}