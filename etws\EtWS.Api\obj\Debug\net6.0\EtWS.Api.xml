<?xml version="1.0"?>
<doc>
    <assembly>
        <name>EtWS.Api</name>
    </assembly>
    <members>
        <member name="M:EtWS.Api.Controllers.CitiesController.GetAsync(System.Int32)">
            <summary>
            Gets city data by its id.
            </summary>
            <param name="id">The id of the city.</param>
            <returns>City data.</returns>
        </member>
        <member name="M:EtWS.Api.Controllers.CitiesController.GetCityRegionByIdAsync(System.Int32)">
            <summary>
            Returns the region of the city as string.
            </summary>
            <param name="id">The id of the city.</param>
            <returns>The region of the city.</returns>
        </member>
        <member name="M:EtWS.Api.Controllers.CitiesController.GetCitiesAsync(EtWS.Models.Requests.SearchDataRequestModel)">
            <summary>
            Search cities based on criterias.
            </summary>
            <param name="request">The search parameters of the request.</param>
            <returns>List of cities, corresponding to the search criterias.</returns>
        </member>
        <member name="M:EtWS.Api.Controllers.CitiesController.AddCityAsync(EtWS.Models.Requests.CityRequestModel)">
            <summary>
            Adds city to the database.
            </summary>
            <param name="request">The city data to save in the database.</param>
            <returns>The id of the newly created city.</returns>
        </member>
        <member name="M:EtWS.Api.Controllers.CitiesController.UpdateCityAsync(System.Int32,EtWS.Models.Requests.CityRequestModel)">
            <summary>
            Updates an existing city in the databse.
            </summary>
            <param name="id">The id of the city, being updated.</param>
            <param name="request">The new city data.</param>
            <returns>ActionResult.</returns>
        </member>
        <member name="M:EtWS.Api.Controllers.CitiesController.BlockCitiesAsync(System.Collections.Generic.List{System.Int32})">
            <summary>
            Blocks selected cities in the database.
            </summary>
            <param name="cityIds">The ids of the cities, that needs to be blocked.</param>
            <returns>ActionResult.</returns>
        </member>
        <member name="M:EtWS.Api.Controllers.CitiesController.ActivateCitiesAsync(System.Collections.Generic.List{System.Int32})">
            <summary>
            Activates selected cities in the database.
            </summary>
            <param name="cityIds">The ids of the cities, that needs to be activated.</param>
            <returns>ActionResult.</returns>
        </member>
        <member name="M:EtWS.Api.Controllers.CitiesController.GetSapCityCodes(System.Int32)">
            <summary>
            Gets a city by its id (request parameter) and returns the SAPCityCode and Cluster of the city.
            </summary>
            <param name="cityId">The id of the city.</param>
            <returns>SAPCityCode and Cluster.</returns>
        </member>
    </members>
</doc>
