# AcceptItemsForTransferRequestModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**selectedItems** | **Array&lt;number&gt;** |  | [optional] [default to undefined]

## Example

```typescript
import { AcceptItemsForTransferRequestModel } from './api';

const instance: AcceptItemsForTransferRequestModel = {
    selectedItems,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
