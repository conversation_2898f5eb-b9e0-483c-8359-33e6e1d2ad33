/* Mobile-First Navigation Enhancements */

/* Enhanced touch targets for mobile devices */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Smooth scrolling for mobile navigation */
@media (max-width: 1024px) {
  .overscroll-contain {
    overscroll-behavior: contain;
  }
  
  /* Improved mobile menu animations */
  .mobile-nav-item {
    transform: translateX(0);
    transition: transform 0.2s ease-out, background-color 0.2s ease-out;
  }
  
  .mobile-nav-item:active {
    transform: translateX(4px);
    background-color: rgba(55, 65, 81, 0.5);
  }
  
  /* Enhanced mobile dropdown animations */
  .mobile-submenu {
    transform: translateY(-10px);
    opacity: 0;
    transition: all 0.3s ease-out;
  }
  
  .mobile-submenu.open {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Enhanced focus states for accessibility */
.navbar-button:focus-visible {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Improved mobile sheet content */
@media (max-width: 640px) {
  .mobile-sheet {
    width: 100vw;
    max-width: 320px;
  }
}

/* Smooth transitions for navbar background */
.navbar-backdrop {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Enhanced mobile notification badges */
.notification-badge {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.2);
}

/* Mobile-optimized dropdown menus */
@media (max-width: 1024px) {
  .mobile-dropdown {
    margin-right: 1rem;
    margin-left: 1rem;
    border-radius: 0.75rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}

/* Improved touch feedback */
.mobile-touch-feedback {
  transition: all 0.15s ease-out;
}

.mobile-touch-feedback:active {
  transform: scale(0.95);
  background-color: rgba(55, 65, 81, 0.3);
}

/* Enhanced mobile logo scaling */
.mobile-logo {
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.mobile-logo:active {
  transform: scale(0.95);
}

/* Mobile-first media queries for responsive breakpoints */
/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
  .mobile-sheet {
    max-width: 360px;
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .mobile-sheet {
    max-width: 384px;
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  /* Desktop-specific enhancements */
  .desktop-nav-item {
    position: relative;
  }
  
  .desktop-nav-item::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #3B82F6, #1D4ED8);
    transition: all 0.3s ease-out;
    transform: translateX(-50%);
  }
  
  .desktop-nav-item:hover::after {
    width: 100%;
  }
}

/* Enhanced animations for better UX */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-in {
  animation: slideInFromTop 0.3s ease-out;
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .navbar-backdrop {
    background-color: rgba(17, 24, 39, 0.8);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .mobile-nav-item,
  .mobile-submenu,
  .navbar-backdrop,
  .notification-badge,
  .mobile-logo {
    transition: none;
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .navbar-button {
    border: 2px solid currentColor;
  }
  
  .notification-badge {
    border: 2px solid white;
  }
}

/* Enhanced focus indicators for keyboard navigation */
.keyboard-navigation .navbar-button:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}
