﻿namespace EtDb.DataHandlers
{
    using AutoMapper;
    using EtDb.ApiClients.EtDb.Context;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using Microsoft.EntityFrameworkCore;
    using Net.Common.Extensions;
    using System.Linq;

    public class SchenkerDataHandler : BaseData<PERSON><PERSON>ler, ISchenkerDataHandler
    {
        private readonly Lazy<IMapper> mapper;

        public SchenkerDataHandler(Lazy<EtDbContext> dbContext, Lazy<IMapper> mapper)
            : base(dbContext)
        {
            this.mapper = mapper;
        }

        public IQueryable<Schenkers> GetAll() =>
            this.dbContext.Value
                .Schenkers;

        public async Task<FilteredDataModel<Schenkers>> GetFilteredSchenkers(string sortBy, string sortDir, int pageNumber, int pageSize, string query)
        {
            IQueryable<Schenkers> schenkers = this.dbContext.Value.Schenkers
                .Include(s => s.User)
                .Include(s => s.Warehouse)
                .Include(s => s.City);

            FilteredDataModel<Schenkers> filteredGridData = await this.GetFilteredData(schenkers, sortBy, sortDir, pageNumber, pageSize, query);

            return filteredGridData;
        }

        public async Task<IQueryable<Schenkers>> GetSchenkersWithUsers() =>
            await Task.FromResult(this.dbContext.Value.Schenkers.Include(s => s.User).OrderBy(u => u.Opcode));

        public async Task<Schenkers> GetSchenkerById(int id)
        {
            Schenkers? schenker = await this.dbContext.Value.Schenkers.SingleOrDefaultAsync(e => e.Id == id);

            if (schenker == null)
            {
                throw new ArgumentException($"No schenker with id: {id} was found.");
            }

            return schenker;
        }

        public async Task<Schenkers> GetSchenkerById(int id, Func<IQueryable<Schenkers>, IQueryable<Schenkers>> includeProperties)
        {
            var query = this.dbContext.Value.Schenkers.Select(s => s);

            if (includeProperties != null)
            {
                query = includeProperties(query);
            }

            Schenkers? schenker = await query.SingleOrDefaultAsync(s => s.Id == id);

            if (schenker == null)
            {
                throw new ArgumentException($"No schenker with id: {id} was found.");
            }

            return schenker;
        }

        public async Task<Schenkers> GetSchenkerById(int id, params string[] includeProperties)
        {
            var query = this.dbContext.Value.Schenkers.Select(s => s);

            query = includeProperties.Aggregate(query, (current, property) => current.Include(property));

            Schenkers? schenker = await query.SingleOrDefaultAsync(s => s.Id == id);

            if (schenker == null)
            {
                throw new ArgumentException($"No schenker with id: {id} was found.");
            }

            return await Task.FromResult(schenker);
        }

        public async Task<bool> CheckForExistingOPCode(int id, string opCode)
        {
            return await this.dbContext.Value.Schenkers.AnyAsync(c => c.Opcode.ToLower() == opCode.ToLower() && c.Id != id);
        }

        public async Task<int> InsertSchenker(SchenkerDto schenkerToInsert)
        {
            bool isExistingOPCode = await this.CheckForExistingOPCode(schenkerToInsert.Id, schenkerToInsert.OPCode);
            if (isExistingOPCode)
            {
                throw new ArgumentException($"There is another schenker with OP Code: {schenkerToInsert.OPCode}.");
            }

            var schenker = this.mapper.Value.Map<Schenkers>(schenkerToInsert);
            await this.dbContext.Value.Schenkers.AddAsync(schenker);
            await this.dbContext.Value.SaveChangesAsync();


            return schenker.Id;
        }

        public async Task UpdateSchenker(SchenkerDto schenkerToUpdate)
        {
            bool isExistingOPCode = await this.CheckForExistingOPCode(schenkerToUpdate.Id, schenkerToUpdate.OPCode);
            if (isExistingOPCode)
            {
                throw new ArgumentException($"There is another schenker with OP Code: {schenkerToUpdate.OPCode}.");
            }

            var schenker = await this.dbContext.Value.Schenkers.Include(s => s.User).Include(s => s.City).Include(s => s.Warehouse).SingleOrDefaultAsync(s => s.Id == schenkerToUpdate.Id);
            if (schenker == null)
            {
                throw new ArgumentException($"No schenker with id: {schenkerToUpdate.Id} was found.");
            }

            schenker.Opcode = schenkerToUpdate.OPCode;
            schenker.TransportArea = schenkerToUpdate.TransportArea;
            schenker.ProcessingTime = schenkerToUpdate.ProcessingTime;
            schenker.ProtectiveTime = schenkerToUpdate.ProtectiveTime;

            if (schenker.User != null)
            {
                schenker.User.ForEach(u => u.Opcode = schenkerToUpdate.OPCode);
            }

            schenker.WarehouseId = schenkerToUpdate.LocalWarehouseId;
            schenker.CityId = schenkerToUpdate.CityId;
            schenker.Address = schenkerToUpdate.Address;

            this.dbContext.Value.Schenkers.Update(schenker);
            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task<bool> IsUserMol(string userId)
        {
            var schenker = await this.dbContext.Value
                .Schenkers
                .FirstOrDefaultAsync(s => s.Molid == userId);

            return schenker != null;
        }
    }
}
