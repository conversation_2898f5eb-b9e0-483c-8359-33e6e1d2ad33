﻿namespace EtDb.Api.Controllers
{
    using EtDb.Api.Infrastructure.Utils;
    using EtDb.Models.Requests;
    using EtDb.Models.Requests.QuantityCalculationObjectModels;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.QuantityCalculationObjectModels;
    using EtDb.Services.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class QuantityCalculationObjectController : BaseApiController
    {
        private readonly Lazy<ICriticalQuantitiesService> criticalQuantitiesService;

        public QuantityCalculationObjectController(Lazy<ICriticalQuantitiesService> criticalQuantitiesService)
        {
            this.criticalQuantitiesService = criticalQuantitiesService;
        }

        [HttpPost]
        [Route("search")]
        [ProducesResponseType(typeof(SearchResponseModel<QuantityCalculationObjectResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<QuantityCalculationObjectResponseModel>>> SearchQuantityCalculationObjectsAsync([Required] SearchRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.criticalQuantitiesService.Value.SearchQuantityCalculationObjectsAsync(request);
                    return this.Ok(result);
                }

                var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
            }
            catch (ArgumentException arg)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message, });
            }
        }

        [HttpGet]
        [ProducesResponseType(typeof(QuantityCalculationObjectResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<QuantityCalculationObjectResponseModel>> GetQuantityCalculationObjectById([Required] int id)
        {
            try
            {
                var result = await this.criticalQuantitiesService.Value.GetQuantityCalculationObjectByIdAsync(id);
                return this.Ok(result);
            }
            catch (ArgumentException arg)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPost("update")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateQuantityCalculationObject([FromBody] QuantityCalculationObjectRequestModel request)
        {
            try
            {
                await this.criticalQuantitiesService.Value.UpdateQuantityCalculationObjectAsync(request);

                return this.Ok(new BaseResponseModel { Success = true, Message = "Quantity calculation object updated successfully." });
            }
            catch (ArgumentException argEx)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = argEx.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message ?? ex.Message });
            }
        }
    }
}