# SubstitutionsResponseModelSearchResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**count** | **number** |  | [optional] [default to undefined]
**dataCollection** | [**Array&lt;SubstitutionsResponseModel&gt;**](SubstitutionsResponseModel.md) |  | [optional] [default to undefined]

## Example

```typescript
import { SubstitutionsResponseModelSearchResponseModel } from './api';

const instance: SubstitutionsResponseModelSearchResponseModel = {
    count,
    dataCollection,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
