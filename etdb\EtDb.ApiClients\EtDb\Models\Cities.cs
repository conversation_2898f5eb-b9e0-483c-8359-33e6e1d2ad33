﻿using System;
using System.Collections.Generic;

namespace EtDb.ApiClients.EtDb.Models
{
    public partial class Cities
    {
        public Cities()
        {
            Schenkers = new HashSet<Schenkers>();
            User = new HashSet<User>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public int Region { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime? DeletedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public string ModifiedBy { get; set; }
        public string SapcityCode { get; set; }
        public string Cluster { get; set; }

        public virtual ICollection<Schenkers> Schenkers { get; set; }
        public virtual ICollection<User> User { get; set; }
    }
}
