﻿using EtDb.Infrastructure.Interfaces;
using Net.Ams.Models;
using System;

namespace EtDb.ApiClients.EtDb.Models
{
    public partial class User : IDeletableEntity, IAuditInfo
    {

        public User(ApplicationUserModel cacheUser)
            : this()
        {
            this.SetNewGuidUser();
            this.UpdateEntry(cacheUser);
        }

        //public bool PreserveCreatedOn { get; set; }

        public void SetNewGuidUser()
        {
            this.Id = Guid.NewGuid().ToString();
        }

        public void UpdateEntry(ApplicationUserModel cacheUser)
        {
            string noData = "няма данни";

            this.Eln = cacheUser.ELN;
            this.FirstName = string.IsNullOrWhiteSpace(cacheUser.FirstName) ? noData : cacheUser.FirstName;
            this.Surname = string.IsNullOrWhiteSpace(cacheUser.Surname) ? noData : cacheUser.Surname;
            this.FamilyName = string.IsNullOrWhiteSpace(cacheUser.FamilyName) ? noData : cacheUser.FamilyName;
            this.FullName = string.IsNullOrWhiteSpace(cacheUser.FullName) ? noData : cacheUser.FullName;
            this.DisplayName = string.IsNullOrWhiteSpace(cacheUser.DisplayName) ? cacheUser.Username : cacheUser.DisplayName;
            this.Iptuname = cacheUser.BranchBg;
            this.Position = cacheUser.PositionBg;
            this.Adaccount = cacheUser.Username;
            this.Email = cacheUser.EMail;
        }

        public override bool Equals(object obj)
        {
            ApplicationUserModel u = obj as ApplicationUserModel;

            if (u == null)
            {
                return false;
            }

            return this.Eln == u.ELN && this.FirstName == u.FirstName &&
                this.Surname == u.Surname && this.FamilyName == u.FamilyName &&
                this.Iptuname == u.BranchBg && this.Position == u.PositionBg &&
                this.Adaccount == u.Username && this.Email == u.EMail;
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
    }
}

