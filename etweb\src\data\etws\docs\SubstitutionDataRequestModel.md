# SubstitutionDataRequestModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**opId** | **number** |  | [optional] [default to undefined]
**opCode** | **string** |  | [optional] [default to undefined]
**fromDate** | **string** |  | [optional] [default to undefined]
**toDate** | **string** |  | [optional] [default to undefined]
**forUserId** | **string** |  | [optional] [default to undefined]
**forUserFullName** | **string** |  | [optional] [default to undefined]
**substituteUserId** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { SubstitutionDataRequestModel } from './api';

const instance: SubstitutionDataRequestModel = {
    id,
    opId,
    opCode,
    fromDate,
    toDate,
    forUserId,
    forUserFullName,
    substituteUserId,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
