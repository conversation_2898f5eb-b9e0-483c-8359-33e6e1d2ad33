﻿namespace EtDb.Api.Controllers
{
    using EtDb.Api.Infrastructure.Utils;
    using EtDb.Models.Requests;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.UserModels;
    using EtDb.Services.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class SchenkerController : BaseApiController
    {
        private readonly Lazy<ISchenkerService> schenkerService;

        public SchenkerController(Lazy<ISchenkerService> schenkerService)
        {
            this.schenkerService = schenkerService;
        }

        [HttpGet]
        [ProducesResponseType(typeof(SchenkersResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SchenkersResponseModel>> GetSchenkerById([Required] int id)
        {
            try
            {
                var result = await this.schenkerService.Value.GetSchenkerById(id);
                return this.Ok(result);
            }
            catch (ArgumentException arg)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpGet("users")]
        [ProducesResponseType(typeof(IEnumerable<UserConciseResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<UserConciseResponseModel>>> GetSchenkerUsers([Required] int id)
        {
            try
            {
                var result = await this.schenkerService.Value.GetSchenkerUsers(id);
                return this.Ok(result);
            }
            catch (ArgumentException arg)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpGet("opcodes-list")]
        [ProducesResponseType(typeof(List<SchenkerOpCodeResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<List<SchenkerOpCodeResponseModel>>> GetSchenkerOpCodesList()
        {
            try
            {
                var result = await this.schenkerService.Value.GetSchenkerOpCodesList();
                return this.Ok(result);
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPost]
        [Route("search")]
        [ProducesResponseType(typeof(SearchResponseModel<SchenkersResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<SchenkersResponseModel>>> SearchProductsAsync([Required] SearchRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.schenkerService.Value.SearchSchenkers(request);
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (ArgumentException arg)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPost]
        [Route("insert")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> InsertProduct([Required] SchenkersRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var id = await this.schenkerService.Value.InsertSchenker(request);
                    return this.Ok(id);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (ArgumentException arg)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPut]
        [Route("update")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateProduct([Required] SchenkersRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    await this.schenkerService.Value.UpdateSchenker(request);
                    return this.Ok();
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (ArgumentException arg)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (DbUpdateException ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.InnerException?.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }
    }
}
