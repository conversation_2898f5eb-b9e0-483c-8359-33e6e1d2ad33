﻿{
  "apiVersion": "v1",
  "spring": {
    "application": {
      "name": "et-ws"
    },
    "cloud": {
      "config": {
        "uri": "http://cfgprodapp.btk.bg/configserverapi/v1/",
        "validateCertificates": false,
        "failFast": true,
        "health": {
          "enabled": true
        }
      }
    }
  },
  "management": {
    "endpoints": {
      "actuator": {
        "exposure": {
          "include": [ "refresh", "health", "info" ]
        }
      }
    }
  },
  "eureka": {
    "client": {
      "serviceUrl": "${endpoints:eureka}",
      "shouldFetchRegistry": true,
      "shouldRegisterWithEureka": true
    },
    "instance": {
      "healthCheckUrlPath": "/actuator/health",
      "statusPageUrlPath": "/actuator/info"
    }
  },
  "redis": {
    "client": {
      "connectionString": "${endpoints:keyDb}, name = ${spring:application:name}, password = AES$GCQ9v+YgWd8MHj1lL2wcWhZoMOyw+ginmOia2QNsbmqbnSKXsN5ev3L3Noen5DTH3+aE+CbZp8k8U1eYa1B/w5hJPjLuDGQfvPq7k1XvGdwxs9tftKSwje68LilkMDRS1n0bDIK6+PL24uVNJ9NRbje6hPeAgBPt0cptr86MpwI=, abortConnect = false, defaultDatabase = 0"
    }
  },
  "endpoints": {
    "etdb": "https://et-db/",
    "esbWs": "http://esb-ws/",
    "notificationsSender": "http://notifications-sender/"
  },
  "UserManager": {
    "CacheId": "EtWsUsers",
    "ExpirationInMinutes": 60,
    "FunctionalityAccessSeparator": "_",
    "Domains": [
      {
        "Alias": "AD",
        "Name": "ad.btk.bg",
        "Port": 389
      }
    ]
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Error",
        "System.Net.Http.HttpClient": "Error"
      }
    }
  },
  "InnerLoggingOptions": {
    "RequestPropertiesToRemove": [ "password" ],
    "SkipRequestsWithPathEndsWith": [ "/metrics", "/healthchecks-ui", "/healthchecks-ui-datasource", "/healthchecks-overview", "/healthchecks-api", "/healthchecks-api/ui-settings" ]
  },
  "OuterLoggingOptions": {
    "IsOuterLoggingEnabled": false
  }
}
