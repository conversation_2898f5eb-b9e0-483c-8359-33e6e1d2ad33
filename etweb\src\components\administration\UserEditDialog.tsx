
import { useUserById } from "@/hooks/useUsers";
import { useCitiesSelectList } from "@/hooks/useCities";
import { useSchenkersSelectList } from "@/hooks/useSchenkers";
import { LoadingState } from "@/components/ui/loading-state";
import { ErrorState } from "@/components/ui/error-state";
import UserEditForm from "./UserEditForm";

interface UserEditDialogProps {
  userId: string;
  onClose: () => void;
}

export function UserEditDialog({ userId, onClose }: UserEditDialogProps) {
  const { data: citiesData, isLoading: citiesLoading, refetch: refetchCities } = useCitiesSelectList();
  const { data: schenkersData, isLoading: schenkersLoading, refetch: refetchSchenkers } = useSchenkersSelectList();
  const { data: user, isLoading, isError, refetch: refetchUser } = useUserById(userId);

  const handleRetry = () => {
    refetchUser();
    refetchCities();
    refetchSchenkers();
  };

  if (isLoading || citiesLoading || schenkersLoading) {
    return <LoadingState/>
  }

  if (isError || !user || !citiesData || !schenkersData) {
    return (
      <ErrorState
        onRetry={handleRetry}
      />
    );
  }

  return (
    <UserEditForm
      userId={userId}
      user={user}
      citiesData={citiesData}
      schenkersData={schenkersData}
      onClose={onClose}
    />
  );
}