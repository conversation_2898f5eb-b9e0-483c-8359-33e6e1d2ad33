/**
 * Generic TableFilter component for filtering and sorting table data
 *
 * Supports SearchTypes enum operators:
 * - 0: Equals
 * - 1: Contains
 * - 2: Ends With
 * - 3: Starts With
 *
 * Usage example:
 * ```tsx
 * const columns: TableColumn[] = [
 *   { field: "name", label: "Name" },
 *   { field: "email", label: "Email" },
 *   {
 *     field: "status",
 *     label: "Status",
 *     type: "select",
 *     options: [
 *       { value: "active", label: "Active" },
 *       { value: "inactive", label: "Inactive" }
 *     ]
 *   }
 * ];
 *
 * <TableFilter
 *   columns={columns}
 *   filters={filters}
 *   onFiltersChange={setFilters}
 *   onQueryChange={setQueryString} // Optional: receives the built query string
 *   sortBy={sortBy}
 *   sortDir={sortDir}
 *   onSortChange={(sortBy, sortDir) => { setSortBy(sortBy); setSortDir(sortDir); }}
 *   sortOptions={sortOptions}
 * />
 * ```
 */

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { X, Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export interface ColumnFilter {
  field: string;
  operator: string;
  value: string;
}

export interface TableColumn {
  field: string;
  label: string;
  type?: "text" | "select";
  options?: { value: string; label: string }[];
}

interface TableFilterProps {
  columns: TableColumn[];
  filters: ColumnFilter[];
  onFiltersChange: (filters: ColumnFilter[]) => void;
  onQueryChange?: (query: string) => void;
  sortBy: string;
  sortDir: string;
  onSortChange: (sortBy: string, sortDir: string) => void;
  sortOptions: { value: string; label: string }[];
}

export function TableFilter({
  columns,
  filters,
  onFiltersChange,
  onQueryChange,
  sortBy,
  sortDir,
  onSortChange,
  sortOptions,
}: TableFilterProps) {
  const { t } = useTranslation();
  const [showFilters, setShowFilters] = useState(false);

  // Build query string from column filters
  const buildQueryString = (filterList: ColumnFilter[]) => {
    if (filterList.length === 0) return "";

    return filterList
      .filter((filter) => filter.value.trim() !== "")
      .map((filter) => `${filter.field}+${filter.operator}+${filter.value}`)
      .join(",");
  };

  const updateFilter = (field: string, operator: string, value: string) => {
    const existingFilterIndex = filters.findIndex(f => f.field === field);
    let newFilters: ColumnFilter[];

    if (value.trim() === "") {
      // Remove filter if value is empty
      if (existingFilterIndex >= 0) {
        newFilters = filters.filter(f => f.field !== field);
      } else {
        return; // No change needed
      }
    } else {
      const newFilter = { field, operator, value };

      if (existingFilterIndex >= 0) {
        // Update existing filter
        newFilters = [...filters];
        newFilters[existingFilterIndex] = newFilter;
      } else {
        // Add new filter
        newFilters = [...filters, newFilter];
      }
    }

    onFiltersChange(newFilters);

    // Call onQueryChange with the new query string
    if (onQueryChange) {
      onQueryChange(buildQueryString(newFilters));
    }
  };

  const removeFilter = (field: string) => {
    const newFilters = filters.filter(f => f.field !== field);
    onFiltersChange(newFilters);

    // Call onQueryChange with the new query string
    if (onQueryChange) {
      onQueryChange(buildQueryString(newFilters));
    }
  };

  const clearAllFilters = () => {
    onFiltersChange([]);

    // Call onQueryChange with empty string
    if (onQueryChange) {
      onQueryChange("");
    }
  };

  const getFilterValue = (field: string) => {
    return filters.find(f => f.field === field)?.value || "";
  };

  const getFilterOperator = (field: string) => {
    return filters.find(f => f.field === field)?.operator || "1";
  };

  // Helper function for select type filters
  const renderSelectInputType = (column: TableColumn, filterValue: string, inputId: string) => {
    return (
      <div className="flex gap-1 items-center flex-grow">
        <Select
          value={filterValue}
          onValueChange={(value) => updateFilter(column.field, "0", value)} // Use "0" (equals) for select fields
        >
          <SelectTrigger id={inputId} className="h-8 text-xs flex-grow">
            <SelectValue placeholder={`${t("select")}...`} />
          </SelectTrigger>
          <SelectContent>
            {column.options!.map((option) => (
              <SelectItem key={option.value} value={option.value} className="text-xs">
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {filterValue && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => removeFilter(column.field)}
            className="h-8 w-8 p-0 flex-shrink-0"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    );
  };

  // Helper function for text type filters
  const renderTextInputType = (column: TableColumn, filterValue: string, filterOperator: string, inputId: string) => {
    return (
      <div className="relative group flex-grow">
        <div className="flex items-center border border-input rounded-md bg-background hover:border-accent-foreground/30 transition-all duration-200">
          {/* Compact operator selector integrated as prefix */}
          <Select
            value={filterOperator}
            onValueChange={(operator) => updateFilter(column.field, operator, filterValue)}
          >
            <SelectTrigger className="border-0 h-7 w-10 text-xs bg-transparent rounded-r-none border-r border-border/50 focus:ring-0 hover:bg-accent/50 flex-shrink-0">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0" className="text-xs">≡</SelectItem>
              <SelectItem value="1" className="text-xs">∋</SelectItem>
              <SelectItem value="2" className="text-xs">$</SelectItem>
              <SelectItem value="3" className="text-xs">^</SelectItem>
            </SelectContent>
          </Select>
          
          {/* Input field seamlessly integrated */}
          <Input
            id={inputId}
            placeholder="..."
            value={filterValue}
            onChange={(e) => updateFilter(column.field, filterOperator, e.target.value)}
            className="border-0 h-7 text-xs bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 placeholder:text-muted-foreground/50 flex-grow rounded-none"
          />
          
          {/* Clear button - appears smoothly */}
          {filterValue && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => removeFilter(column.field)}
              className="h-5 w-5 p-0 mr-1 opacity-0 group-hover:opacity-100 hover:bg-destructive/10 hover:text-destructive transition-all duration-200 rounded-sm flex-shrink-0"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
        
        {/* Tooltip showing operator meaning */}
        {filterValue && (
          <div className="absolute -top-6 left-0 bg-popover text-popover-foreground text-xs px-2 py-0.5 rounded border shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-20 whitespace-nowrap">
            {filterOperator === "0" && "= " + t("equals")}
            {filterOperator === "1" && "∋ " + t("contains")}
            {filterOperator === "2" && "$ " + t("endsWith")}
            {filterOperator === "3" && "^ " + t("startsWith")}
          </div>
        )}
      </div>
    );
  };

  const renderFilterInput = (column: TableColumn, inputId: string) => {
    const filterValue = getFilterValue(column.field);
    const filterOperator = getFilterOperator(column.field);

    if (column.type === "select" && column.options) {
      return renderSelectInputType(column, filterValue, inputId);
    }

    // Default to text filter if type is not select or options are missing
    return renderTextInputType(column, filterValue, filterOperator, inputId);
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
          className="h-8"
        >
          <Filter className="h-3 w-3 mr-1" />
          {t("filters")} {filters.length > 0 && `(${filters.length})`}
        </Button>
        
        <div className="flex items-center gap-2 text-xs">
          <span className="text-muted-foreground">{t("sortBy")}:</span>
          <Select value={sortBy} onValueChange={(value) => onSortChange(value, sortDir)}>
            <SelectTrigger className="h-8 w-32 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value} className="text-xs">
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={sortDir} onValueChange={(value) => onSortChange(sortBy, value)}>
            <SelectTrigger className="h-8 w-20 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="asc" className="text-xs">{t("ascending")}</SelectItem>
              <SelectItem value="desc" className="text-xs">{t("descending")}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {showFilters && (
        <div className="border rounded-md p-3 space-y-3 bg-muted/20">
          <div className="flex items-center justify-between">
            <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
              {t("columnFilters")}
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              disabled={filters.length === 0}
              className="h-7 text-xs"
            >
              {t("clearAll")}
            </Button>
          </div>

          {/* Column Filters - More compact grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-x-4 gap-y-3">
            {columns.map((column) => {
              const inputId = `filter-input-${column.field}`;
              return (
                <div key={column.field} className="flex items-center gap-2">
                  <label htmlFor={inputId} className="text-xs font-medium text-muted-foreground whitespace-nowrap flex-shrink-0">
                    {column.label}:
                  </label>
                  <div className="flex-grow min-w-0">
                    {renderFilterInput(column, inputId)}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
