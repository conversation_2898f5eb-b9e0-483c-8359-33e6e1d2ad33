﻿namespace EtWS.Services.CriticalQuantitiesService
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;

    public class CriticalQuantitiesService : ICriticalQuantitiesService
    {
        private readonly Lazy<IETDB> eTDB;
        private readonly Lazy<IMapper> mapper;

        public CriticalQuantitiesService(Lazy<IETDB> eTDB, Lazy<IMapper> mapper)
        {
            this.eTDB = eTDB;
            this.mapper = mapper;
        }

        public async Task<QuantityCalculationObjectResponseModelSearchResponseModel> SearchQuantityCalculationObjectsAsync(SearchDataRequestModel request)
        {
            var dbRequest = this.mapper.Value.Map<SearchRequestModel>(request);

            var result = await this.eTDB.Value.ApiQuantityCalculationObjectSearchPostAsync(body: dbRequest);

            return result;
        }

        public async Task<QuantityCalculationObjectResponseModel> GetQuantityCalculationObjectByIdAsync(int id)
        {
            var result = await this.eTDB.Value.ApiQuantityCalculationObjectGetAsync(id);

            return result;
        }

        public async Task UpdateQuantityCalculationObjectAsync(QuantityCalculationObjectRequestModel request)
        {
            var result = await this.eTDB.Value.ApiQuantityCalculationObjectUpdatePostAsync(body: request);
        }
    }
}
