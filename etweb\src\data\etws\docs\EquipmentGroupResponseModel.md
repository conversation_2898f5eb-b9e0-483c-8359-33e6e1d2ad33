# EquipmentGroupResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [optional] [default to undefined]
**name** | **string** |  | [optional] [default to undefined]
**containsSubstitutes** | **boolean** |  | [optional] [default to undefined]

## Example

```typescript
import { EquipmentGroupResponseModel } from './api';

const instance: EquipmentGroupResponseModel = {
    id,
    name,
    containsSubstitutes,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
