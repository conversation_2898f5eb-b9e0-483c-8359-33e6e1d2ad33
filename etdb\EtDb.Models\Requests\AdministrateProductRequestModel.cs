﻿
namespace EtDb.Models.Requests
{
    public class AdministrateProductRequestModel
    {
        [Required(ErrorMessage = "IdRequired")]
        public int Id { get; set; }

        [Required(ErrorMessage = "NameRequired")]
        [StringLength(100, ErrorMessage = "NameValidate")]
        public string Name { get; set; }

        [Required(ErrorMessage = "SNRequired")]
        [StringLength(50, ErrorMessage = "SNValidate")]
        [RegularExpression(@"^\s*\S+\s*$", ErrorMessage = "InvalidSapMaterialNum")]
        public string SAPMaterialNum { get; set; }

        [Required(ErrorMessage = "EquipmentNameFirstRequired")]
        [StringLength(100, ErrorMessage = "NameValidate")]
        public string EquipmentNameFirst { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessage = "MaterialFirstRequired")]
        [StringLength(50, ErrorMessage = "SNValidate")]
        [RegularExpression(@"^\s*\S+\s*$", ErrorMessage = "SapMaterialNumInvalid")]
        public string MatFirst { get; set; }

        [Required(ErrorMessage = "MinQuantityRequired")]
        [Range(0, int.MaxValue, ErrorMessage = "MinQuantityRequired")]
        public int MinimumQuantity { get; set; }

        [Required(ErrorMessage = "SapRequestTypeRequired")]
        public int SapRequestType { get; set; }

        public string BRProjectName { get; set; }

        public string SapElementCode { get; set; }

        [Required(ErrorMessage = "EquipmentGroupIdRequired")]
        public int EquipmentGroupId { get; set; }

        [Required(ErrorMessage = "UnitOfMeasureRequired")]
        public int UnitOfMeasure { get; set; }

        public int? BoxCapacity { get; set; }
    }
}
