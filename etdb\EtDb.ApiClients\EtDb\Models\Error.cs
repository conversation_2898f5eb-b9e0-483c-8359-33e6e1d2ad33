﻿using System;
using System.Collections.Generic;

namespace EtDb.ApiClients.EtDb.Models
{
    public partial class Error
    {
        public int Id { get; set; }
        public string RequestId { get; set; }
        public string SourceReq { get; set; }
        public int? HistoryId { get; set; }
        public string RequestText { get; set; }
        public string ErrorCode { get; set; }
        public string ErrorText { get; set; }
        public int Quantity { get; set; }
        public string UserId { get; set; }
        public string User2Id { get; set; }

        public virtual History History { get; set; }
        public virtual User User { get; set; }
        public virtual User User2 { get; set; }
    }
}
