﻿namespace EtWS.Services.SchenkersService
{
    using System.Text;

    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.SchenkerModels;
    using EtWS.Models.Responses.UserModels;
    using EtWS.Services.Interfaces;
    using Microsoft.Extensions.Caching.Distributed;
    using Newtonsoft.Json;

    public class SchenkersService : ISchenkersService
    {
        private readonly Lazy<IETDB> etDb;
        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<IDistributedCache> distributedCache;

        public SchenkersService(
            Lazy<IETDB> etDb,
            Lazy<IMapper> mapper,
            Lazy<IDistributedCache> distributedCache)
        {
            this.etDb = etDb;
            this.mapper = mapper;
            this.distributedCache = distributedCache;
        }

        public async Task<SchenkerResponseModel> GetSchenkerByIdAsync(int id)
        {
            var schenker = await this.etDb.Value.ApiSchenkerGetAsync(id);
            return this.mapper.Value.Map<SchenkersResponseModel, SchenkerResponseModel>(schenker);
        }

        public async Task<IEnumerable<UserDataConciseResponseModel>> GetSchenkerUsersAsync(int schenkerId)
        {
            var users = await this.etDb.Value.ApiSchenkerUsersGetAsync(schenkerId);

            return this.mapper.Value.Map<ICollection<UserConciseResponseModel>, IEnumerable<UserDataConciseResponseModel>>(users);
        }

        public async Task<IEnumerable<SchenkerSelectItemResponseModel>> GetSchenkerOpCodesListAsync()
        {
            var cachedOpCodes = await this.distributedCache.Value.GetStringAsync($"{CacheConstants.EtWsSchenkersCacheKey}OpCodes");
            if (cachedOpCodes != null)
            {
                return JsonConvert.DeserializeObject<IEnumerable<SchenkerSelectItemResponseModel>>(cachedOpCodes);
            }

            var schenkersWithUsers = await this.etDb.Value.ApiSchenkerOpcodesListGetAsync();

            await this.distributedCache.Value.SetAsync(
                $"{CacheConstants.EtWsSchenkersCacheKey}OpCodes",
                Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(schenkersWithUsers)),
                new DistributedCacheEntryOptions { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(1) });

            return this.mapper.Value.Map<ICollection<SchenkerOpCodeResponseModel>, IEnumerable<SchenkerSelectItemResponseModel>>(schenkersWithUsers);
        }

        public async Task<SearchResponseModel<SchenkerResponseModel>> SearchSchenkersAsync(SearchDataRequestModel request)
        {
            var dbRequest = this.mapper.Value.Map<SearchRequestModel>(request);
            var filteredSchenkers = await this.etDb.Value.ApiSchenkerSearchPostAsync(body: dbRequest);
            var schenkersResponse = this.mapper.Value.Map<SearchResponseModel<SchenkerResponseModel>>(filteredSchenkers);

            return schenkersResponse;
        }

        public async Task<int> AddSchenkerAsync(SchenkerRequestModel request)
        {
            var dbRequest = this.mapper.Value.Map<SchenkersRequestModel>(request);
            return await this.etDb.Value.ApiSchenkerInsertPostAsync(body: dbRequest);
        }

        public async Task UpdateSchenkerAsync(SchenkerRequestModel request)
        {
            var dbRequest = this.mapper.Value.Map<SchenkersRequestModel>(request);
            await this.etDb.Value.ApiSchenkerUpdatePutAsync(body: dbRequest);
        }
    }
}
