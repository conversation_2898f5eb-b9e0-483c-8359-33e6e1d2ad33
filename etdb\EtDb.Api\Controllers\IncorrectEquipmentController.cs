﻿namespace EtDb.Api.Controllers
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.Models.Requests;
    using EtDb.Models.Requests.EquipmentModels;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.UserModels;
    using EtDb.Services.Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class IncorrectEquipmentController : BaseApiController
    {
        private readonly Lazy<IIncorrectEquipmentService> incorrectEquipmentService;

        public IncorrectEquipmentController(Lazy<IIncorrectEquipmentService> incorrectEquipmentService)
        {
            this.incorrectEquipmentService = incorrectEquipmentService;
        }

        [HttpPost("get-itemId")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> GetItemsByEquipmentSerialNums(string equipmentSerialNumber)
        {
            if (string.IsNullOrWhiteSpace(equipmentSerialNumber))
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Equipment serial number cannot be null or empty." });
            }

            try
            {
                var itemId = await this.incorrectEquipmentService.Value.GetItemIdBySerialNumberAsync(equipmentSerialNumber);

                if (itemId == null)
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = "No item found for the provided serial number." });
                }

                return this.Ok(itemId);
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }


        [HttpPut("update-items-addition-dates")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateAdditionDatesAsync([FromBody] int itemId)
        {
            if (itemId <= 0)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Invalid item ID." });
            }

            try
            {
                await this.incorrectEquipmentService.Value.UpdateItemAdditionDateAsync(itemId);

                return this.Ok(new BaseResponseModel { Success = true, Message = "AdditionDate updated successfully." });
            }
            catch (ArgumentException arg)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpGet("get-histories-by-itemId/{itemId}")]
        [ProducesResponseType(typeof(IEnumerable<History>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<History>>> GetHistoriesByItemId(int itemId)
        {
            try
            {
                var histories = await this.incorrectEquipmentService.Value.GetAllHistoriesByItemId(itemId);

                if (histories == null || !histories.Any())
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = $"No history record found with Id{itemId}" });
                }

                return this.Ok(histories);
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpPost]
        [Route("add-history")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> AddHistoryAsync([FromBody] HistoryRequestModel request)
        {
            if (request == null)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Request cannot be null." });
            }

            try
            {
                var historyId = await this.incorrectEquipmentService.Value.AddHistoryAsync(request);
                return this.Ok(historyId);
            }
            catch (DbUpdateException dbEx)
            {
                return this.BadRequest("Database error: " + dbEx.Message);
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpPut("update-available-equipment-quantity")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateAvailableEquipmentQuantity([FromBody] AvailableEquipmentRequestModel request)
        {
            if (request.ItemQuantity < 0 || request.ItemQuantity > 1)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Quantity cannot be negative or bigger than 1." });
            }

            try
            {
                var updated = await this.incorrectEquipmentService.Value.UpdateAvailableEquipmentById(request);

                if (!updated)
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = "Equipment not exist" });
                }

                return this.Ok(new BaseResponseModel { Success = true, Message = "Equipment Quantity updated successfully." });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpPost("add-status-history")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> AddStatusHistoryAsync([FromBody] StatusHistoryRequestModel request)
        {
            if (request == null)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Request cannot be null." });
            }

            try
            {
                await this.incorrectEquipmentService.Value.AddStatusHistoryAsync(request);

                return this.Ok(new BaseResponseModel { Success = true, Message = "Status history added successfully." });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpDelete("delete-history/{id}")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult> DeleteHistoryByIdAsync(int id)
        {
            try
            {
                var result = await this.incorrectEquipmentService.Value.DeleteHistoryByIdAsync(id);

                if (!result)
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = $"No history record found with Id {id}." });
                }

                return this.Ok(new BaseResponseModel { Success = true, Message = "History record deleted successfully." });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpDelete("delete-status-histories/{historyId}")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult> DeleteStatusHistoriesAsync(int historyId)
        {
            try
            {
                var result = await this.incorrectEquipmentService.Value.DeleteStatusHistoryAsync(historyId);

                if (!result)
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = $"No status histories found for HistoryId {historyId}." });
                }

                return this.Ok(new BaseResponseModel { Success = true, Message = $"All status histories with HistoryId {historyId} deleted successfully." });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpDelete("delete-errors-by-history/{historyId}")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult> DeleteErrorsByHistoryIdAsync(int historyId)
        {
            try
            {
                var result = await this.incorrectEquipmentService.Value.DeleteErrorsByHistoryIdAsync(historyId);

                if (!result)
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = $"No errors found for HistoryId {historyId}." });
                }

                return this.Ok(new BaseResponseModel { Success = true, Message = $"All errors with HistoryId {historyId} deleted successfully." });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpPost("get-technician-id")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<string>> GetTechnicianIdByName([FromBody] UserRequestModel request)
        {
            if (request == null || string.IsNullOrEmpty(request.ТechnicianName))
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Invalid request data. TechnicianName is required." });
            }

            try
            {
                var technicianId = await this.incorrectEquipmentService.Value.GetTechnicianIdByNameAsync(request);

                if (string.IsNullOrEmpty(technicianId))
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = "Technician not found." });
                }

                return this.Ok(technicianId);
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpGet("get-all-users-with-opcode")]
        [ProducesResponseType(typeof(IEnumerable<UsersWithOpCodeResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<IEnumerable<UsersWithOpCodeResponseModel>>> GetAllUsersWithOpCode()
        {
            try
            {
                var users = await this.incorrectEquipmentService.Value.GetAllUsersWithOpCodeAsync();

                if (users == null || !users.Any())
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = "No users with OPCode found." });
                }

                return this.Ok(users);
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpPost("get-available-equipment-id")]
        [ProducesResponseType(typeof(int?), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int?>> GetAvailableEquipmentId([FromBody] AvailableEquipmentRequestModel request)
        {
            if (request == null || request.ItemId == 0 || string.IsNullOrEmpty(request.UserId))
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Invalid request data." });
            }

            try
            {
                var equipmentId = await this.incorrectEquipmentService.Value.GetAvailableEquipmentIdByTechnicianNameAndItemIdAsync(request);

                if (!equipmentId.HasValue)
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = "No available equipment found for the provided technician and item Id." });
                }

                return this.Ok(equipmentId);
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpPost("add-available-equipment")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> AddAvailableEquipment([FromBody] AvailableEquipmentRequestModel request)
        {
            if (request == null || request.ItemId == 0 || string.IsNullOrEmpty(request.UserId))
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Invalid request data." });
            }

            try
            {
                await this.incorrectEquipmentService.Value.AddAvailableEquipmentAsync(request);

                return this.StatusCode(StatusCodes.Status201Created, new BaseResponseModel { Success = true, Message = "Available equipment added successfully." });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpPut("update-available-equipment-to-zero/{itemId}")]
        [ProducesResponseType(typeof(int?), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateAvailableEquipmentToZero(int itemId)
        {
            if (itemId <= 0)
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Invalid item ID." });
            }

            try
            {
                bool updated = await this.incorrectEquipmentService.Value.UpdateAvailableEquipmentToZeroAsync(itemId);

                if (!updated)
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = "No available equipment found for the given item ID." });
                }

                return this.Ok(new BaseResponseModel { Success = true, Message = "Available equipment updated successfully." });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpGet("check-serial-number/{equipmentSerialNum}")]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<bool>> CheckSerialNumberExistsAsync(string equipmentSerialNum)
        {
            if (string.IsNullOrWhiteSpace(equipmentSerialNum))
            {
                return this.BadRequest(new BaseResponseModel { Success = false, Message = "Equipment serial number cannot be null or empty." });
            }

            try
            {
                var exists = await this.incorrectEquipmentService.Value.DoesSerialNumberExistAsync(equipmentSerialNum);

                if (!exists)
                {
                    return this.NotFound(new BaseResponseModel { Success = false, Message = "Serial number not found." });
                }

                return this.Ok(new BaseResponseModel { Success = true, Message = "Serial number exists." });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }

        [HttpGet("user-and-iptu-names")]
        [ProducesResponseType(typeof(IEnumerable<UserResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<UserResponseModel>>> GetUserDisplayAndIptuNames()
        {

            try
            {
                var displayNames = await this.incorrectEquipmentService.Value.GetUserDisplayAndIptuNames();

                if (!displayNames.Any())
                {
                    return this.BadRequest(new BaseResponseModel { Success = false, Message = "No user was found" });
                }

                return this.Ok(displayNames);
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex.Message });
            }
        }
    }
}
