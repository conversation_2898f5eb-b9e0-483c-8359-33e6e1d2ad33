﻿namespace EtDb.Services.Interfaces
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Enums;
    using EtDb.DataHandlers.Models;
    using EtDb.Models.Requests;
    using EtDb.Models.Requests.EquipmentModels;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.EquipmentModels;
    using EtDb.Models.Responses.UserModels;

    public interface IEquipmentService : IService
    {
        Task<List<SapmatMapp>> GetAllSapmatMappAsync();

        Task<SearchResponseModel<AvailableEquipmentResponseModel>> SearchAvailableEquipmentAsync(SearchRequestModelWithUserId request);

        Task<ReserveItemHistoryResponseModel> ReserveItemForTransferAsync(int itemId, string userId, int quantity);

        Task<ReserveItemHistoryResponseModel> ReserveItemForTransferAsync(string itemSerialNumber, string userId, int quantity);

        Task<int> CountAllUserReservedItemsForTransfer(string userId);

        Task<IEnumerable<AvailableEquipmentResponseModel>> GetAllUserReservedItemsForTransferAsync(string userId);

        Task ReserveAllItemsForTransferAsync(string userId);

        Task<string> RemoveItemAsync(int itemId, string userId);

        Task RemoveItemsAsync(IEnumerable<int> selectedItemIds, string userId);

        Task RemoveAllItemsAsync(string userId);

        Task UpdateAvailableEquipmentsAsync(IList<AvailableEquipmentRequestModel> availableEquipments);

        Task<SearchResponseModel<ItemResponseModel>> SearchTransferDataAsync(SearchRequestModelWithUserId request);

        Task<SearchResponseModel<AvailableEquipmentResponseModel>> SearchDailyEquipmentAsync(SearchRequestModelWithUserId request);

        Task DeliverItemsAsync(DeliverItemsRequestModel request);

        Task<IEnumerable<PostOfficesSelectListResponseModel>> GetAllActivePostOfficesAsync();

        Task<SearchResponseModel<UserItemsToAcceptResponseModel>> SearchUserItemsToAcceptDataAsync(SearchRequestModelWithUserId request);

        Task<IDictionary<string, TransferedItemsModel>> RefuseItemsAsync(IEnumerable<int> selectedItems, RefuseReasonsList? refuseReason, string userId);

        Task<IDictionary<string, TransferedItemsModel>> AcceptItemsAsync(IEnumerable<int> selectedItems, string userId);

        Task<SearchResponseModel<UserItemsToCancelResponseModel>> SearchUserItemsToCancelDataAsync(SearchRequestModelWithUserId request);

        Task<IDictionary<string, TransferedItemsModel>> CancelItemsAsync(IEnumerable<int> selectedItems, RefuseReasonsList? refuseReason, string userId);
    }
}
