﻿namespace EtDb.DataHandlers.Interfaces
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Models;
    using EtDb.Infrastructure.Enumerations;

    public interface IEquipmentTypeDataHandler
    {
        Task<EquipmentType> GetEquipmentTypeByIdAsync(int id);

        IQueryable<EquipmentType> GetEquipmentTypes();

        Task<int> GetEquipmentTypeIdAsync(string sapMaterialNum);

        Task<IEnumerable<EquipmentType>> GetEquipmentTypesAsync(SendMethod sendMethod);

        IEnumerable<EquipmentTypeConciseDto> GetConciseEquipmentTypes(IEnumerable<int> equipmentTypeIds, bool serialNumbersRequired = true);

        Task<IDictionary<string, int>> GetEquipmentTypesLastMonthQuantitiesAsync();
    }
}
