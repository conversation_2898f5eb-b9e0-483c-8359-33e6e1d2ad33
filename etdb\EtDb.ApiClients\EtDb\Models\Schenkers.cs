﻿using System;
using System.Collections.Generic;

namespace EtDb.ApiClients.EtDb.Models
{
    public partial class Schenkers
    {
        public Schenkers()
        {
            AvailableEquipmentByOpsnapshots = new HashSet<AvailableEquipmentByOpsnapshots>();
            QuantityCalculationObject = new HashSet<QuantityCalculationObject>();
            Transfers = new HashSet<Transfers>();
            User = new HashSet<User>();
        }

        public int Id { get; set; }
        public string Opcode { get; set; }
        public int TransportArea { get; set; }
        public int ProcessingTime { get; set; }
        public int ProtectiveTime { get; set; }
        public string Molid { get; set; }
        public string Address { get; set; }
        public int CityId { get; set; }
        public int? WarehouseId { get; set; }
        public bool? IncludedInMonthlyCalculations { get; set; }

        public virtual Cities City { get; set; }
        public virtual User Mol { get; set; }
        public virtual Warehouses Warehouse { get; set; }
        public virtual ICollection<AvailableEquipmentByOpsnapshots> AvailableEquipmentByOpsnapshots { get; set; }
        public virtual ICollection<QuantityCalculationObject> QuantityCalculationObject { get; set; }
        public virtual ICollection<Transfers> Transfers { get; set; }
        public virtual ICollection<User> User { get; set; }
    }
}
