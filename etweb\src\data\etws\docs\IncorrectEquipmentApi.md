# IncorrectEquipmentApi

All URIs are relative to *http://microit9app1.drcenter.btk.bg:26121/et-ws*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet**](#apiincorrectequipmentcheckserialnumberbyequipmentserialnumget) | **GET** /api/incorrect-equipment/check-serial-number/{equipmentSerialNum} | |
|[**apiIncorrectEquipmentCheckServiceIdByServiceIdGet**](#apiincorrectequipmentcheckserviceidbyserviceidget) | **GET** /api/incorrect-equipment/check-service-id/{serviceId} | |
|[**apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet**](#apiincorrectequipmentgethistoriesbyequipmentserialnumberget) | **GET** /api/incorrect-equipment/get-histories/{equipmentSerialnumber} | |
|[**apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet**](#apiincorrectequipmentgetuserdisplaynamesbynameprefixget) | **GET** /api/incorrect-equipment/get-user-display-names/{namePrefix} | |
|[**apiIncorrectEquipmentTransferCorrectionPost**](#apiincorrectequipmenttransfercorrectionpost) | **POST** /api/incorrect-equipment/transfer-correction | |
|[**apiIncorrectEquipmentTransferDeleteDelete**](#apiincorrectequipmenttransferdeletedelete) | **DELETE** /api/incorrect-equipment/transfer-delete | |
|[**apiIncorrectEquipmentUsersWithOpcodesGet**](#apiincorrectequipmentuserswithopcodesget) | **GET** /api/incorrect-equipment/users-with-opcodes | |

# **apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet**
> BaseResponseModel apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet()


### Example

```typescript
import {
    IncorrectEquipmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new IncorrectEquipmentApi(configuration);

let equipmentSerialNum: string; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet(
    equipmentSerialNum,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **equipmentSerialNum** | [**string**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**BaseResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiIncorrectEquipmentCheckServiceIdByServiceIdGet**
> BaseResponseModel apiIncorrectEquipmentCheckServiceIdByServiceIdGet()


### Example

```typescript
import {
    IncorrectEquipmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new IncorrectEquipmentApi(configuration);

let serviceId: string; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiIncorrectEquipmentCheckServiceIdByServiceIdGet(
    serviceId,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **serviceId** | [**string**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**BaseResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet**
> Array<HistoriesResponse> apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet()


### Example

```typescript
import {
    IncorrectEquipmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new IncorrectEquipmentApi(configuration);

let equipmentSerialnumber: string; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet(
    equipmentSerialnumber,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **equipmentSerialnumber** | [**string**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**Array<HistoriesResponse>**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet**
> Array<UserDisplayNameResponseModel> apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet()


### Example

```typescript
import {
    IncorrectEquipmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new IncorrectEquipmentApi(configuration);

let namePrefix: string; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet(
    namePrefix,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **namePrefix** | [**string**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**Array<UserDisplayNameResponseModel>**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiIncorrectEquipmentTransferCorrectionPost**
> BaseResponseModel apiIncorrectEquipmentTransferCorrectionPost()


### Example

```typescript
import {
    IncorrectEquipmentApi,
    Configuration,
    TransferCorrectionRequest
} from './api';

const configuration = new Configuration();
const apiInstance = new IncorrectEquipmentApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)
let transferCorrectionRequest: TransferCorrectionRequest; // (optional)

const { status, data } = await apiInstance.apiIncorrectEquipmentTransferCorrectionPost(
    inputRequestId,
    inputTimestamp,
    transferCorrectionRequest
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **transferCorrectionRequest** | **TransferCorrectionRequest**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**BaseResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiIncorrectEquipmentTransferDeleteDelete**
> BaseResponseModel apiIncorrectEquipmentTransferDeleteDelete()


### Example

```typescript
import {
    IncorrectEquipmentApi,
    Configuration,
    DeleteTransferRequest
} from './api';

const configuration = new Configuration();
const apiInstance = new IncorrectEquipmentApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)
let deleteTransferRequest: DeleteTransferRequest; // (optional)

const { status, data } = await apiInstance.apiIncorrectEquipmentTransferDeleteDelete(
    inputRequestId,
    inputTimestamp,
    deleteTransferRequest
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **deleteTransferRequest** | **DeleteTransferRequest**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**BaseResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiIncorrectEquipmentUsersWithOpcodesGet**
> Array<UsersWithOPCodeResponse> apiIncorrectEquipmentUsersWithOpcodesGet()


### Example

```typescript
import {
    IncorrectEquipmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new IncorrectEquipmentApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiIncorrectEquipmentUsersWithOpcodesGet(
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**Array<UsersWithOPCodeResponse>**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

