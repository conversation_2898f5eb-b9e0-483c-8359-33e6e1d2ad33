# CityResponseModelSearchResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**count** | **number** |  | [optional] [default to undefined]
**dataCollection** | [**Array&lt;CityResponseModel&gt;**](CityResponseModel.md) |  | [optional] [default to undefined]

## Example

```typescript
import { CityResponseModelSearchResponseModel } from './api';

const instance: CityResponseModelSearchResponseModel = {
    count,
    dataCollection,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
