// Custom function to validate serial number
export const validateSerialNumber = async (value: string): Promise<boolean> => {
  // Basic client-side validation first
  if (!value || value.length < 8) {
    return false;
  }

  // Simulate API call with artificial delay
  try {
    // Simulate network delay (300-700ms)
    await new Promise((resolve) => setTimeout(resolve, Math.floor(Math.random() * 400) + 300));

    // Simulate server validation logic
    // For demo purposes: consider serial numbers that contain "INVALID" to be invalid
    const simulatedServerCheck = !value.includes("INVALID");

    // Additional fake validation rules
    const hasLettersAndNumbers = /[A-Za-z].*[0-9]|[0-9].*[A-Za-z]/.test(value);

    return simulatedServerCheck && hasLettersAndNumbers;
  } catch (error) {
    console.error("Serial number validation failed:", error);
    return false;
  }
};
