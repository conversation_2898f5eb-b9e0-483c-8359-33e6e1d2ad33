﻿namespace EtDb.Services.Interfaces
{
    using EtDb.Models.Requests;
    using EtDb.Models.Responses;

    public interface IProductService : IService
    {
        Task<AdministrateProductResponseModel> GetProductById(int id);

        Task<SearchResponseModel<AdministrateProductResponseModel>> SearchProducts(SearchRequestModel request);

        Task<int> InsertProduct(AdministrateProductRequestModel request);

        Task UpdateProduct(AdministrateProductRequestModel request);
    }
}
