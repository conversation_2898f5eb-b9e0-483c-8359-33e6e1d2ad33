# SchenkerResponseModelSearchResponseModel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**count** | **number** |  | [optional] [default to undefined]
**dataCollection** | [**Array&lt;SchenkerResponseModel&gt;**](SchenkerResponseModel.md) |  | [optional] [default to undefined]

## Example

```typescript
import { SchenkerResponseModelSearchResponseModel } from './api';

const instance: SchenkerResponseModelSearchResponseModel = {
    count,
    dataCollection,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
