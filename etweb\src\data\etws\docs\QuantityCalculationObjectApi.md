# QuantityCalculationObjectApi

All URIs are relative to *http://microit9app1.drcenter.btk.bg:26121/et-ws*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**apiQuantityCalculationObjectByIdGet**](#apiquantitycalculationobjectbyidget) | **GET** /api/quantity-calculation-object/{id} | |
|[**apiQuantityCalculationObjectSearchPost**](#apiquantitycalculationobjectsearchpost) | **POST** /api/quantity-calculation-object/search | |
|[**apiQuantityCalculationObjectUpdatePost**](#apiquantitycalculationobjectupdatepost) | **POST** /api/quantity-calculation-object/update | |

# **apiQuantityCalculationObjectByIdGet**
> QuantityCalculationObjectResponseModel apiQuantityCalculationObjectByIdGet()


### Example

```typescript
import {
    QuantityCalculationObjectApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new QuantityCalculationObjectApi(configuration);

let id: number; // (default to undefined)
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiQuantityCalculationObjectByIdGet(
    id,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**QuantityCalculationObjectResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**404** | Not Found |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiQuantityCalculationObjectSearchPost**
> QuantityCalculationObjectResponseModelSearchResponseModel apiQuantityCalculationObjectSearchPost(searchDataRequestModel)


### Example

```typescript
import {
    QuantityCalculationObjectApi,
    Configuration,
    SearchDataRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new QuantityCalculationObjectApi(configuration);

let searchDataRequestModel: SearchDataRequestModel; //
let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.apiQuantityCalculationObjectSearchPost(
    searchDataRequestModel,
    inputRequestId,
    inputTimestamp
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **searchDataRequestModel** | **SearchDataRequestModel**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**QuantityCalculationObjectResponseModelSearchResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **apiQuantityCalculationObjectUpdatePost**
> BaseResponseModel apiQuantityCalculationObjectUpdatePost()


### Example

```typescript
import {
    QuantityCalculationObjectApi,
    Configuration,
    QuantityCalculationObjectRequestModel
} from './api';

const configuration = new Configuration();
const apiInstance = new QuantityCalculationObjectApi(configuration);

let inputRequestId: string; // (optional) (default to undefined)
let inputTimestamp: string; // (optional) (default to undefined)
let quantityCalculationObjectRequestModel: QuantityCalculationObjectRequestModel; // (optional)

const { status, data } = await apiInstance.apiQuantityCalculationObjectUpdatePost(
    inputRequestId,
    inputTimestamp,
    quantityCalculationObjectRequestModel
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **quantityCalculationObjectRequestModel** | **QuantityCalculationObjectRequestModel**|  | |
| **inputRequestId** | [**string**] |  | (optional) defaults to undefined|
| **inputTimestamp** | [**string**] |  | (optional) defaults to undefined|


### Return type

**BaseResponseModel**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json, text/json, application/*+json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**500** | Server Error |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |
|**200** | Success |  * Output-Request-Id -  <br>  * Output-Timestamp -  <br>  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

