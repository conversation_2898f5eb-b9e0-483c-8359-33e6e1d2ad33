﻿using ET.Database.Enums;
using ET.DataHandlers.Models;
using EtDb.Api.Infrastructure.Utils;
using EtDb.ApiClients.EtDb.Models;
using EtDb.DataHandlers.Models;
using EtDb.Models.Requests.TransferModels;
using EtDb.Models.Responses;
using EtDb.Services.Interfaces;

namespace EtDb.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TransfersController : BaseApiController
    {
        private readonly Lazy<ITransfersService> transfersService;

        public TransfersController(Lazy<ITransfersService> transfersService)
        {
            this.transfersService = transfersService;
        }

        [HttpGet("by-op-within-date-range")]
        [ProducesResponseType(typeof(IDictionary<string, IEnumerable<TransferListItemWithDate>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public ActionResult<IDictionary<string, IEnumerable<TransferListItemWithDate>>> GetTransferListItemsByOpWithinDateRange(
            [FromQuery] DateTime fromDate,
            [FromQuery] DateTime toDate,
            [FromQuery] TransferStatus status)
        {
            try
            {
                var result = this.transfersService.Value.GetTransferListItemsByOpWithinDateRange(fromDate, toDate, status);
                return this.Ok(result);
            }
            catch (ArgumentException arg)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpPost("grid-data")]
        [ProducesResponseType(typeof(FilteredDataModel<Transfers>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<FilteredDataModel<Transfers>>> GetTransfersGridData([FromBody] TransfersGridRequestModel model)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.transfersService.Value.GetTransfersGridData(model);
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }

        [HttpGet("list-items-by-send-method")]
        [ProducesResponseType(typeof(IList<TransferListItems>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IList<TransferListItems>>> GetTransferListItemsBySendMethod(
            [FromQuery] int transferId,
            [FromQuery] bool bRProject)
        {
            try
            {
                var result = await this.transfersService.Value.GetTransferListItemsBySendMethodAsync(transferId, bRProject);
                return this.Ok(result);
            }
            catch (ArgumentException arg)
            {
                return this.NotFound(new BaseResponseModel { Success = false, Message = arg.Message });
            }
            catch (Exception ex)
            {
                return this.StatusCode(StatusCodes.Status500InternalServerError, new BaseResponseModel { Success = false, Message = ex?.Message });
            }
        }
    }
}