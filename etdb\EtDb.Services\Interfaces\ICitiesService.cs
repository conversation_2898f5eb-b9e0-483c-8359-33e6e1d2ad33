﻿namespace EtDb.Services.Interfaces
{
    using EtDb.Models.Requests;
    using EtDb.Models.Responses;

    public interface ICitiesService : IService
    {
        Task<CitiesResponseModel> GetCityAsync(int id);

        Task<SearchResponseModel<CitiesResponseModel>> GetCitiesAsync(SearchRequestModel request);

        Task<int> AddCityAsync(CitiesRequestModel request);

        Task UpdateCityAsync(int id, CitiesRequestModel request);

        Task BlockCitiesAsync(List<int> cityIds);

        Task ActivateCitiesAsync(List<int> cityIds);

        Task<string> GetCityRegionByIdAsync(int cityId);
    }
}
