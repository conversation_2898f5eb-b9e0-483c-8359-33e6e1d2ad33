﻿namespace EtWS.Services.EquipmentGroupService
{
    using EtWS.ApiClients.ETDB;

    public class EquipmentGroupService : IEquipmentGroupService
    {
        private readonly Lazy<IETDB> etDb;

        public EquipmentGroupService(Lazy<IETDB> etDb)
        {
            this.etDb = etDb;
        }

        public async Task<IEnumerable<EquipmentGroupResponseModel>> GetEquipmentGroupsAsync()
        {
            return await this.etDb.Value.ApiEquipmentGroupAllGetAsync();
        }

        public async Task<IEnumerable<string>> GetEquipmentGroupsNamesAsync()
        {
            return await this.etDb.Value.ApiEquipmentGroupAllNamesGetAsync();
        }

        public async Task<string> GetEquipmentGroupNameAsync(string sapMaterialNum)
        {
            return await this.etDb.Value.ApiEquipmentGroupNameBySapMaterialNumGetAsync(sapMaterialNum);
        }

        public async Task<EquipmentGroupResponseModel> GetEquipmentGroupByIdAsync(int equipmentGroupId)
        {
            return await this.etDb.Value.ApiEquipmentGroupByIdByIdGetAsync(equipmentGroupId);
        }
    }
}
