﻿using ET.Database.Enums;
using ET.DataHandlers.Models;
using EtDb.ApiClients.EtDb.Models;
using EtDb.DataHandlers.Interfaces;
using EtDb.DataHandlers.Models;
using EtDb.Infrastructure.Enumerations;
using EtDb.Models.Requests.TransferModels;
using EtDb.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace EtDb.Services
{
    public class TransfersService : ITransfersService
    {
        private readonly Lazy<IMapper> mapper;
        private readonly Lazy<ITransfersDataHandler> transfersDataHandler;

        public TransfersService(Lazy<IMapper> mapper, Lazy<ITransfersDataHandler> transfersDataHandler)
        {
            this.mapper = mapper;
            this.transfersDataHandler = transfersDataHandler;

        }

        public IDictionary<string, IEnumerable<TransferListItemWithDate>> GetTransferListItemsByOpWithinDateRange(DateTime fromDate, DateTime toDate, TransferStatus status)
        {
            var transferListItemsByOp = this.transfersDataHandler.Value.GetAllTransferListItemsByOpFilteredBy(
                x => fromDate <= x.DeliveryDate && x.DeliveryDate <= toDate &&
                    x.Status == (int)status);

            return transferListItemsByOp;
        }

        public async Task<FilteredDataModel<Transfers>> GetTransfersGridData(TransfersGridRequestModel model)
        {
            var transfers = this.transfersDataHandler.Value.GetAllTransfers();
            if (!string.IsNullOrEmpty(model.OpCode))
            {
                transfers = transfers.Where(t => t.RequestNumber == model.OpCode);
            }

            if (model.FromDate.HasValue)
            {
                transfers = transfers.Where(t => t.DeliveryDate >= model.FromDate.Value);
            }

            if (model.ToDate.HasValue)
            {
                transfers = transfers.Where(t => t.DeliveryDate <= model.ToDate.Value);
            }

            if (model.Status.HasValue)
            {
                transfers = transfers.Where(t => t.Status == (int)model.Status.Value);
            }

            return await this.transfersDataHandler.Value.GetTransfersGridDataModel(model.SortBy, model.Dir, model.Page, model.PageDataSize, model.Query, transfers);
        }

        public async Task<IList<TransferListItems>> GetTransferListItemsBySendMethodAsync(int transferId, bool bRProject)
        {
            if (bRProject)
            {
                var transferListItems = await this.transfersDataHandler.Value.GetTransferListItemsByTransferId(transferId)
                    .Where(x => x.EquipmentType.SendMethod == (int)SendMethod.BRProject)
                    .ToListAsync();
                return transferListItems;
            }
            else
            {
                var transferListItems = await this.transfersDataHandler.Value.GetTransferListItemsByTransferId(transferId)
                    .Where(x => x.EquipmentType.SendMethod != (int)SendMethod.BRProject)
                     .ToListAsync();
                return transferListItems;
            }
        }
    }
}
