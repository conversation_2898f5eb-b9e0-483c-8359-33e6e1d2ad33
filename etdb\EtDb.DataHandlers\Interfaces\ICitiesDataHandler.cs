﻿using EtDb.DataHandlers.Models;

namespace EtDb.DataHandlers.Interfaces
{
    public interface ICitiesDataHandler
    {
        Task<CitiesDto> GetCityByIdAsync(int id);

        Task<FilteredDataModel<CitiesDto>> GetFilteredCitiesAsync(string sortBy, string sortDir, int pageNumber, int pageSize, string query);

        Task<int> AddCityAsync(CitiesDto request);

        Task UpdateCityAsync(CitiesDto request);

        Task BlockCitiesAsync(List<int> ids);

        Task ActivateCitiesAsync(List<int> ids);

        Task<string> GetCityRegionByIdAsync(int id);
    }
}
