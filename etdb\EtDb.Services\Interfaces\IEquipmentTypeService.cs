﻿namespace EtDb.Services.Interfaces
{
    using EtDb.DataHandlers.Models;
    using EtDb.Infrastructure.Enumerations;
    using EtDb.Models.Responses.EquipmentModels;

    public interface IEquipmentTypeService : IService
    {
        Task<EquipmentTypeResponseModel> GetEquipmentTypeByIdAsync(int id);

        Task<IEnumerable<EquipmentTypeResponseModel>> GetEquipmentTypesAsync();

        Task<int> GetEquipmentTypeIdAsync(string sapMaterialNum);

        Task<IEnumerable<EquipmentTypeResponseModel>> GetEquipmentTypesAsync(SendMethod sendMethod);

        Task<IEnumerable<EquipmentTypeConciseDto>> GetConciseEquipmentTypesAsync(IEnumerable<int> equipmentTypeIds, bool serialNumbersRequired);

        Task<IDictionary<string, int>> GetEquipmentTypesLastMonthQuantitiesAsync();
    }
}
