﻿namespace EtWS.Api.Controllers
{
    using System.Security.Claims;

    using EtWS.Api.Infrastructure.Utils;
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.UserModels;
    using EtWS.Services.UserManagerService;
    using Microsoft.AspNetCore.Authentication.Cookies;
    using Microsoft.AspNetCore.Authorization;
    using Net.Ams.Interfaces;

    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class UsersController : BaseController
    {
        private readonly Lazy<IUserManagerService> userManagerService;
        private readonly Lazy<IUserManager> userManager;

        public UsersController(Lazy<IUserManagerService> userManagerService, Lazy<IUserManager> userManager)
        {
            this.userManagerService = userManagerService;
            this.userManager = userManager;
        }

        [HttpGet("{userId}")]
        [ProducesResponseType(typeof(SearchUserDataResponseModel), StatusCodes.Status200OK)]
        public async Task<ActionResult<SearchUserDataResponseModel>> GetUserByUserIdAsync([Required, FromRoute] string userId)
        {
            try
            {
                return this.Ok(await this.userManagerService.Value.GetUserByUserIdAsync(userId));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("user-by-username/{username}")]
        [ProducesResponseType(typeof(SearchUserDataResponseModel), StatusCodes.Status200OK)]
        public async Task<ActionResult<SearchUserDataResponseModel>> GetUserByUsernameAsync([Required, FromRoute] string username)
        {
            try
            {
                return this.Ok(await this.userManagerService.Value.GetUserByUsernameAsync(username));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("ad-account/{userId}")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        public async Task<ActionResult<string>> GetUserADAccountAsync([Required, FromRoute] string userId)
        {
            try
            {
                return this.Ok(await this.userManagerService.Value.GetUserADAccountAsync(userId));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("users-select-list")]
        [ProducesResponseType(typeof(UsersListResponseModel), StatusCodes.Status200OK)]
        public async Task<ActionResult<UsersListResponseModel>> GetUsersSelectListAsync()
        {
            try
            {
                return this.Ok(await this.userManagerService.Value.GetUsersSelectListAsync(this.User.Claims
                    .Where(c => c.Type == ClaimTypes.Role)
                    .Select(c => c.Value)
                    .ToList()));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("is-user-mol/{userId}")]
        [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
        public async Task<ActionResult<bool>> IsUserMolAsync([Required, FromRoute] string userId)
        {
            try
            {
                return this.Ok(await this.userManagerService.Value.IsUserMolAsync(userId));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("all-mols")]
        [ProducesResponseType(typeof(ICollection<UserDataConciseResponseModel>), StatusCodes.Status200OK)]
        public async Task<ActionResult<ICollection<UserDataConciseResponseModel>>> GetAllMolsAsync()
        {
            try
            {
                return this.Ok(await this.userManagerService.Value.GetAllMolsAsync());
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("search")]
        [ProducesResponseType(typeof(SearchResponseModel<SearchUserDataResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<SearchUserDataResponseModel>>> GetUsersAsync([Required] SearchDataRequestModel request)
        {
            try
            {
                var users = await this.userManagerService.Value.GetUsersAsync(request, this.GetUserRoles());
                return this.Ok(users);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPut]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        public async Task<ActionResult> EditUserAsync([Required, FromBody] EditUserRequest request)
        {
            try
            {
                await this.userManagerService.Value.UpdateUserAsync(request);
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPut("activate-users")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        public async Task<ActionResult> ActivateUsersAsync([Required] IEnumerable<string> selectedUsersRequest)
        {
            try
            {
                await this.userManagerService.Value.ActivateSelectedUsersAsync(selectedUsersRequest, this.GetUserId());
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPut("block-users")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        public async Task<ActionResult> BlockUsersAsync([Required] IEnumerable<string> selectedUsersRequest)
        {
            try
            {
                await this.userManagerService.Value.BlockSelectedUsersAsync(selectedUsersRequest, this.GetUserId());
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
