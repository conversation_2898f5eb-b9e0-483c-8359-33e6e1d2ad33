﻿namespace EtWS.Models.Requests
{
    public class SearchDataRequestModel
    {
        [Range(0, int.MaxValue, ErrorMessage = "Only positive number allowed for PageNumber")]
        public int PageNumber { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "Only positive number allowed for PageSize")]
        public int PageSize { get; set; }

        public string SortBy { get; set; }

        public string SortDir { get; set; }

        public string Query { get; set; }
    }
}
