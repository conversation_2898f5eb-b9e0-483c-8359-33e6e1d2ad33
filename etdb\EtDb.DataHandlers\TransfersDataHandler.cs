﻿namespace EtDb.DataHandlers
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Threading.Tasks;
    using ET.Database.Enums;
    using ET.DataHandlers.Models;
    using EtDb.ApiClients.EtDb.Context;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using EtDb.Infrastructure.Enumerations;
    using Microsoft.EntityFrameworkCore;

    public class TransfersDataHandler : BaseDataHandler, ITransfersDataHandler
    {
        public TransfersDataHandler(Lazy<EtDbContext> dbContext)
            : base(dbContext)
        {
        }

        public IQueryable<Transfers> GetAllTransfers() =>
            this.dbContext.Value
                .Transfers;

        public IQueryable<TransferListItems> GetAllTransferListItems()
        {
            IQueryable<TransferListItems> result = this.dbContext.Value
                .TransferListItems;

            return result;
        }

        public IDictionary<string, IEnumerable<TransferListItemWithDate>> GetAllTransferListItemsByOpFilteredBy(
            Expression<Func<Transfers, bool>> filter)
        {
            var result = this.GetAllTransfers()
                .Where(filter)
                .GroupBy(x => new
                {
                    OPCode = x.Schenker.Opcode,
                })
                .Select(g => new
                {
                    OPCode = g.Key.OPCode,
                    Items = g
                            .SelectMany(x => x.TransferListItems)
                            .Select(x => new TransferListItemWithDate
                            {
                                Quantity = x.Quantity,
                                EquipmentTypeSAPMaterialNumber = x.EquipmentType.SapmaterialNum,
                                EquipmentTypeId = x.EquipmentType.Id,
                                SendMethod = (SendMethod)x.EquipmentType.SendMethod,
                                DeliveryDate = x.Transfer.DeliveryDate,
                                Status = (TransferStatus)x.Transfer.Status
                            })
                })
                .ToDictionary(g => g.OPCode, g => g.Items);

            return result;
        }

        public int GetTransferIdByRequestNumber(string requestNumber) =>
            this.dbContext.Value
                .Transfers
                .Where(t => t.RequestNumber == requestNumber)
                .Select(t => t.Id)
                .SingleOrDefault();

        public int GetNumberOfTransfersByOp(string opCode) =>
            this.GetAllTransfers()
                .Count(t => t.Schenker.Opcode == opCode);

        public void AddTransfer(Transfers transfer)
        {
            this.dbContext.Value
                          .Transfers
                .Add(transfer);
        }

        public void AddTransferListItems(params TransferListItems[] listItems)
        {
            foreach (TransferListItems item in listItems)
            {
                this.dbContext.Value
                                  .TransferListItems
                    .Add(item);
            }
        }

        public async Task<FilteredDataModel<Transfers>> GetTransfersGridDataModel(string sortBy, string dir, int page, int pageDataSize, string query, IQueryable<Transfers> transfers)
        {
            FilteredDataModel<Transfers> gridData = await this.GetFilteredData(transfers, sortBy, dir, page, pageDataSize, query);

            return gridData;
        }

        public Transfers GetTransferById(int id)
        {
            Transfers? transfer = this.dbContext.Value
                .Transfers
                .FirstOrDefault(t => t.Id == id);

            return transfer ?? new Transfers();
        }

        public IQueryable<TransferListItems> GetTransferListItemsByTransferId(int transferId)
        {
            IQueryable<TransferListItems> transferListItems = this.dbContext.Value
                .TransferListItems
                .Include(li => li.EquipmentType)
                .Where(t => t.TransferId == transferId);

            return transferListItems;
        }

        public void DeleteTransferListItem(int id)
        {
            var item = this.dbContext.Value.TransferListItems.FirstOrDefault(t => t.Id == id);
            if (item != null)
            {
                this.dbContext.Value.TransferListItems.Remove(item);
            }
        }

        public TransferListItems GetTransferListItemById(int itemId) =>
            this.dbContext.Value
                .TransferListItems
                .FirstOrDefault(t => t.Id == itemId) ?? new TransferListItems();

        public void UpdateTransferListItem(TransferListItems listItem) =>
            this.dbContext.Value
                .TransferListItems
                .Update(listItem);

        public void UpdateTransfer(Transfers transfer)
        {
            this.dbContext.Value
                .Transfers
                .Update(transfer);
        }
    }
}
