﻿namespace EtDb.Api.Controllers
{
    using EtDb.Api.Infrastructure.Utils;
    using EtDb.Models.Responses;
    using EtDb.Models.Responses.EquipmentModels;
    using EtDb.Services.Interfaces;

    public class EquipmentGroupController : BaseApiController
    {
        private readonly Lazy<IEquipmentGroupService> equipmentGroupService;

        public EquipmentGroupController(Lazy<IEquipmentGroupService> equipmentGroupService)
        {
            this.equipmentGroupService = equipmentGroupService;
        }

        [HttpGet("all")]
        [ProducesResponseType(typeof(IEnumerable<EquipmentGroupResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<EquipmentGroupResponseModel>>> GetEquipmentGroups()
        {
            try
            {
                var result = await this.equipmentGroupService.Value.GetEquipmentGroupsAsync();

                if (result != null && result.Any())
                {
                    return this.Ok(result);
                }

                return this.NotFound(new BaseResponseModel { Success = false, Message = "No equipment groups found!" });
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("all/names")]
        [ProducesResponseType(typeof(IEnumerable<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<string>>> GetEquipmentGroupsNames()
        {
            try
            {
                var result = await this.equipmentGroupService.Value.GetEquipmentGroupsNamesAsync();

                if (result != null && result.Any())
                {
                    return this.Ok(result);
                }

                return this.NotFound(new BaseResponseModel { Success = false, Message = "No equipment groups found!" });
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("name/{sapMaterialNum}")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<string>> GetEquipmentGroupNameAsync(string sapMaterialNum)
        {
            try
            {
                var result = await this.equipmentGroupService.Value.GetEquipmentGroupNameAsync(sapMaterialNum);

                return this.Ok(result);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("by-id/{id}")]
        [ProducesResponseType(typeof(EquipmentGroupResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<EquipmentGroupResponseModel>> GetEquipmentGroupByIdAsync(int id)
        {
            try
            {
                var result = await this.equipmentGroupService.Value.GetEquipmentGroupByIdAsync(id);

                return this.Ok(result);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
