﻿namespace EtDb.Api.Infrastructure.Mapper
{
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.Models.AuxModels;
    using EtDb.Models.Requests;

    public class AuxProfile : Profile
    {
        public AuxProfile()
        {
            this.CreateMap<InsertItemRequestModel, Item>();

            this.CreateMap<InsertHistoryRequestModel, History>();

            this.CreateMap<InsertAvailableEquipmentRequestModel, AvailableEquipment>();

            this.CreateMap<UpdateAvailableEquipmentRequestModel, AvailableEquipment>();

            this.CreateMap<History, StatusHistoryAux>()
                .ForMember(x => x.HistoryId, input => input.MapFrom(i => i.Id))
                .ForMember(x => x.DocStatusOld, input => input.MapFrom(i => i.DocStatus))
                .ForMember(x => x.DocStatusNew, input => input.MapFrom(i => GlobalConstants.DocStatusProvidedToClient))
                .ForMember(x => x.SourceSystem, input => input.MapFrom(i => GlobalConstants.SourceSystemSGW))
                .ForMember(x => x.DateInsert, input => input.MapFrom(i => DateTime.UtcNow));

            this.CreateMap<StatusHistoryAux, StatusHistory>();

            this.CreateMap<InsertStatusHistoryRequestModel, StatusHistory>()
                .ForMember(x => x.DocStatusOld, input => input.MapFrom(i => 0))
                .ForMember(x => x.UserId, input => input.MapFrom(i => i.ElnId))
                .ForMember(x => x.DateInsert, input => input.MapFrom(i => DateTime.UtcNow));
        }
    }
}
