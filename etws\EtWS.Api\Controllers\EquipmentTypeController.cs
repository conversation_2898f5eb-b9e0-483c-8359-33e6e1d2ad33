﻿namespace EtWS.Api.Controllers
{
    using EtWS.Api.Infrastructure.Utils;
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Services.EquipmentTypeService;

    public class EquipmentTypeController : BaseApiController
    {
        private readonly Lazy<IEquipmentTypeService> equipmentTypeService;

        public EquipmentTypeController(Lazy<IEquipmentTypeService> equipmentTypeService)
        {
            this.equipmentTypeService = equipmentTypeService;
        }

        [HttpGet("all")]
        [ProducesResponseType(typeof(IEnumerable<EquipmentTypeResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<EquipmentTypeResponseModel>>> GetEquipmentTypes()
        {
            try
            {
                return this.Ok(await this.equipmentTypeService.Value.GetEquipmentTypesAsync());
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("by-id/{id}")]
        [ProducesResponseType(typeof(EquipmentTypeResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<EquipmentTypeResponseModel>> GetEquipmentTypeByIdAsync(int id)
        {
            try
            {
                return await this.equipmentTypeService.Value.GetEquipmentTypeByIdAsync(id);
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("type-id-by-sap-material-num/{sapMaterialNum}")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> GetEquipmentTypeIdAsync(string sapMaterialNum)
        {
            try
            {
                return this.Ok(await this.equipmentTypeService.Value.GetEquipmentTypeIdAsync(sapMaterialNum));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("by-send-method/{sendMethod}")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> GetEquipmentTypesAsync(string sendMethod)
        {
            try
            {
                return this.Ok(await this.equipmentTypeService.Value.GetEquipmentTypesAsync(sendMethod));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("concise-equipment-types")]
        [ProducesResponseType(typeof(IEnumerable<EquipmentTypeConciseDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<EquipmentTypeConciseDto>>> GetConciseEquipmentTypes([Required] ConciseEquipmentTypesDataRequestModel request)
        {
            try
            {
                return this.Ok(await this.equipmentTypeService.Value.GetConciseEquipmentTypesAsync(request));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("last-month-quantities")]
        [ProducesResponseType(typeof(IDictionary<string, int>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IDictionary<string, int>>> GetEquipmentTypesLastMonthQuantitiesAsync()
        {
            try
            {
                return this.Ok(await this.equipmentTypeService.Value.GetEquipmentTypesLastMonthQuantitiesAsync());
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
